# Website Monetization Design Document

## Overview

The website monetization system will implement a multi-tier access model that provides users with flexible options to access premium features. The system integrates with DodoPayments for subscription processing, LinkShortify for ad-supported temporary access, and includes a referral program for user acquisition. The design ensures seamless migration for existing free users while establishing sustainable revenue streams.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Interface] --> B[Access Control Layer]
    B --> C[Monetization Service]
    C --> D[DodoPayments Integration]
    C --> E[LinkShortify Integration]
    C --> F[Referral System]
    C --> G[Access Management]
    G --> H[Supabase Database]
    I[Analytics Service] --> H
    J[Notification Service] --> H
```

### System Components

1. **Access Control Layer**: Middleware that validates user access permissions
2. **Monetization Service**: Core service managing all monetization logic
3. **Payment Integration**: DodoPayments default payment screen for subscription management
4. **Ad-Gate Integration**: LinkShortify integration for temporary access
5. **Referral System**: Tracking and reward management for user referrals
6. **Analytics Service**: Revenue and usage analytics
7. **Notification Service**: Email and in-app notifications

## Components and Interfaces

### 1. Access Control System

#### AccessControlProvider
```typescript
interface AccessControlProvider {
  checkAccess(userId: string, feature: string): Promise<AccessResult>
  validateSubscription(userId: string): Promise<SubscriptionStatus>
  grantTemporaryAccess(userId: string, duration: number): Promise<void>
  revokeAccess(userId: string): Promise<void>
}

interface AccessResult {
  hasAccess: boolean
  accessType: 'premium' | 'temporary' | 'referral' | 'none'
  expiresAt?: Date
  remainingTime?: number
}
```

#### ProtectedRoute Enhancement
```typescript
interface EnhancedProtectedRoute {
  requiredAccess: 'free' | 'premium' | 'any'
  fallbackComponent?: React.Component
  redirectTo?: string
}
```

### 2. DodoPayments Integration

#### PaymentService
```typescript
interface PaymentService {
  createSubscription(userId: string, plan: PricingPlan): Promise<PaymentSession>
  verifyPayment(sessionId: string): Promise<PaymentResult>
  cancelSubscription(subscriptionId: string): Promise<void>
  getSubscriptionStatus(userId: string): Promise<SubscriptionStatus>
  handleWebhook(payload: any): Promise<void>
  redirectToDodoPayments(userId: string, plan: PricingPlan): Promise<string>
}

interface PricingPlan {
  id: string
  name: string
  price: number
  currency: 'INR' | 'USD'
  duration: number // in days
  features: string[]
}

interface PaymentSession {
  sessionId: string
  redirectUrl: string // DodoPayments hosted payment page
  expiresAt: Date
}
```

#### DodoPayments Configuration
```typescript
interface DodoConfig {
  apiKey: string
  webhookSecret: string
  baseUrl: string
  hostedPaymentUrl: string // DodoPayments hosted interface
  returnUrl: string // Success redirect URL
  cancelUrl: string // Cancel redirect URL
  plans: {
    india: PricingPlan
    international: PricingPlan
  }
}
```

#### DodoPayments Integration Notes
- **Use DodoPayments Hosted Interface**: Instead of building custom payment forms, redirect users to DodoPayments' secure hosted payment interface
- **Payment Flow**: User clicks subscribe → System creates payment session → Redirect to DodoPayments interface → User completes payment → Redirect back to app with success/failure
- **Security Benefits**: PCI compliance handled by DodoPayments, no sensitive payment data on our servers
- **User Experience**: Professional payment interface with multiple payment methods supported by DodoPayments

### 3. LinkShortify Integration

#### AdGateService
```typescript
interface AdGateService {
  generateAdGateUrl(userId: string): Promise<string>
  verifyCompletion(token: string): Promise<AdGateResult>
  trackProgress(userId: string, step: number): Promise<void>
}

interface AdGateResult {
  success: boolean
  userId: string
  completedAt: Date
  accessDuration: number // 24 hours in milliseconds
}
```

#### LinkShortify Configuration
```typescript
interface LinkShortifyConfig {
  apiKey: string
  campaignId: string
  redirectUrl: string
  requiredSteps: number // 4-5 ad websites
  rewardDuration: number // 24 hours
}
```

### 4. Referral System

#### ReferralService
```typescript
interface ReferralService {
  generateReferralCode(userId: string): Promise<string>
  trackReferral(referralCode: string, newUserId: string): Promise<void>
  processReferralReward(referrerId: string): Promise<void>
  getReferralStats(userId: string): Promise<ReferralStats>
}

interface ReferralStats {
  totalReferrals: number
  paidReferrals: number
  pendingRewards: number
  earnedRewards: number
  referralCode: string
}
```

### 5. User Interface Components

#### MonetizationModal
```typescript
interface MonetizationModalProps {
  isOpen: boolean
  onClose: () => void
  currentUser: User
  onAccessGranted: (accessType: string) => void
}
```

#### SubscriptionDashboard
```typescript
interface SubscriptionDashboardProps {
  user: User
  subscription: SubscriptionStatus
  onManageSubscription: () => void
  onCancelSubscription: () => void
}
```

#### AccessStatusIndicator
```typescript
interface AccessStatusProps {
  accessType: 'premium' | 'temporary' | 'referral' | 'none'
  expiresAt?: Date
  showUpgradePrompt?: boolean
}
```

## Data Models

### Database Schema Extensions

#### Subscriptions Table
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  plan_id VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL, -- active, cancelled, expired, pending
  payment_provider VARCHAR(20) DEFAULT 'dodopayments',
  external_subscription_id VARCHAR(255),
  starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  auto_renew BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Temporary Access Table
```sql
CREATE TABLE temporary_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  access_type VARCHAR(20) NOT NULL, -- adgate, referral
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  source_data JSONB, -- LinkShortify token, referral info, etc.
  is_active BOOLEAN DEFAULT true
);
```

#### Referrals Table
```sql
CREATE TABLE referrals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id UUID REFERENCES users(id) ON DELETE CASCADE,
  referred_id UUID REFERENCES users(id) ON DELETE CASCADE,
  referral_code VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL, -- pending, paid, rewarded
  referred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  paid_at TIMESTAMP WITH TIME ZONE,
  reward_granted_at TIMESTAMP WITH TIME ZONE
);
```

#### Payment Transactions Table
```sql
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id),
  external_transaction_id VARCHAR(255),
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) NOT NULL,
  status VARCHAR(20) NOT NULL, -- pending, completed, failed, refunded
  payment_method VARCHAR(50),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### User Profile Extensions
```sql
-- Add columns to existing users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS 
  access_type VARCHAR(20) DEFAULT 'free', -- free, premium, temporary
  subscription_expires_at TIMESTAMP WITH TIME ZONE,
  referral_code VARCHAR(50) UNIQUE,
  country_code VARCHAR(2), -- For pricing determination
  timezone VARCHAR(50);
```

## Error Handling

### Payment Error Handling
```typescript
enum PaymentErrorType {
  PAYMENT_FAILED = 'payment_failed',
  SUBSCRIPTION_EXPIRED = 'subscription_expired',
  INVALID_PLAN = 'invalid_plan',
  WEBHOOK_VERIFICATION_FAILED = 'webhook_verification_failed',
  REFUND_FAILED = 'refund_failed'
}

interface PaymentError {
  type: PaymentErrorType
  message: string
  code?: string
  retryable: boolean
  userMessage: string
}
```

### Access Control Error Handling
```typescript
enum AccessErrorType {
  ACCESS_DENIED = 'access_denied',
  SUBSCRIPTION_REQUIRED = 'subscription_required',
  TEMPORARY_ACCESS_EXPIRED = 'temporary_access_expired',
  INVALID_REFERRAL = 'invalid_referral'
}

interface AccessError {
  type: AccessErrorType
  message: string
  redirectTo?: string
  showUpgradeModal?: boolean
}
```

### LinkShortify Error Handling
```typescript
enum AdGateErrorType {
  VERIFICATION_FAILED = 'verification_failed',
  INCOMPLETE_STEPS = 'incomplete_steps',
  EXPIRED_TOKEN = 'expired_token',
  RATE_LIMITED = 'rate_limited'
}
```

## Testing Strategy

### Unit Testing
1. **Payment Service Tests**
   - DodoPayments API integration
   - Webhook processing
   - Subscription lifecycle management
   - Error handling scenarios

2. **Access Control Tests**
   - Permission validation logic
   - Subscription status checking
   - Temporary access management
   - Route protection

3. **Referral System Tests**
   - Code generation and validation
   - Reward calculation and distribution
   - Tracking accuracy

### Integration Testing
1. **End-to-End Payment Flow**
   - Complete subscription purchase
   - Payment verification
   - Access activation
   - Subscription renewal

2. **LinkShortify Integration**
   - Ad gate completion flow
   - Token verification
   - Access granting

3. **User Migration Testing**
   - Existing user transition
   - Data preservation
   - Access continuity

### Performance Testing
1. **Database Performance**
   - Query optimization for access checks
   - Subscription status lookups
   - Analytics queries

2. **API Response Times**
   - Payment processing speed
   - Access validation latency
   - Real-time updates

## Security Considerations

### Payment Security
- PCI DSS compliance through DodoPayments
- Webhook signature verification
- Secure API key management
- Transaction logging and monitoring

### Access Control Security
- JWT token validation
- Session management
- Rate limiting for access attempts
- Audit logging for access changes

### Data Protection
- Encryption of sensitive payment data
- GDPR compliance for user data
- Secure storage of referral codes
- Regular security audits

## Migration Strategy

### Phase 1: Infrastructure Setup
1. Database schema creation
2. DodoPayments integration setup
3. LinkShortify API integration
4. Basic UI components

### Phase 2: Core Functionality
1. Subscription management system
2. Access control implementation
3. Payment processing
4. User interface integration

### Phase 3: Advanced Features
1. Referral system implementation
2. Analytics and reporting
3. Advanced UI features
4. Performance optimization

### Phase 4: User Migration
1. Existing user notification system
2. Gradual rollout with feature flags
3. Support and documentation
4. Monitoring and feedback collection

## Analytics and Monitoring

### Key Metrics
- Conversion rates by access method
- Revenue per user (RPU)
- Churn rate and retention
- Referral program effectiveness
- LinkShortify completion rates

### Monitoring Setup
- Real-time payment processing alerts
- Subscription expiration notifications
- Access control failure tracking
- Performance monitoring dashboards

### Reporting Dashboard
- Revenue analytics
- User acquisition metrics
- Feature usage statistics
- Geographic distribution analysis

## Future Considerations

### In-Site Advertising Framework
- Ad placement infrastructure
- Ad network integration capabilities
- Revenue optimization algorithms
- User experience balance

### Advanced Features
- Multiple subscription tiers
- Family/team plans
- Corporate subscriptions
- Loyalty programs

### Scalability Considerations
- Microservices architecture migration
- Caching strategies for access control
- Database sharding for large user base
- CDN integration for global performance

{UPDATE ACCORDING TO SUPABASE DATABASE}