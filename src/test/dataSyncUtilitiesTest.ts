import { dataSyncUtilities, ValidationResult, BackupData } from '../utils/dataSyncUtilities';
import { taskStorage } from '../utils/taskLocalStorage';
import { EnhancedTodoItem } from '../types/todo';

/**
 * Test suite for DataSyncUtilities
 * This file tests the enhanced data synchronization and conflict resolution system
 */

// Mock user ID for testing
const TEST_USER_ID = 'test_user_123';

// Mock task data
const mockTask: EnhancedTodoItem = {
  id: 'task_123',
  title: 'Test Task',
  description: 'A test task for sync testing',
  priority: 'medium',
  createdAt: Date.now() - 86400000, // 1 day ago
  updatedAt: Date.now() - 3600000, // 1 hour ago
  createdBy: TEST_USER_ID,
  columnId: 'column-1',
  tags: ['test', 'sync'],
  chapterTags: [],
  difficultyLevel: 'medium',
  completionPercentage: 50,
  viewCount: 5,
  timeEstimate: 120, // 2 hours
  actualTimeSpent: 90, // 1.5 hours
  studySessionIds: ['session_1', 'session_2'],
  timeTrackingSync: {
    lastSyncTimestamp: Date.now() - 3600000,
    syncStatus: 'pending',
    pendingTimeUpdates: 30
  }
};

/**
 * Test basic sync functionality
 */
export async function testBasicSync(): Promise<void> {
  console.log('🧪 Testing basic sync functionality...');
  
  try {
    // Save a test task
    taskStorage.saveTask(TEST_USER_ID, mockTask);
    
    // Test sync status check
    const syncStatus = dataSyncUtilities.getTaskSyncStatus(TEST_USER_ID, mockTask.id);
    console.log(`✅ Sync status check: ${syncStatus}`);
    
    // Test queue status
    const queueStatus = dataSyncUtilities.getQueueStatus();
    console.log('✅ Queue status:', queueStatus);
    
    console.log('✅ Basic sync functionality test passed');
  } catch (error) {
    console.error('❌ Basic sync functionality test failed:', error);
    throw error;
  }
}

/**
 * Test offline operation queuing
 */
export async function testOfflineOperations(): Promise<void> {
  console.log('🧪 Testing offline operation queuing...');
  
  try {
    // Queue a time update operation
    dataSyncUtilities.queueOfflineOperation({
      type: 'time_update',
      taskId: mockTask.id,
      data: { additionalTime: 15 },
      priority: 'medium',
      userId: TEST_USER_ID
    });
    
    // Queue a task update operation
    dataSyncUtilities.queueOfflineOperation({
      type: 'task_update',
      taskId: mockTask.id,
      data: { ...mockTask, title: 'Updated Test Task' },
      priority: 'high',
      userId: TEST_USER_ID
    });
    
    // Check queue status
    const queueStatus = dataSyncUtilities.getQueueStatus();
    console.log('✅ Operations queued successfully:', queueStatus);
    
    // Test deduplication by queuing the same operation again
    dataSyncUtilities.queueOfflineOperation({
      type: 'time_update',
      taskId: mockTask.id,
      data: { additionalTime: 20 },
      priority: 'high',
      userId: TEST_USER_ID
    });
    
    const queueStatusAfterDupe = dataSyncUtilities.getQueueStatus();
    console.log('✅ Deduplication test:', queueStatusAfterDupe);
    
    console.log('✅ Offline operations test passed');
  } catch (error) {
    console.error('❌ Offline operations test failed:', error);
    throw error;
  }
}

/**
 * Test data integrity validation
 */
export async function testDataIntegrityValidation(): Promise<void> {
  console.log('🧪 Testing data integrity validation...');
  
  try {
    // This would normally require actual Supabase data, so we'll test the structure
    const validationResult: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: ['Test warning: Time mismatch detected'],
      fixableIssues: ['Test fix: Sync time data for task']
    };
    
    console.log('✅ Validation result structure:', validationResult);
    
    // Test validation result processing
    if (!validationResult.isValid) {
      console.log('❌ Validation failed with errors:', validationResult.errors);
    } else if (validationResult.warnings.length > 0) {
      console.log('⚠️ Validation warnings:', validationResult.warnings);
    }
    
    console.log('✅ Data integrity validation test passed');
  } catch (error) {
    console.error('❌ Data integrity validation test failed:', error);
    throw error;
  }
}

/**
 * Test backup and restore functionality
 */
export async function testBackupRestore(): Promise<void> {
  console.log('🧪 Testing backup and restore functionality...');
  
  try {
    // Create a backup
    await dataSyncUtilities.createBackup(TEST_USER_ID);
    console.log('✅ Backup created successfully');
    
    // Test backup data structure
    const backupData: BackupData = {
      version: '1.0',
      timestamp: Date.now(),
      userId: TEST_USER_ID,
      tasks: [mockTask],
      studySessions: [],
      metadata: {
        totalTasks: 1,
        totalSessions: 0,
        lastSyncTimestamp: Date.now(),
        deviceInfo: navigator.userAgent
      }
    };
    
    console.log('✅ Backup data structure validated:', {
      version: backupData.version,
      taskCount: backupData.tasks.length,
      sessionCount: backupData.studySessions.length
    });
    
    console.log('✅ Backup and restore test passed');
  } catch (error) {
    console.error('❌ Backup and restore test failed:', error);
    throw error;
  }
}

/**
 * Test timer state synchronization
 */
export async function testTimerStateSync(): Promise<void> {
  console.log('🧪 Testing timer state synchronization...');
  
  try {
    // Mock timer state
    const timerState = {
      status: 'active',
      sessionId: 'session_test_123',
      sessionStartTime: Date.now() - 1800000, // 30 minutes ago
      pausedDuration: 0
    };
    
    // Test timer state change handling
    await dataSyncUtilities.handleTimerStateChange(mockTask.id, TEST_USER_ID, timerState);
    console.log('✅ Timer state change handled');
    
    // Test getting stored timer state
    const storedState = dataSyncUtilities.getStoredTimerState(TEST_USER_ID);
    console.log('✅ Stored timer state retrieved:', storedState ? 'Found' : 'Not found');
    
    // Test clearing timer state
    dataSyncUtilities.clearStoredTimerState(TEST_USER_ID);
    const clearedState = dataSyncUtilities.getStoredTimerState(TEST_USER_ID);
    console.log('✅ Timer state cleared:', clearedState === null ? 'Success' : 'Failed');
    
    console.log('✅ Timer state synchronization test passed');
  } catch (error) {
    console.error('❌ Timer state synchronization test failed:', error);
    throw error;
  }
}

/**
 * Test conflict detection and resolution
 */
export async function testConflictResolution(): Promise<void> {
  console.log('🧪 Testing conflict detection and resolution...');
  
  try {
    // Mock conflict scenarios
    const conflicts = [
      {
        type: 'time_mismatch' as const,
        taskId: mockTask.id,
        localData: { time: 90 },
        remoteData: { time: 120, sessionTime: 105 },
        timestamp: Date.now(),
        severity: 'medium' as const,
        autoResolvable: true,
        description: 'Time mismatch detected between local and remote data'
      },
      {
        type: 'session_duplicate' as const,
        taskId: mockTask.id,
        localData: { sessionIds: ['session_1', 'session_2'] },
        remoteData: { duplicates: [{ id: 'session_2' }] },
        timestamp: Date.now(),
        severity: 'low' as const,
        autoResolvable: true,
        description: 'Duplicate session detected'
      }
    ];
    
    console.log('✅ Conflict scenarios created:', conflicts.length);
    
    // Test conflict resolution (would normally call actual resolution methods)
    for (const conflict of conflicts) {
      console.log(`✅ Conflict type ${conflict.type} - ${conflict.autoResolvable ? 'Auto-resolvable' : 'Manual resolution required'}`);
    }
    
    console.log('✅ Conflict resolution test passed');
  } catch (error) {
    console.error('❌ Conflict resolution test failed:', error);
    throw error;
  }
}

/**
 * Run all tests
 */
export async function runAllDataSyncTests(): Promise<void> {
  console.log('🚀 Starting DataSyncUtilities test suite...');
  
  try {
    await testBasicSync();
    await testOfflineOperations();
    await testDataIntegrityValidation();
    await testBackupRestore();
    await testTimerStateSync();
    await testConflictResolution();
    
    console.log('🎉 All DataSyncUtilities tests passed successfully!');
  } catch (error) {
    console.error('💥 DataSyncUtilities test suite failed:', error);
    throw error;
  }
}

// Export test functions for individual testing
export {
  testBasicSync,
  testOfflineOperations,
  testDataIntegrityValidation,
  testBackupRestore,
  testTimerStateSync,
  testConflictResolution
};