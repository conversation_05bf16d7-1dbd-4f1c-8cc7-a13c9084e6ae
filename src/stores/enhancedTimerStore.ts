import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { EnhancedTimerState, TimerTaskContext } from '../services/TaskTimerIntegrationService';
import { taskTimerIntegration } from '../services/TaskTimerIntegrationService';
import { dataSyncUtilities } from '../utils/dataSyncUtilities';
import { smartNotificationService } from '../services/SmartNotificationService';

// Timer settings interface
export interface TimerSettings {
  workDuration: number; // seconds
  shortBreakDuration: number; // seconds
  longBreakDuration: number; // seconds
  sessionsUntilLongBreak: number;
  notificationInterval: number; // minutes
  dayResetHour: number; // Hour (0-23) when the day resets for logging
}

// Default timer settings
const DEFAULT_SETTINGS: TimerSettings = {
  workDuration: 25 * 60, // 25 minutes
  shortBreakDuration: 5 * 60, // 5 minutes
  longBreakDuration: 15 * 60, // 15 minutes
  sessionsUntilLongBreak: 4,
  notificationInterval: 60, // 60 minutes
  dayResetHour: 4, // 4 AM
};

// Enhanced timer store state
interface EnhancedTimerStoreState extends EnhancedTimerState {
  // Settings
  settings: TimerSettings;

  // UI state
  isSettingsOpen: boolean;
  isTaskSelectorOpen: boolean;

  // Advanced features state
  isTimeBlockingActive: boolean;
  isDistractionBlockingActive: boolean;
  currentBreakSuggestion: string | null;
  activeRecurringTemplate: string | null;

  // Session management
  currentPhase: 'work' | 'shortBreak' | 'longBreak';
  completedSessions: number;

  // Internal state for preventing rapid updates
  _lastUpdate: number;
  
  // Actions
  startTimer: (taskId?: string, userId?: string, startTime?: Date) => Promise<void>;
  pauseTimer: () => Promise<void>;
  stopTimer: (feedback?: any) => Promise<void>;
  resetTimer: () => void;
  
  // Task integration
  linkTaskToTimer: (taskId: string, userId: string) => Promise<void>;
  unlinkTaskFromTimer: () => void;
  
  // Settings management
  updateSettings: (newSettings: Partial<TimerSettings>) => void;
  resetSettings: () => void;
  
  // UI actions
  setSettingsOpen: (open: boolean) => void;
  setTaskSelectorOpen: (open: boolean) => void;
  
  // Advanced features actions
  setTimeBlockingActive: (active: boolean) => void;
  setDistractionBlockingActive: (active: boolean) => void;
  setCurrentBreakSuggestion: (suggestion: string | null) => void;
  setActiveRecurringTemplate: (template: string | null) => void;
  
  // Phase management
  completePhase: () => Promise<void>;
  skipPhase: () => void;
  
  // Sync actions
  syncWithTask: (taskId: string, userId: string) => Promise<void>;
}

// Initial state
const initialState: Omit<EnhancedTimerStoreState, keyof ReturnType<typeof createActions>> = {
  status: 'idle',
  mode: 'stopwatch',
  displayTime: 0,
  sessionStartTime: new Date(),
  pausedDuration: 0,
  breaksSuggested: 0,
  settings: DEFAULT_SETTINGS,
  isSettingsOpen: false,
  isTaskSelectorOpen: false,
  currentPhase: 'work',
  completedSessions: 0,
  isTimeBlockingActive: false,
  isDistractionBlockingActive: false,
  currentBreakSuggestion: null,
  activeRecurringTemplate: null,
  _lastUpdate: 0,
};

// Create actions separately to avoid circular references
const createActions = (set: any, get: any) => ({
  startTimer: async (taskId?: string, userId?: string, startTime?: Date) => {
    try {
      const state = get();

      // Prevent rapid calls that could cause infinite loops
      const currentTime = Date.now();
      if (currentTime - state._lastUpdate < 100) {
        console.warn('Timer action called too rapidly, ignoring');
        return;
      }

      if (state.status === 'running') {
        console.warn('Timer is already running');
        return;
      }

      // If taskId provided, link task to timer
      if (taskId && userId) {
        await get().linkTaskToTimer(taskId, userId);
      }

      const now = startTime || new Date();
      const newState = {
        status: 'running' as const,
        sessionStartTime: now,
        pausedDuration: 0,
        displayTime: state.mode === 'pomodoro' ? state.settings.workDuration : 0,
        _lastUpdate: Date.now(),
      };

      set(newState);

      // Start timer session if task is linked
      if (state.linkedTask && userId) {
        try {
          const session = await taskTimerIntegration.startTimerForTask(state.linkedTask.taskId, userId);
          set({ sessionId: session.id });
        } catch (error) {
          console.error('Error starting timer session:', error);
          // Continue with timer even if session creation fails
        }
      }

      // Notify sync utilities of state change
      if (state.linkedTask?.taskId && userId) {
        await dataSyncUtilities.handleTimerStateChange(
          state.linkedTask.taskId,
          userId,
          { ...state, ...newState, status: 'running' }
        );
      }

      // Schedule break suggestions for the new session
      if (userId) {
        smartNotificationService.scheduleBreakSuggestions(userId);
      }

    } catch (error) {
      console.error('Error starting timer:', error);
      throw error;
    }
  },

  pauseTimer: async () => {
    try {
      const state = get();

      // Prevent rapid calls
      const currentTime = Date.now();
      if (currentTime - state._lastUpdate < 100) {
        console.warn('Timer action called too rapidly, ignoring');
        return;
      }

      if (state.status !== 'running') {
        console.warn('Timer is not running');
        return;
      }

      const now = new Date();
      const sessionDuration = now.getTime() - state.sessionStartTime.getTime();
      
      const newState = {
        status: 'paused' as const,
        pausedDuration: state.pausedDuration + sessionDuration,
        _lastUpdate: Date.now(),
      };

      set(newState);

      // Pause timer session if task is linked
      if (state.linkedTask) {
        try {
          await taskTimerIntegration.pauseTimerForTask(state.linkedTask.taskId);
        } catch (error) {
          console.error('Error pausing timer session:', error);
        }
      }

    } catch (error) {
      console.error('Error pausing timer:', error);
      throw error;
    }
  },

  stopTimer: async (feedback?: any) => {
    try {
      const state = get();

      // Prevent rapid calls
      const currentTime = Date.now();
      if (currentTime - state._lastUpdate < 100) {
        console.warn('Timer action called too rapidly, ignoring');
        return;
      }

      if (state.status === 'idle') {
        console.warn('Timer is not running');
        return;
      }

      const now = new Date();
      let totalDuration = state.pausedDuration;
      
      if (state.status === 'running') {
        totalDuration += now.getTime() - state.sessionStartTime.getTime();
      }

      // Stop timer session if task is linked
      if (state.linkedTask) {
        try {
          // Get user ID from the calling context (will be passed as parameter)
          const userId = (feedback as any)?.userId;
          if (userId) {
            await taskTimerIntegration.stopTimerForTask(state.linkedTask.taskId, userId, feedback);
            
            // Track productivity streak and run analysis
            smartNotificationService.trackProductivityStreak(userId);
            smartNotificationService.runAnalysis(userId);
          }
        } catch (error) {
          console.error('Error stopping timer session:', error);
        }
      }

      // Reset timer state
      set({
        status: 'idle' as const,
        displayTime: state.mode === 'pomodoro' ? state.settings.workDuration : 0,
        sessionStartTime: new Date(),
        pausedDuration: 0,
        linkedTask: undefined,
        sessionId: undefined,
        estimatedEndTime: undefined,
        productivityScore: undefined,
        _lastUpdate: Date.now(),
      });

    } catch (error) {
      console.error('Error stopping timer:', error);
      throw error;
    }
  },

  resetTimer: () => {
    const state = get();
    set({
      status: 'idle' as const,
      displayTime: state.mode === 'pomodoro' ? state.settings.workDuration : 0,
      sessionStartTime: new Date(),
      pausedDuration: 0,
      breaksSuggested: 0,
      linkedTask: undefined,
      sessionId: undefined,
      estimatedEndTime: undefined,
      productivityScore: undefined,
      _lastUpdate: Date.now(),
    });
  },

  linkTaskToTimer: async (taskId: string, userId: string) => {
    try {
      const state = get();
      
      // Prevent linking the same task multiple times
      if (state.linkedTask?.taskId === taskId) {
        console.log('Task already linked to timer:', taskId);
        return;
      }

      // Get task details from localStorage via the integration service
      const timeData = await taskTimerIntegration.getTaskTimeTracking(taskId, userId);
      
      // Get task details from localStorage to populate context
      const { taskStorage } = await import('../utils/taskLocalStorage');
      const task = taskStorage.getTask(userId, taskId);
      
      // Create timer task context
      const taskContext: TimerTaskContext = {
        taskId,
        taskTitle: task?.title || 'Unknown Task',
        taskDescription: task?.description,
        subjectId: task?.subjectId,
        subjectName: task?.subjectName,
        subjectColor: task?.subjectColor,
        priority: task?.priority || 'medium',
        actualTimeSpent: Math.round((timeData.totalTimeSpent || 0) / 60), // Convert to minutes
        completionPercentage: task?.completionPercentage || 0,
        estimatedTime: timeData.estimatedTime ? Math.round(timeData.estimatedTime / 60) : undefined,
        dueDate: task?.dueDate,
      };

      set({
        linkedTask: taskContext,
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      });

      console.log('Task linked to timer:', taskContext);

    } catch (error) {
      console.error('Error linking task to timer:', error);
      throw error;
    }
  },

  unlinkTaskFromTimer: () => {
    set({
      linkedTask: undefined,
      sessionId: undefined,
      estimatedEndTime: undefined,
    });
  },

  updateSettings: (newSettings: Partial<TimerSettings>) => {
    const state = get();
    const updatedSettings = { ...state.settings, ...newSettings };
    
    set({ settings: updatedSettings });
    
    // Update display time if timer is idle and in pomodoro mode
    if (state.status === 'idle' && state.mode === 'pomodoro') {
      let newDisplayTime = updatedSettings.workDuration;
      
      if (state.currentPhase === 'shortBreak') {
        newDisplayTime = updatedSettings.shortBreakDuration;
      } else if (state.currentPhase === 'longBreak') {
        newDisplayTime = updatedSettings.longBreakDuration;
      }
      
      set({ displayTime: newDisplayTime });
    }
  },

  resetSettings: () => {
    set({ settings: DEFAULT_SETTINGS });
  },

  setSettingsOpen: (open: boolean) => {
    set({ isSettingsOpen: open });
  },

  setTaskSelectorOpen: (open: boolean) => {
    set({ isTaskSelectorOpen: open });
  },

  setTimeBlockingActive: (active: boolean) => {
    set({ isTimeBlockingActive: active });
  },

  setDistractionBlockingActive: (active: boolean) => {
    set({ isDistractionBlockingActive: active });
  },

  setCurrentBreakSuggestion: (suggestion: string | null) => {
    set({ currentBreakSuggestion: suggestion });
  },

  setActiveRecurringTemplate: (template: string | null) => {
    set({ activeRecurringTemplate: template });
  },

  completePhase: async () => {
    const state = get();
    
    if (state.mode !== 'pomodoro') return;

    let nextPhase: 'work' | 'shortBreak' | 'longBreak';
    let nextDuration: number;
    let newCompletedSessions = state.completedSessions;

    if (state.currentPhase === 'work') {
      newCompletedSessions += 1;
      
      if (newCompletedSessions % state.settings.sessionsUntilLongBreak === 0) {
        nextPhase = 'longBreak';
        nextDuration = state.settings.longBreakDuration;
      } else {
        nextPhase = 'shortBreak';
        nextDuration = state.settings.shortBreakDuration;
      }
    } else {
      nextPhase = 'work';
      nextDuration = state.settings.workDuration;
    }

    set({
      currentPhase: nextPhase,
      completedSessions: newCompletedSessions,
      displayTime: nextDuration,
      sessionStartTime: new Date(),
      pausedDuration: 0,
      status: 'running' as const,
    });
  },

  skipPhase: () => {
    const state = get();
    get().completePhase();
  },

  syncWithTask: async (taskId: string, userId: string) => {
    try {
      await dataSyncUtilities.syncTaskWithTimer(taskId, userId);
      
      // Update linked task context with latest data
      const state = get();
      if (state.linkedTask?.taskId === taskId) {
        const timeData = await taskTimerIntegration.getTaskTimeTracking(taskId, userId);
        
        set({
          linkedTask: {
            ...state.linkedTask,
            actualTimeSpent: Math.round((timeData.totalTimeSpent || 0) / 60),
            estimatedTime: timeData.estimatedTime ? Math.round(timeData.estimatedTime / 60) : undefined,
          }
        });
      }
    } catch (error) {
      console.error('Error syncing with task:', error);
      throw error;
    }
  },
});

// Create the store
export const useEnhancedTimerStore = create<EnhancedTimerStoreState>()(
  persist(
    (set, get) => ({
      ...initialState,
      ...createActions(set, get),
    }),
    {
      name: 'enhanced-timer-storage',
      partialize: (state) => ({
        // Only persist settings and basic state, not active session data
        settings: state.settings,
        mode: state.mode,
        currentPhase: state.currentPhase,
        completedSessions: state.completedSessions,
      }),
    }
  )
);

// Helper hooks for specific timer functionality
export const useTimerStatus = () => useEnhancedTimerStore(state => state.status);
export const useTimerDisplayTime = () => useEnhancedTimerStore(state => state.displayTime);
export const useTimerLinkedTask = () => useEnhancedTimerStore(state => state.linkedTask);

// Action hooks
export const useTimerActions = () => useEnhancedTimerStore(state => ({
  startTimer: state.startTimer,
  pauseTimer: state.pauseTimer,
  stopTimer: state.stopTimer,
  resetTimer: state.resetTimer,
  linkTaskToTimer: state.linkTaskToTimer,
  unlinkTaskFromTimer: state.unlinkTaskFromTimer,
}));

export const useTimerSettings = () => useEnhancedTimerStore(state => ({
  settings: state.settings,
  updateSettings: state.updateSettings,
  resetSettings: state.resetSettings,
  isSettingsOpen: state.isSettingsOpen,
  setSettingsOpen: state.setSettingsOpen,
}));

export const useTaskSelector = () => useEnhancedTimerStore(state => ({
  isTaskSelectorOpen: state.isTaskSelectorOpen,
  setTaskSelectorOpen: state.setTaskSelectorOpen,
  linkedTask: state.linkedTask,
  linkTaskToTimer: state.linkTaskToTimer,
  unlinkTaskFromTimer: state.unlinkTaskFromTimer,
}));
