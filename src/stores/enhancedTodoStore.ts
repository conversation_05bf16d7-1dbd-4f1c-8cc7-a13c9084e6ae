import { create } from 'zustand';

import { taskStorage } from '../utils/taskLocalStorage';
import {
  EnhancedTodoState,
  EnhancedTodoItem,
  EnhancedTodoBoard,
  TodoColumn,
  Subject,
  PresetExamType,
  FilterState,
  SearchState,
  AnalyticsData,
  LoadingState,
  ErrorState,
  BulkOperation,
  EnhancedDragItem,
} from '../types/todo';
import { PRESET_EXAMS } from '../data/presetExams';

// Initial enhanced board state
const initialEnhancedBoard: EnhancedTodoBoard = {
  tasks: {},
  columns: {
    'column-1': {
      id: 'column-1',
      title: 'Todo',
      taskIds: [],
      color: '#6366f1', // Indigo
    },
    'column-2': {
      id: 'column-2',
      title: 'In Progress',
      taskIds: [],
      color: '#f59e0b', // Amber
    },
    'column-3': {
      id: 'column-3',
      title: 'Done',
      taskIds: [],
      color: '#10b981', // Emerald
    },
  },
  columnOrder: ['column-1', 'column-2', 'column-3'],
};

// Initial filter state
const initialFilterState: FilterState = {
  subjects: [],
  priorities: [],
  statuses: [],
  dateRange: {},
  tags: [],
  examIds: [],
  showOverdue: false,
  showCompleted: true,
  difficultyLevels: [],
};

// Initial search state
const initialSearchState: SearchState = {
  query: '',
  filters: initialFilterState,
  savedFilters: [],
  activeFilterId: undefined,
};

// Initial analytics data
const initialAnalyticsData: AnalyticsData = {
  completionRates: {
    overall: 0,
    bySubject: {},
    byPriority: {},
    byDifficulty: {},
  },
  taskVelocity: {
    daily: 0,
    weekly: 0,
    monthly: 0,
  },
  studyPatterns: {
    productiveHours: [],
    averageTaskDuration: 0,
    completionStreak: 0,
  },
  subjectDistribution: {},
  upcomingDeadlines: [],
};

// Initial loading state
const initialLoadingState: LoadingState = {
  tasks: false,
  subjects: false,
  exams: false,
  analytics: false,
};

// Initial error state
const initialErrorState: ErrorState = {};

// Initial enhanced state
const initialEnhancedState: EnhancedTodoState = {
  board: initialEnhancedBoard,
  subjects: {},
  presetExams: PRESET_EXAMS,
  search: initialSearchState,
  selectedTasks: [],
  viewMode: 'kanban',
  analytics: initialAnalyticsData,
  loading: initialLoadingState,
  error: initialErrorState,
};

// Track initialization to prevent multiple calls
let isInitialized = false;

// Enhanced todo store with advanced features
export const useEnhancedTodoStore = create<
  EnhancedTodoState & {
    // Core CRUD operations
    fetchTodos: (userId: string) => Promise<void>;
    addTask: (task: Partial<EnhancedTodoItem>) => Promise<void>;
    updateTask: (taskId: string, updates: Partial<EnhancedTodoItem>) => Promise<void>;
    deleteTask: (taskId: string, deleteSubtasks?: boolean) => Promise<void>;
    moveTask: (source: EnhancedDragItem, destination: EnhancedDragItem) => Promise<void>;

    // Hierarchical task operations
    addSubtask: (parentId: string, subtask: Partial<EnhancedTodoItem>) => Promise<void>;
    promoteTask: (taskId: string) => Promise<void>;
    demoteTask: (taskId: string, newParentId: string) => Promise<void>;

    // Column management operations
    addColumn: (title: string, color?: string) => Promise<void>;
    updateColumn: (columnId: string, title: string, color?: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;

    // Subject integration
    fetchSubjects: (userId: string) => Promise<void>;
    linkTaskToSubject: (taskId: string, subjectId: string) => Promise<void>;
    linkTaskToExam: (taskId: string, examId: PresetExamType) => Promise<void>;

    // Search and filtering
    setSearchQuery: (query: string) => void;
    setFilters: (filters: Partial<FilterState>) => void;
    clearFilters: () => void;
    saveFilter: (name: string, filters: FilterState) => void;
    loadFilter: (filterId: string) => void;
    deleteFilter: (filterId: string) => void;

    // Bulk operations
    selectTask: (taskId: string) => void;
    selectAllTasks: (taskIds: string[]) => void;
    clearSelection: () => void;
    bulkOperation: (operation: BulkOperation) => Promise<void>;
    bulkUpdateTasks: (updates: Array<{ id: string; updates: Partial<EnhancedTodoItem> }>, onProgress?: (progress: number) => void) => Promise<void>;

    // View management
    setViewMode: (mode: 'kanban' | 'table' | 'calendar') => void;

    // Analytics
    calculateAnalytics: () => void;
    getTasksBySubject: (subjectId: string) => EnhancedTodoItem[];
    getOverdueTasks: () => EnhancedTodoItem[];
    getUpcomingDeadlines: (days: number) => EnhancedTodoItem[];

    // Utility functions
    getFilteredTasks: () => EnhancedTodoItem[];
    getTaskHierarchy: (taskId: string) => EnhancedTodoItem[];
    calculateTaskProgress: (taskId: string) => number;

    // Real-time subscription
    subscribeToUpdates: (userId: string) => () => void;

    // Reset and cleanup
    reset: () => void;
  }
>()(
    (set, get) => ({
      ...initialEnhancedState,

      // Core CRUD operations
      fetchTodos: async (userId: string) => {
        console.log('Enhanced store: fetchTodos called for user:', userId);
        
        // Prevent multiple simultaneous fetches
        const state = get();
        if (state.loading.tasks) {
          console.log('Fetch already in progress, skipping...');
          return;
        }
        
        set((state) => ({
          loading: { ...state.loading, tasks: true },
          error: { ...state.error, tasks: undefined },
        }));

        try {
          // Check if migration is needed and attempt migration from Supabase
          if (taskStorage.isMigrationNeeded(userId)) {
            console.log('Migration needed, attempting to migrate from Supabase...');
            try {
              const { taskMigration } = await import('../utils/taskMigration');
              const migrationResult = await taskMigration.migrateFromSupabase(userId);
              if (migrationResult.success && migrationResult.tasksImported > 0) {
                console.log(`Successfully migrated ${migrationResult.tasksImported} tasks from Supabase`);
              }
            } catch (migrationError) {
              console.warn('Migration from Supabase failed:', migrationError);
              // Continue with local storage even if migration fails
            }

            taskStorage.migrateFromLegacy(userId);
          }

          // Fetch todos with hierarchy and computed fields from local storage
          console.log('Enhanced store: calling getTasksWithHierarchy...');
          const todos = taskStorage.getTasksWithHierarchy(userId);
          console.log('Enhanced store: received todos:', todos.length, 'items');

          // Get columns from local storage
          const localColumns = taskStorage.getAllColumns(userId);

          const tasks: Record<string, EnhancedTodoItem> = {};
          const columns: Record<string, TodoColumn> = {};

          // Convert columns array to object format
          localColumns.forEach(column => {
            columns[column.id] = {
              id: column.id,
              title: column.title,
              color: column.color,
              taskIds: [],
            };
          });

          // Process todos and organize by columns
          todos.forEach((todo) => {
            console.log('Enhanced store: processing todo:', todo.id, todo.title);
            tasks[todo.id] = todo;

            // Find which column this task belongs to
            const columnId = todo.columnId || 'column-1';
            if (columns[columnId]) {
              columns[columnId].taskIds.push(todo.id);
            } else {
              // If column doesn't exist, move to Todo
              if (columns['column-1']) {
                columns['column-1'].taskIds.push(todo.id);
              }
            }
          });

          console.log('Enhanced store: final tasks object:', Object.keys(tasks).length, 'tasks');
          console.log('Enhanced store: final columns:', columns);

          // Ensure we always have the default columns if none exist
          const finalColumns = Object.keys(columns).length > 0 ? columns : initialEnhancedBoard.columns;

          set((state) => ({
            board: {
              ...state.board,
              tasks: tasks || {},
              columns: finalColumns,
              columnOrder: Object.keys(finalColumns),
            },
            loading: { ...state.loading, tasks: false },
          }));

          // Calculate analytics after loading tasks
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error fetching enhanced todos:', error);
          set((state) => ({
            loading: { ...state.loading, tasks: false },
            error: { ...state.error, tasks: error.message },
          }));
        }
      },

      addTask: async (taskData: Partial<EnhancedTodoItem>) => {
        console.log('Enhanced store: addTask called with:', taskData);
        try {
          const userId = taskData.createdBy || 'current_user'; // Get from auth context in real app

          // Create a complete task data object
          const newTaskData: EnhancedTodoItem = {
            id: taskData.id || `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            title: taskData.title || 'New Task',
            description: taskData.description || '',
            priority: taskData.priority || 'medium',
            createdAt: taskData.createdAt || Date.now(),
            updatedAt: Date.now(),
            createdBy: userId,
            columnId: taskData.columnId || 'column-1',
            tags: taskData.tags || [],
            chapterTags: taskData.chapterTags || [],
            difficultyLevel: taskData.difficultyLevel || 'medium',
            completionPercentage: taskData.completionPercentage || 0,
            viewCount: taskData.viewCount || 0,
            dueDate: taskData.dueDate,
            assignedTo: taskData.assignedTo,
            assignedToName: taskData.assignedToName,
            assignedToPhotoURL: taskData.assignedToPhotoURL,
            parentId: taskData.parentId,
            subjectId: taskData.subjectId,
            examId: taskData.examId,
            timeEstimate: taskData.timeEstimate,
            actualTimeSpent: taskData.actualTimeSpent,
            notes: taskData.notes,
            lastViewed: taskData.lastViewed,
            groupId: taskData.groupId,
          };

          console.log('Enhanced store: saving task to local storage:', newTaskData);
          const createdTask = taskStorage.saveTask(userId, newTaskData);
          console.log('Enhanced store: created task:', createdTask);

          // Update local state immediately for better UX
          set((state) => {
            const columnId = createdTask.columnId || 'column-1';
            const newTasks = { ...state.board.tasks };
            const newColumns = { ...state.board.columns };

            newTasks[createdTask.id] = createdTask;

            // Ensure column exists and add task to it
            if (newColumns[columnId]) {
              newColumns[columnId] = {
                ...newColumns[columnId],
                taskIds: [...(newColumns[columnId].taskIds || []), createdTask.id],
              };
            } else {
              // Create column if it doesn't exist
              newColumns[columnId] = {
                id: columnId,
                title: columnId === 'column-1' ? 'Todo' : columnId === 'column-2' ? 'In Progress' : 'Done',
                color: columnId === 'column-1' ? '#6366f1' : columnId === 'column-2' ? '#f59e0b' : '#10b981',
                taskIds: [createdTask.id],
              };
            }

            const newState = {
              ...state,
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
            
            console.log('Store state updated after task creation:', {
              tasksCount: Object.keys(newTasks).length,
              columnsCount: Object.keys(newColumns).length,
              newTaskId: createdTask.id
            });
            
            return newState;
          });

          // Recalculate analytics
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error adding enhanced task:', error);

          // Provide user-friendly error messages
          let errorMessage = 'Failed to create task';
          if (error.message?.includes('Failed to save')) {
            errorMessage = 'Unable to save task. Please try again.';
          }

          set((state) => ({
            error: { ...state.error, tasks: errorMessage },
          }));
          throw new Error(errorMessage);
        }
      },

      updateTask: async (taskId: string, updates: Partial<EnhancedTodoItem>) => {
        try {
          const state = get();
          const currentTask = state.board.tasks[taskId];
          const userId = currentTask?.createdBy || 'current_user'; // Get from auth context in real app

          if (!currentTask) {
            throw new Error('Task not found');
          }

          // Merge updates with current task
          const updatedTaskData = { ...currentTask, ...updates, updatedAt: Date.now() };
          
          // Update local state immediately for better UX
          set((state) => {
            const newTasks = { ...state.board.tasks };
            newTasks[taskId] = updatedTaskData;

            // Also update column task IDs if column changed
            let newColumns = { ...state.board.columns };
            if (updatedTaskData.columnId && updatedTaskData.columnId !== currentTask.columnId) {
              // Remove from old column
              if (currentTask.columnId && newColumns[currentTask.columnId]) {
                newColumns[currentTask.columnId] = {
                  ...newColumns[currentTask.columnId],
                  taskIds: newColumns[currentTask.columnId].taskIds.filter(id => id !== taskId),
                };
              }
              
              // Add to new column
              if (newColumns[updatedTaskData.columnId]) {
                newColumns[updatedTaskData.columnId] = {
                  ...newColumns[updatedTaskData.columnId],
                  taskIds: [...newColumns[updatedTaskData.columnId].taskIds, taskId],
                };
              }
            }

            const newState = {
              ...state,
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
            
            console.log('Store state updated after task update:', {
              taskId,
              updatedFields: Object.keys(updates),
              tasksCount: Object.keys(newTasks).length,
              completionPercentage: updatedTaskData.completionPercentage
            });
            
            return newState;
          });

          // Save to storage after UI update
          const updatedTask = taskStorage.saveTask(userId, updatedTaskData);

          // Check if task was completed
          const wasCompleted = currentTask?.completionPercentage === 100;
          const isNowCompleted = updatedTask.completionPercentage === 100;
          const justCompleted = !wasCompleted && isNowCompleted;

          // Trigger gamification if task was just completed
          if (justCompleted) {
            // Dispatch custom event for gamification system
            const event = new CustomEvent('taskCompleted', {
              detail: { task: updatedTask }
            });
            window.dispatchEvent(event);
          }

          // Recalculate analytics
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error updating enhanced task:', error);

          // Provide user-friendly error messages
          let errorMessage = 'Failed to update task';
          if (error.message?.includes('Task not found')) {
            errorMessage = 'Task not found';
          } else if (error.message?.includes('Failed to save')) {
            errorMessage = 'Unable to save task updates. Please try again.';
          }

          set((state) => ({
            error: { ...state.error, tasks: errorMessage },
          }));
          throw new Error(errorMessage);
        }
      },

      deleteTask: async (taskId: string, deleteSubtasks: boolean = false) => {
        try {
          const state = get();
          const currentTask = state.board.tasks[taskId];
          const userId = currentTask?.createdBy || 'current_user'; // Get from auth context in real app

          if (!currentTask) {
            throw new Error('Task not found');
          }

          // Delete from local storage
          taskStorage.deleteTask(userId, taskId);

          // Update local state immediately
          set((state) => {
            const newTasks = { ...state.board.tasks };
            const newColumns = { ...state.board.columns };

            // Remove task
            delete newTasks[taskId];

            // Remove from columns
            Object.keys(newColumns).forEach(columnId => {
              newColumns[columnId] = {
                ...newColumns[columnId],
                taskIds: newColumns[columnId].taskIds.filter(id => id !== taskId),
              };
            });

            // If deleteSubtasks is false, handle orphaned subtasks
            if (!deleteSubtasks) {
              Object.values(newTasks).forEach(task => {
                if (task.parentId === taskId) {
                  // Move subtasks to the deleted task's parent (or make them root tasks)
                  const deletedTask = state.board.tasks[taskId];
                  const updatedTask = {
                    ...task,
                    parentId: deletedTask?.parentId || undefined,
                  };
                  newTasks[task.id] = updatedTask;
                  // Update in storage too
                  taskStorage.saveTask(userId, updatedTask);
                }
              });
            } else {
              // Remove all subtasks recursively
              const removeSubtasks = (parentId: string) => {
                Object.values(newTasks).forEach(task => {
                  if (task.parentId === parentId) {
                    removeSubtasks(task.id);
                    delete newTasks[task.id];
                    taskStorage.deleteTask(userId, task.id);

                    // Remove from columns
                    Object.keys(newColumns).forEach(columnId => {
                      newColumns[columnId] = {
                        ...newColumns[columnId],
                        taskIds: newColumns[columnId].taskIds.filter(id => id !== task.id),
                      };
                    });
                  }
                });
              };
              removeSubtasks(taskId);
            }

            return {
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
          });

          // Recalculate analytics
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error deleting enhanced task:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      moveTask: async (source: EnhancedDragItem, destination: EnhancedDragItem) => {
        try {
          const { board } = get();
          const sourceColumn = board.columns[source.droppableId];
          const destColumn = board.columns[destination.droppableId];

          if (!sourceColumn || !destColumn) {
            throw new Error('Invalid source or destination column');
          }

          const taskId = sourceColumn.taskIds[source.index];
          const task = board.tasks[taskId];

          if (!task) {
            throw new Error('Task not found');
          }

          const userId = task.createdBy || 'current_user'; // Get from auth context in real app

          // Update task's column in local storage
          const updatedTask = { ...task, columnId: destination.droppableId, updatedAt: Date.now() };
          taskStorage.saveTask(userId, updatedTask);

          // Move task between columns in local storage
          taskStorage.moveTaskBetweenColumns(
            userId,
            taskId,
            source.droppableId,
            destination.droppableId,
            destination.index
          );

          // Update local state immediately for better UX
          set((state) => {
            const newColumns = { ...state.board.columns };
            const newTasks = { ...state.board.tasks };

            // Remove from source column
            const newSourceTaskIds = [...sourceColumn.taskIds];
            newSourceTaskIds.splice(source.index, 1);
            newColumns[source.droppableId] = {
              ...sourceColumn,
              taskIds: newSourceTaskIds,
            };

            // Add to destination column
            const newDestTaskIds = [...destColumn.taskIds];
            newDestTaskIds.splice(destination.index, 0, taskId);
            newColumns[destination.droppableId] = {
              ...destColumn,
              taskIds: newDestTaskIds,
            };

            // Update task's column reference
            newTasks[taskId] = {
              ...task,
              columnId: destination.droppableId,
              updatedAt: Date.now(),
            };

            return {
              board: {
                ...state.board,
                tasks: newTasks,
                columns: newColumns,
              },
            };
          });

          console.log('Task moved successfully:', taskId, 'from', source.droppableId, 'to', destination.droppableId);
        } catch (error: any) {
          console.error('Error moving task:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      addSubtask: async (parentId: string, subtask: Partial<EnhancedTodoItem>) => {
        const subtaskData = {
          ...subtask,
          parentId,
        };
        await get().addTask(subtaskData);
      },

      promoteTask: async (taskId: string) => {
        const task = get().board.tasks[taskId];
        if (task && task.parentId) {
          const parent = get().board.tasks[task.parentId];
          await get().updateTask(taskId, {
            parentId: parent?.parentId || undefined,
          });
        }
      },

      demoteTask: async (taskId: string, newParentId: string) => {
        await get().updateTask(taskId, { parentId: newParentId });
      },

      // Subject and exam integration
      fetchSubjects: async () => {
        set((state) => ({
          loading: { ...state.loading, subjects: true },
          error: { ...state.error, subjects: undefined },
        }));

        try {
          // For now, use empty subjects array since we're focusing on local storage
          // In a real implementation, you might want to integrate with existing subject storage
          const subjects: Subject[] = [];
          const subjectsMap = subjects.reduce((acc, subject) => {
            acc[subject.id] = subject;
            return acc;
          }, {} as Record<string, Subject>);

          set((state) => ({
            subjects: subjectsMap,
            loading: { ...state.loading, subjects: false },
          }));
        } catch (error: any) {
          console.error('Error fetching subjects:', error);
          set((state) => ({
            loading: { ...state.loading, subjects: false },
            error: { ...state.error, subjects: error.message },
          }));
        }
      },

      linkTaskToSubject: async (taskId: string, subjectId: string) => {
        await get().updateTask(taskId, { subjectId });
      },

      linkTaskToExam: async (taskId: string, examId: PresetExamType) => {
        await get().updateTask(taskId, { examId });
      },

      setSearchQuery: (query: string) => {
        set((state) => ({
          search: {
            ...state.search,
            query,
          },
        }));
      },

      setFilters: (filters: Partial<FilterState>) => {
        set((state) => ({
          search: {
            ...state.search,
            filters: {
              ...state.search.filters,
              ...filters,
            },
          },
        }));
      },

      clearFilters: () => {
        set((state) => ({
          search: {
            ...state.search,
            filters: initialFilterState,
          },
        }));
      },

      saveFilter: (name: string, filters: FilterState) => {
        const filterId = `filter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const savedFilter = {
          id: filterId,
          name,
          filters,
          createdAt: Date.now(),
        };

        set((state) => ({
          search: {
            ...state.search,
            savedFilters: [...state.search.savedFilters, savedFilter],
          },
        }));
      },

      loadFilter: (filterId: string) => {
        const state = get();
        const savedFilter = state.search.savedFilters.find(f => f.id === filterId);
        if (savedFilter) {
          set({
            search: {
              ...state.search,
              filters: savedFilter.filters,
              activeFilterId: filterId,
            },
          });
        }
      },

      deleteFilter: (filterId: string) => {
        set((state) => ({
          search: {
            ...state.search,
            savedFilters: state.search.savedFilters.filter(f => f.id !== filterId),
            activeFilterId: state.search.activeFilterId === filterId ? undefined : state.search.activeFilterId,
          },
        }));
      },

      selectTask: (taskId: string) => {
        set((state) => ({
          selectedTasks: state.selectedTasks.includes(taskId)
            ? state.selectedTasks.filter(id => id !== taskId)
            : [...state.selectedTasks, taskId],
        }));
      },

      selectAllTasks: (taskIds?: string[]) => {
        if (taskIds) {
          set({ selectedTasks: taskIds });
        } else {
          const filteredTasks = get().getFilteredTasks();
          set({ selectedTasks: filteredTasks.map(task => task.id) });
        }
      },

      clearSelection: () => {
        set({ selectedTasks: [] });
      },

      bulkOperation: async (operation: BulkOperation) => {
        try {
          const { type, taskIds, value } = operation;
          
          let updates: Partial<EnhancedTodoItem> = {};
          
          switch (type) {
            case 'complete':
              updates = { completionPercentage: 100 };
              break;
            case 'priority':
              updates = { priority: value };
              break;
            case 'subject':
              updates = { subjectId: value };
              break;
            case 'status':
              updates = { columnId: value };
              break;
            case 'exam': // Added exam type for bulk operations
              updates = { examId: value };
              break;
            case 'delete':
              // Handle delete separately
              for (const taskId of taskIds) {
                await get().deleteTask(taskId);
              }
              get().clearSelection();
              return;
          }

          if (Object.keys(updates).length > 0) {
            const state = get();
            const userId = taskIds.length > 0 ? state.board.tasks[taskIds[0]]?.createdBy || 'current_user' : 'current_user';
            const bulkUpdates = taskIds.map(id => ({ id, updates }));
            taskStorage.bulkUpdateTasks(userId, bulkUpdates);
            
            // Update local state
            set((state) => {
              const newTasks = { ...state.board.tasks };
              taskIds.forEach(taskId => {
                if (newTasks[taskId]) {
                  newTasks[taskId] = { ...newTasks[taskId], ...updates };
                }
              });
              
              return {
                board: {
                  ...state.board,
                  tasks: newTasks,
                },
              };
            });
          }

          get().clearSelection();
          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error performing bulk operation:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      bulkUpdateTasks: async (updates: Array<{ id: string; updates: Partial<EnhancedTodoItem> }>, onProgress?: (progress: number) => void) => {
        try {
          set((state) => ({
            loading: { ...state.loading, tasks: true },
            error: { ...state.error, tasks: null },
          }));

          for (let i = 0; i < updates.length; i++) {
            const { id, updates: taskUpdates } = updates[i];
            const state = get();
            const currentTask = state.board.tasks[id];
            const userId = currentTask?.createdBy || 'current_user';

            if (currentTask) {
              const updatedTask = { ...currentTask, ...taskUpdates, updatedAt: Date.now() };
              taskStorage.saveTask(userId, updatedTask);
            }

            // Update progress
            if (onProgress) {
              onProgress(((i + 1) / updates.length) * 100);
            }

            // Update local state
            set((state) => ({
              board: {
                ...state.board,
                tasks: {
                  ...state.board.tasks,
                  [id]: {
                    ...state.board.tasks[id],
                    ...taskUpdates,
                    updatedAt: Date.now(),
                  },
                },
              },
            }));
          }

          set((state) => ({
            loading: { ...state.loading, tasks: false },
          }));

          get().calculateAnalytics();
        } catch (error: any) {
          console.error('Error performing bulk update:', error);
          set((state) => ({
            loading: { ...state.loading, tasks: false },
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      setViewMode: (mode: 'kanban' | 'table' | 'calendar') => {
        set({ viewMode: mode });
      },

      calculateAnalytics: () => {
        const state = get();
        const tasks = Object.values(state.board.tasks);
        const subjects = Object.values(state.subjects);

        // Calculate completion rates
        const completedTasks = tasks.filter(task => task.completionPercentage === 100);
        const overallCompletionRate = tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0;

        // Calculate by subject
        const bySubject: Record<string, number> = {};
        subjects.forEach(subject => {
          const subjectTasks = tasks.filter(task => task.subjectId === subject.id);
          const subjectCompleted = subjectTasks.filter(task => task.completionPercentage === 100);
          bySubject[subject.id] = subjectTasks.length > 0 ? (subjectCompleted.length / subjectTasks.length) * 100 : 0;
        });

        // Calculate by priority
        const byPriority: Record<string, number> = {};
        ['low', 'medium', 'high'].forEach(priority => {
          const priorityTasks = tasks.filter(task => task.priority === priority);
          const priorityCompleted = priorityTasks.filter(task => task.completionPercentage === 100);
          byPriority[priority] = priorityTasks.length > 0 ? (priorityCompleted.length / priorityTasks.length) * 100 : 0;
        });

        // Calculate by difficulty
        const byDifficulty: Record<string, number> = {};
        ['easy', 'medium', 'hard'].forEach(difficulty => {
          const difficultyTasks = tasks.filter(task => task.difficultyLevel === difficulty);
          const difficultyCompleted = difficultyTasks.filter(task => task.completionPercentage === 100);
          byDifficulty[difficulty] = difficultyTasks.length > 0 ? (difficultyCompleted.length / difficultyTasks.length) * 100 : 0;
        });

        // Calculate upcoming deadlines
        const now = Date.now();
        const upcomingDeadlines = tasks
          .filter(task => task.dueDate && task.dueDate > now && task.completionPercentage < 100)
          .sort((a, b) => (a.dueDate || 0) - (b.dueDate || 0))
          .slice(0, 10);

        // Subject distribution
        const subjectDistribution: Record<string, number> = {};
        subjects.forEach(subject => {
          subjectDistribution[subject.id] = tasks.filter(task => task.subjectId === subject.id).length;
        });

        const analytics: AnalyticsData = {
          completionRates: {
            overall: overallCompletionRate,
            bySubject,
            byPriority,
            byDifficulty,
          },
          taskVelocity: {
            daily: 0, // Will be calculated based on completion history
            weekly: 0,
            monthly: 0,
          },
          studyPatterns: {
            productiveHours: [],
            averageTaskDuration: 0,
            completionStreak: 0,
          },
          subjectDistribution,
          upcomingDeadlines,
        };

        set({ analytics });
      },

      getTasksBySubject: (subjectId: string) => {
        const tasks = Object.values(get().board.tasks);
        return tasks.filter(task => task.subjectId === subjectId);
      },

      getOverdueTasks: () => {
        const tasks = Object.values(get().board.tasks);
        const now = Date.now();
        return tasks.filter(task => 
          task.dueDate && 
          task.dueDate < now && 
          task.completionPercentage < 100
        );
      },

      getUpcomingDeadlines: (days: number) => {
        const tasks = Object.values(get().board.tasks);
        const now = Date.now();
        const futureTime = now + (days * 24 * 60 * 60 * 1000);
        
        return tasks
          .filter(task => 
            task.dueDate && 
            task.dueDate > now && 
            task.dueDate <= futureTime &&
            task.completionPercentage < 100
          )
          .sort((a, b) => (a.dueDate || 0) - (b.dueDate || 0));
      },

      getFilteredTasks: () => {
        const state = get();
        if (!state.board?.tasks) return [];
        
        const tasks = Object.values(state.board.tasks);
        const { query, filters } = state.search;

        let filteredTasks = tasks;

        // Apply text search
        if (query.trim()) {
          const searchTerm = query.toLowerCase();
          filteredTasks = filteredTasks.filter(task =>
            task.title.toLowerCase().includes(searchTerm) ||
            task.description.toLowerCase().includes(searchTerm) ||
            task.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            task.chapterTags.some(tag => tag.toLowerCase().includes(searchTerm))
          );
        }

        // Apply filters
        if (filters.subjects.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            task.subjectId && filters.subjects.includes(task.subjectId)
          );
        }

        if (filters.priorities.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            filters.priorities.includes(task.priority)
          );
        }

        if (filters.difficultyLevels.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            filters.difficultyLevels.includes(task.difficultyLevel)
          );
        }

        if (filters.tags.length > 0) {
          filteredTasks = filteredTasks.filter(task =>
            filters.tags.some(tag => task.tags.includes(tag) || task.chapterTags.includes(tag))
          );
        }

        if (filters.examIds.length > 0) {
          filteredTasks = filteredTasks.filter(task => 
            task.examId && filters.examIds.includes(task.examId)
          );
        }

        if (filters.showOverdue) {
          const now = Date.now();
          filteredTasks = filteredTasks.filter(task => 
            task.dueDate && task.dueDate < now && task.completionPercentage < 100
          );
        }

        if (!filters.showCompleted) {
          filteredTasks = filteredTasks.filter(task => task.completionPercentage < 100);
        }

        // Apply date range filter
        if (filters.dateRange.start || filters.dateRange.end) {
          filteredTasks = filteredTasks.filter(task => {
            if (!task.dueDate) return false;
            
            const taskDate = new Date(task.dueDate);
            const start = filters.dateRange.start;
            const end = filters.dateRange.end;
            
            if (start && taskDate < start) return false;
            if (end && taskDate > end) return false;
            
            return true;
          });
        }

        return filteredTasks;
      },

      getTaskHierarchy: (taskId: string) => {
        const tasks = get().board.tasks;
        const hierarchy: EnhancedTodoItem[] = [];
        
        const addSubtasks = (parentId: string) => {
          Object.values(tasks).forEach(task => {
            if (task.parentId === parentId) {
              hierarchy.push(task);
              addSubtasks(task.id);
            }
          });
        };
        
        addSubtasks(taskId);
        return hierarchy;
      },

      calculateTaskProgress: (taskId: string) => {
        const task = get().board.tasks[taskId];
        if (!task) return 0;
        
        const subtasks = get().getTaskHierarchy(taskId);
        if (subtasks.length === 0) {
          return task.completionPercentage;
        }
        
        const totalProgress = subtasks.reduce((sum, subtask) => sum + subtask.completionPercentage, 0);
        return Math.round(totalProgress / subtasks.length);
      },

      // Column management operations
      addColumn: async (title: string, color: string = '#6366f1') => {
        try {
          const columnId = `column-${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          const newColumn: TodoColumn = {
            id: columnId,
            title,
            taskIds: [],
            color,
          };

          set((state) => ({
            board: {
              ...state.board,
              columns: {
                ...state.board.columns,
                [columnId]: newColumn,
              },
              columnOrder: [...state.board.columnOrder, columnId],
            },
          }));
        } catch (error: any) {
          console.error('Error adding column:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      updateColumn: async (columnId: string, title: string, color?: string) => {
        try {
          set((state) => {
            const column = state.board.columns[columnId];
            if (!column) return state;

            return {
              board: {
                ...state.board,
                columns: {
                  ...state.board.columns,
                  [columnId]: {
                    ...column,
                    title,
                    color: color || column.color,
                  },
                },
              },
            };
          });
        } catch (error: any) {
          console.error('Error updating column:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      deleteColumn: async (columnId: string) => {
        try {
          set((state) => {
            const { [columnId]: deletedColumn, ...remainingColumns } = state.board.columns;
            const newColumnOrder = state.board.columnOrder.filter(id => id !== columnId);

            // Move tasks from deleted column to the first remaining column
            const firstColumnId = newColumnOrder[0];
            if (firstColumnId && deletedColumn) {
              remainingColumns[firstColumnId] = {
                ...remainingColumns[firstColumnId],
                taskIds: [...remainingColumns[firstColumnId].taskIds, ...deletedColumn.taskIds],
              };
            }

            return {
              board: {
                ...state.board,
                columns: remainingColumns,
                columnOrder: newColumnOrder,
              },
            };
          });
        } catch (error: any) {
          console.error('Error deleting column:', error);
          set((state) => ({
            error: { ...state.error, tasks: error.message },
          }));
          throw error;
        }
      },

      subscribeToUpdates: (userId: string) => {
        // For local storage, we don't need real-time subscriptions
        // Instead, we can use localStorage events for cross-tab synchronization
        const handleStorageChange = (event: StorageEvent) => {
          if (event.key?.includes('isotope_tasks') && event.key.includes(userId)) {
            console.log('Local storage task update detected');
            get().fetchTodos(userId);
          }
        };

        window.addEventListener('storage', handleStorageChange);

        return () => {
          window.removeEventListener('storage', handleStorageChange);
        };
      },

      // Advanced search and analytics
      searchTasks: async (query: string) => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return [];

          const results = taskStorage.searchTasks(userId, query);
          return results;
        } catch (error) {
          console.error('Error searching tasks:', error);
          return [];
        }
      },

      getAnalytics: async () => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return null;

          const analytics = taskStorage.calculateAnalytics(userId);

          set((state) => ({
            analytics: {
              ...state.analytics,
              ...analytics,
            },
          }));

          return analytics;
        } catch (error) {
          console.error('Error getting analytics:', error);
          return null;
        }
      },

      // Export/Import functionality
      exportTasks: async (format: 'json' | 'csv' = 'json') => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return '';

          return format === 'csv' ? taskStorage.exportToCSV(userId) : taskStorage.exportTasks(userId);
        } catch (error) {
          console.error('Error exporting tasks:', error);
          return '';
        }
      },

      importTasks: async (data: string) => {
        try {
          const state = get();
          const userId = state.board.tasks ? Object.values(state.board.tasks)[0]?.createdBy : '';
          if (!userId) return [];

          const importResult = taskStorage.importTasks(userId, data);

          // Refresh tasks after import
          await get().fetchTodos(userId);

          return importResult.imported;
        } catch (error) {
          console.error('Error importing tasks:', error);
          return [];
        }
      },

      reset: () => {
        isInitialized = false;
        set(initialEnhancedState);
      },
    })
);
