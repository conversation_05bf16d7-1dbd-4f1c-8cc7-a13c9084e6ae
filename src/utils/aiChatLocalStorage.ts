import { ChatHistory, Message, Comment } from '@/types/chat';

// Storage keys
const CHAT_HISTORY_KEY = 'ai_chat_history';
const CHAT_STATS_KEY = 'ai_chat_stats';

// Interface for chat statistics
interface ChatStats {
  questionsAsked: number;
  lastQuestionDate: string;
  streak: number;
}

// Interface for localStorage chat data
interface StoredChatData {
  [userId: string]: ChatHistory[];
}

interface StoredStatsData {
  [userId: string]: ChatStats;
}

/**
 * Get the storage key for a specific user's chat history
 */
const getChatStorageKey = (userId: string): string => `${CHAT_HISTORY_KEY}_${userId}`;

/**
 * Get the storage key for a specific user's chat statistics
 */
const getStatsStorageKey = (userId: string): string => `${CHAT_STATS_KEY}_${userId}`;

/**
 * Safely parse JSON from localStorage
 */
const safeParseJSON = <T>(data: string | null, fallback: T): T => {
  if (!data) return fallback;
  try {
    return JSON.parse(data);
  } catch (error) {
    console.error('Error parsing JSON from localStorage:', error);
    return fallback;
  }
};

/**
 * Safely stringify and save to localStorage
 */
const safeSaveToStorage = (key: string, data: any): boolean => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    return false;
  }
};

/**
 * Get all chat history for a user
 */
export const getUserChatHistory = async (userId: string): Promise<ChatHistory[]> => {
  try {
    const storageKey = getChatStorageKey(userId);
    const data = localStorage.getItem(storageKey);
    const chatHistory = safeParseJSON<ChatHistory[]>(data, []);
    
    // Sort by timestamp (newest first)
    return chatHistory.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error loading chat history:', error);
    return [];
  }
};

/**
 * Save or update a chat
 */
export const saveAIChat = async (
  chatId: string, 
  chatData: {
    userId: string;
    messages: Message[];
    preview: string;
    isStarred?: boolean;
    isPinned?: boolean;
    comments?: Comment[];
  }, 
  isNew: boolean = false
): Promise<ChatHistory> => {
  try {
    const { userId, messages, preview, isStarred = false, isPinned = false, comments = [] } = chatData;
    const storageKey = getChatStorageKey(userId);
    const existingChats = await getUserChatHistory(userId);
    
    const timestamp = Date.now();
    const chatEntry: ChatHistory = {
      id: chatId,
      timestamp,
      messages,
      preview,
      isStarred,
      isPinned,
      comments
    };

    let updatedChats: ChatHistory[];
    
    if (isNew) {
      // Add new chat to the beginning
      updatedChats = [chatEntry, ...existingChats];
    } else {
      // Update existing chat or add if not found
      const existingIndex = existingChats.findIndex(chat => chat.id === chatId);
      if (existingIndex >= 0) {
        updatedChats = [...existingChats];
        updatedChats[existingIndex] = { ...updatedChats[existingIndex], ...chatEntry };
      } else {
        updatedChats = [chatEntry, ...existingChats];
      }
    }

    safeSaveToStorage(storageKey, updatedChats);
    return chatEntry;
  } catch (error) {
    console.error('Error saving AI chat:', error);
    throw error;
  }
};

/**
 * Delete a chat
 */
export const deleteAIChat = async (chatId: string, userId: string): Promise<void> => {
  try {
    const storageKey = getChatStorageKey(userId);
    const existingChats = await getUserChatHistory(userId);
    const updatedChats = existingChats.filter(chat => chat.id !== chatId);
    
    safeSaveToStorage(storageKey, updatedChats);
  } catch (error) {
    console.error('Error deleting AI chat:', error);
    throw error;
  }
};

/**
 * Toggle chat starred status
 */
export const toggleChatStarred = async (chatId: string, userId: string, isStarred: boolean): Promise<void> => {
  try {
    const storageKey = getChatStorageKey(userId);
    const existingChats = await getUserChatHistory(userId);
    const updatedChats = existingChats.map(chat => 
      chat.id === chatId ? { ...chat, isStarred } : chat
    );
    
    safeSaveToStorage(storageKey, updatedChats);
  } catch (error) {
    console.error('Error toggling chat starred status:', error);
    throw error;
  }
};

/**
 * Toggle chat pinned status
 */
export const toggleChatPinned = async (chatId: string, userId: string, isPinned: boolean): Promise<void> => {
  try {
    const storageKey = getChatStorageKey(userId);
    const existingChats = await getUserChatHistory(userId);
    const updatedChats = existingChats.map(chat => 
      chat.id === chatId ? { ...chat, isPinned } : chat
    );
    
    safeSaveToStorage(storageKey, updatedChats);
  } catch (error) {
    console.error('Error toggling chat pinned status:', error);
    throw error;
  }
};

/**
 * Get chat statistics for a user
 */
export const getChatStats = async (userId: string): Promise<ChatStats> => {
  try {
    const storageKey = getStatsStorageKey(userId);
    const data = localStorage.getItem(storageKey);
    return safeParseJSON<ChatStats>(data, {
      questionsAsked: 0,
      lastQuestionDate: '',
      streak: 0
    });
  } catch (error) {
    console.error('Error loading chat stats:', error);
    return { questionsAsked: 0, lastQuestionDate: '', streak: 0 };
  }
};

/**
 * Increment questions asked count and update streak
 */
export const incrementQuestionsAsked = async (userId: string): Promise<void> => {
  try {
    const stats = await getChatStats(userId);
    const today = new Date().toDateString();
    const lastDate = stats.lastQuestionDate;
    
    let newStreak = stats.streak;
    if (lastDate !== today) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (lastDate === yesterday.toDateString()) {
        newStreak += 1;
      } else if (lastDate !== today) {
        newStreak = 1;
      }
    }

    const updatedStats: ChatStats = {
      questionsAsked: stats.questionsAsked + 1,
      lastQuestionDate: today,
      streak: newStreak
    };

    const storageKey = getStatsStorageKey(userId);
    safeSaveToStorage(storageKey, updatedStats);
  } catch (error) {
    console.error('Error incrementing questions asked:', error);
    throw error;
  }
};

/**
 * Search chats by content
 */
export const searchChats = async (userId: string, query: string): Promise<ChatHistory[]> => {
  try {
    const allChats = await getUserChatHistory(userId);
    const lowercaseQuery = query.toLowerCase();
    
    return allChats.filter(chat => {
      // Search in preview
      if (chat.preview.toLowerCase().includes(lowercaseQuery)) {
        return true;
      }
      
      // Search in messages
      return chat.messages.some(message => 
        message.content.toLowerCase().includes(lowercaseQuery)
      );
    });
  } catch (error) {
    console.error('Error searching chats:', error);
    return [];
  }
};

/**
 * Get a specific chat by ID
 */
export const getChatById = async (userId: string, chatId: string): Promise<ChatHistory | null> => {
  try {
    const allChats = await getUserChatHistory(userId);
    return allChats.find(chat => chat.id === chatId) || null;
  } catch (error) {
    console.error('Error getting chat by ID:', error);
    return null;
  }
};

/**
 * Update chat comments
 */
export const updateChatComments = async (
  chatId: string, 
  userId: string, 
  comments: Comment[]
): Promise<void> => {
  try {
    const storageKey = getChatStorageKey(userId);
    const existingChats = await getUserChatHistory(userId);
    const updatedChats = existingChats.map(chat => 
      chat.id === chatId ? { ...chat, comments } : chat
    );
    
    safeSaveToStorage(storageKey, updatedChats);
  } catch (error) {
    console.error('Error updating chat comments:', error);
    throw error;
  }
};

/**
 * Clear all chat history for a user (useful for testing or user preference)
 */
export const clearAllChats = async (userId: string): Promise<void> => {
  try {
    const storageKey = getChatStorageKey(userId);
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error('Error clearing all chats:', error);
    throw error;
  }
};

/**
 * Export chat history (for backup purposes)
 */
export const exportChatHistory = async (userId: string): Promise<string> => {
  try {
    const chatHistory = await getUserChatHistory(userId);
    const stats = await getChatStats(userId);
    
    const exportData = {
      chatHistory,
      stats,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };
    
    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error('Error exporting chat history:', error);
    throw error;
  }
};

/**
 * Import chat history (for restore purposes)
 */
export const importChatHistory = async (userId: string, importData: string): Promise<boolean> => {
  try {
    const data = JSON.parse(importData);
    
    if (data.chatHistory && Array.isArray(data.chatHistory)) {
      const storageKey = getChatStorageKey(userId);
      safeSaveToStorage(storageKey, data.chatHistory);
      
      if (data.stats) {
        const statsKey = getStatsStorageKey(userId);
        safeSaveToStorage(statsKey, data.stats);
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error importing chat history:', error);
    return false;
  }
};
