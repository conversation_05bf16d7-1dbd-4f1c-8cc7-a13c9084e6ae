import { supabase } from '../integrations/supabase/client';

export const testSupabaseConnection = async () => {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    // Test 1: Check if client is initialized
    console.log('✅ Supabase client initialized');
    
    // Test 2: Test auth connection
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
    } else {
      console.log('✅ Auth connection working, session:', session?.user?.id || 'No active session');
    }
    
    // Test 3: Test database connection with a simple query
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection error:', error);
      return false;
    } else {
      console.log('✅ Database connection working');
      return true;
    }
  } catch (error) {
    console.error('❌ Supabase connection test failed:', error);
    return false;
  }
};

// Auto-run test in development
if (import.meta.env.DEV) {
  testSupabaseConnection();
}
