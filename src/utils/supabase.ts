import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';
import { User } from '@supabase/supabase-js';

// Type definitions
type Tables = Database['public']['Tables'];
type UserRow = Tables['users']['Row'];
type UserInsert = Tables['users']['Insert'];
type UserUpdate = Tables['users']['Update'];
type StudySessionRow = Tables['study_sessions']['Row'];
type StudySessionInsert = Tables['study_sessions']['Insert'];
type TodoRow = Tables['todos']['Row'];
type TodoInsert = Tables['todos']['Insert'];
type TodoUpdate = Tables['todos']['Update'];
type UserSubjectRow = Tables['userSubjects']['Row'];
type UserSubjectInsert = Tables['userSubjects']['Insert'];
type GroupRow = Tables['groups']['Row'];
type GroupInsert = Tables['groups']['Insert'];
// TODO: Add exam types when exams table is implemented
// type ExamRow = Tables['exams']['Row'];
// type ExamInsert = Tables['exams']['Insert'];
type MockTestRow = Tables['mock_tests']['Row'];
type MockTestInsert = Tables['mock_tests']['Insert'];

// Authentication functions
export const signInWithGoogle = async () => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

export const signInWithEmailPassword = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error signing in with email/password:', error);
    throw error;
  }
};

export const signUpWithEmailPassword = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error signing up with email/password:', error);
    throw error;
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

export const getCurrentUser = () => {
  return supabase.auth.getUser();
};

export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return supabase.auth.onAuthStateChange((event, session) => {
    callback(session?.user ?? null);
  });
};

// Utility function to ensure user is authenticated
export const ensureAuthenticated = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    console.error('Error getting session:', error);
    throw new Error('Authentication error');
  }

  if (!session) {
    // Try to refresh the session
    const { data: { session: refreshedSession }, error: refreshError } = await supabase.auth.refreshSession();

    if (refreshError || !refreshedSession) {
      console.error('Error refreshing session:', refreshError);
      throw new Error('User not authenticated');
    }

    return refreshedSession;
  }

  return session;
};

// User profile functions
export const getUserProfile = async (userId: string): Promise<UserRow | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

export const createUserProfile = async (user: User): Promise<UserRow> => {
  try {
    const userProfile: UserInsert = {
      id: user.id,
      email: user.email!,
      username: user.user_metadata?.username || user.email?.split('@')[0] || `user_${user.id.substring(0, 5)}`,
      display_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
      photo_url: user.user_metadata?.avatar_url || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
      member_since: new Date().toISOString(),
      welcome_email_sent: false,
      daily_target: 120, // 2 hours in minutes
      daily_motivation: 'Stay focused and achieve your goals!',
      day_start_time: 4, // 4 AM
      stats: {},
      progress: {}
    };

    const { data, error } = await supabase
      .from('users')
      .insert(userProfile)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
};

export const updateUserProfile = async (userId: string, updates: UserUpdate): Promise<UserRow> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Enhanced study session management with task association
export interface EnhancedStudySessionData extends StudySessionInsert {
  task_id?: string;
  estimated_duration?: number;
  completion_percentage?: number;
  difficulty_rating?: number;
  focus_rating?: number;
  break_type?: string;
  session_tags?: string[];
}

export interface SessionFeedback {
  productivityRating: number; // 1-5
  notes?: string;
  difficultyRating?: number; // 1-5
  focusRating?: number; // 1-5
  completedSubtasks?: string[];
  nextSteps?: string;
}

// Enhanced saveStudySession with task association and metadata
export const saveStudySession = async (sessionData: EnhancedStudySessionData): Promise<StudySessionRow> => {
  try {
    // Prepare the session data with enhanced metadata
    const enhancedSessionData: StudySessionInsert = {
      ...sessionData,
      // Map enhanced fields to existing table structure where possible
      productivity_rating: sessionData.productivity_rating || sessionData.difficulty_rating,
      notes: sessionData.notes || (sessionData.session_tags ? `Tags: ${sessionData.session_tags.join(', ')}` : undefined),
      // Store additional metadata in notes field as JSON if needed
      feedback: sessionData.focus_rating ? JSON.stringify({
        focus_rating: sessionData.focus_rating,
        difficulty_rating: sessionData.difficulty_rating,
        completion_percentage: sessionData.completion_percentage,
        session_tags: sessionData.session_tags,
        break_type: sessionData.break_type
      }) : sessionData.feedback
    };

    const { data, error } = await supabase
      .from('study_sessions')
      .insert(enhancedSessionData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error saving enhanced study session:', error);
    throw error;
  }
};

// Save study session with task association and feedback
export const saveStudySessionWithTaskAssociation = async (
  sessionData: EnhancedStudySessionData,
  feedback?: SessionFeedback
): Promise<StudySessionRow> => {
  try {
    // Merge feedback into session data
    const sessionWithFeedback: EnhancedStudySessionData = {
      ...sessionData,
      productivity_rating: feedback?.productivityRating || sessionData.productivity_rating,
      difficulty_rating: feedback?.difficultyRating || sessionData.difficulty_rating,
      focus_rating: feedback?.focusRating || sessionData.focus_rating,
      notes: feedback?.notes || sessionData.notes,
      feedback: feedback ? JSON.stringify({
        productivityRating: feedback.productivityRating,
        difficultyRating: feedback.difficultyRating,
        focusRating: feedback.focusRating,
        completedSubtasks: feedback.completedSubtasks,
        nextSteps: feedback.nextSteps,
        notes: feedback.notes
      }) : sessionData.feedback
    };

    return await saveStudySession(sessionWithFeedback);
  } catch (error) {
    console.error('Error saving study session with task association:', error);
    throw error;
  }
};

export const getStudySessions = async (userId: string, startDate?: string, endDate?: string): Promise<StudySessionRow[]> => {
  try {
    let query = supabase
      .from('study_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('start_time', { ascending: false });

    if (startDate) {
      query = query.gte('date', startDate);
    }
    if (endDate) {
      query = query.lte('date', endDate);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting study sessions:', error);
    return [];
  }
};

// Get study sessions for a specific task
export const getStudySessionsByTask = async (userId: string, taskName: string): Promise<StudySessionRow[]> => {
  try {
    const { data, error } = await supabase
      .from('study_sessions')
      .select('*')
      .eq('user_id', userId)
      .eq('task_name', taskName)
      .order('start_time', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting study sessions by task:', error);
    return [];
  }
};

// Get study sessions by task ID (for localStorage task integration)
export const getStudySessionsByTaskId = async (userId: string, taskId: string): Promise<StudySessionRow[]> => {
  try {
    // Since task_id might be stored in feedback as JSON or in task_name, we need to check both
    const { data, error } = await supabase
      .from('study_sessions')
      .select('*')
      .eq('user_id', userId)
      .or(`task_name.eq.${taskId},feedback.cs.{"task_id":"${taskId}"}`)
      .order('start_time', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting study sessions by task ID:', error);
    return [];
  }
};

// Get task time analytics
export interface TaskTimeAnalytics {
  taskId: string;
  taskName: string;
  totalTimeSpent: number; // in seconds
  sessionCount: number;
  averageSessionLength: number;
  averageProductivityRating: number;
  lastWorkedOn?: Date;
  sessions: StudySessionRow[];
}

export const getTaskTimeAnalytics = async (userId: string, taskName: string): Promise<TaskTimeAnalytics | null> => {
  try {
    const sessions = await getStudySessionsByTask(userId, taskName);
    
    if (sessions.length === 0) {
      return null;
    }

    const totalTimeSpent = sessions.reduce((total, session) => total + session.duration, 0);
    const averageSessionLength = totalTimeSpent / sessions.length;
    const productivityRatings = sessions.filter(s => s.productivity_rating).map(s => s.productivity_rating!);
    const averageProductivityRating = productivityRatings.length > 0 
      ? productivityRatings.reduce((sum, rating) => sum + rating, 0) / productivityRatings.length 
      : 0;

    return {
      taskId: taskName, // Using task name as ID for now
      taskName,
      totalTimeSpent,
      sessionCount: sessions.length,
      averageSessionLength,
      averageProductivityRating,
      lastWorkedOn: sessions[0] ? new Date(sessions[0].start_time) : undefined,
      sessions
    };
  } catch (error) {
    console.error('Error getting task time analytics:', error);
    return null;
  }
};

export const updateStudySession = async (sessionId: string, updates: Partial<StudySessionRow>): Promise<StudySessionRow> => {
  try {
    const { data, error } = await supabase
      .from('study_sessions')
      .update(updates)
      .eq('id', sessionId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating study session:', error);
    throw error;
  }
};

export const deleteStudySession = async (sessionId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('study_sessions')
      .delete()
      .eq('id', sessionId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting study session:', error);
    throw error;
  }
};

// User subjects functions
export const getUserSubjects = async (userId: string): Promise<UserSubjectRow[]> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .select('*')
      .eq('userId', userId)
      .order('createdAt', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting user subjects:', error);
    return [];
  }
};

export const createUserSubject = async (subjectData: UserSubjectInsert): Promise<UserSubjectRow> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .insert(subjectData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating user subject:', error);
    throw error;
  }
};

export const updateUserSubject = async (subjectId: string, updates: Partial<UserSubjectRow>): Promise<UserSubjectRow> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .update(updates)
      .eq('id', subjectId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user subject:', error);
    throw error;
  }
};

export const deleteUserSubject = async (subjectId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('userSubjects')
      .delete()
      .eq('id', subjectId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting user subject:', error);
    throw error;
  }
};

// Real-time subscriptions
export const subscribeToUserData = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-data-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'users',
        filter: `id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToStudySessions = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('study-sessions-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'study_sessions',
        filter: `user_id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToUserSubjects = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-subjects-changes')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'userSubjects',
        filter: `userId=eq.${userId}`
      },
      callback
    )
    .subscribe();
};

// Todo functions
export const getTodos = async (userId: string): Promise<TodoRow[]> => {
  try {
    // Ensure user is authenticated
    const session = await ensureAuthenticated();
    console.log('Fetching todos for user:', userId, 'with session:', session.user.id);

    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('created_by', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Supabase error getting todos:', error);
      throw error;
    }

    console.log('Fetched todos:', data?.length || 0, 'items');
    return data || [];
  } catch (error) {
    console.error('Error getting todos:', error);
    return [];
  }
};

export const createTodo = async (todoData: TodoInsert): Promise<TodoRow> => {
  try {
    // Ensure user is authenticated
    const session = await ensureAuthenticated();
    console.log('Creating todo with session:', session.user.id, 'data:', todoData);

    const { data, error } = await supabase
      .from('todos')
      .insert(todoData)
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating todo:', error);
      throw error;
    }

    console.log('Todo created successfully:', data);
    return data;
  } catch (error) {
    console.error('Error creating todo:', error);
    throw error;
  }
};

export const updateTodo = async (todoId: string, updates: TodoUpdate): Promise<TodoRow> => {
  try {
    // Ensure user is authenticated
    const session = await ensureAuthenticated();
    console.log('Updating todo with session:', session.user.id, 'todoId:', todoId, 'updates:', updates);

    const { data, error } = await supabase
      .from('todos')
      .update({
        ...updates,
        updated_at: Date.now(),
      })
      .eq('id', todoId)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating todo:', error);
      throw error;
    }

    console.log('Todo updated successfully:', data);
    return data;
  } catch (error) {
    console.error('Error updating todo:', error);
    throw error;
  }
};

export const deleteTodo = async (todoId: string): Promise<void> => {
  try {
    // Ensure user is authenticated
    const session = await ensureAuthenticated();
    console.log('Deleting todo with session:', session.user.id, 'todoId:', todoId);

    const { error } = await supabase
      .from('todos')
      .delete()
      .eq('id', todoId);

    if (error) {
      console.error('Supabase error deleting todo:', error);
      throw error;
    }

    console.log('Todo deleted successfully:', todoId);
  } catch (error) {
    console.error('Error deleting todo:', error);
    throw error;
  }
};

export const subscribeToTodos = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('todos-changes')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'todos',
        filter: `created_by=eq.${userId}`
      },
      callback
    )
    .subscribe();
};

// Groups functions
export const getGroups = async (userId: string): Promise<GroupRow[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .or(`createdBy.eq.${userId},owner_id.eq.${userId},members.cs.{${userId}}`)
      .order('last_activity', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting groups:', error);
    return [];
  }
};

export const createGroup = async (groupData: GroupInsert): Promise<GroupRow> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .insert(groupData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating group:', error);
    throw error;
  }
};

export const updateGroup = async (groupId: string, updates: Partial<GroupRow>): Promise<GroupRow> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating group:', error);
    throw error;
  }
};

export const deleteGroup = async (groupId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('groups')
      .delete()
      .eq('id', groupId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting group:', error);
    throw error;
  }
};

export const joinGroup = async (groupId: string, userId: string): Promise<GroupRow> => {
  try {
    // First get the current group data
    const { data: currentGroup, error: fetchError } = await supabase
      .from('groups')
      .select('members')
      .eq('id', groupId)
      .single();

    if (fetchError) throw fetchError;

    // Add user to members array if not already present
    const currentMembers = currentGroup.members || [];
    if (!currentMembers.includes(userId)) {
      currentMembers.push(userId);
    }

    // Update the group with new members array
    const { data, error } = await supabase
      .from('groups')
      .update({
        members: currentMembers,
        updated_at: new Date().toISOString()
      })
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error joining group:', error);
    throw error;
  }
};

export const leaveGroup = async (groupId: string, userId: string): Promise<GroupRow> => {
  try {
    // First get the current group data
    const { data: currentGroup, error: fetchError } = await supabase
      .from('groups')
      .select('members')
      .eq('id', groupId)
      .single();

    if (fetchError) throw fetchError;

    // Remove user from members array
    const currentMembers = currentGroup.members || [];
    const updatedMembers = currentMembers.filter(memberId => memberId !== userId);

    // Update the group with new members array
    const { data, error } = await supabase
      .from('groups')
      .update({
        members: updatedMembers,
        updated_at: new Date().toISOString()
      })
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error leaving group:', error);
    throw error;
  }
};

export const getPublicGroups = async (userId: string): Promise<GroupRow[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('isPublic', true)
      .not('members', 'cs', `{${userId}}`)
      .not('createdBy', 'eq', userId)
      .not('owner_id', 'eq', userId)
      .order('last_activity', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting public groups:', error);
    return [];
  }
};

export const getGroupByInviteCode = async (inviteCode: string): Promise<GroupRow | null> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('inviteCode', inviteCode)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      throw error;
    }
    return data;
  } catch (error) {
    console.error('Error getting group by invite code:', error);
    return null;
  }
};

// Real-time subscription for user's groups
export const subscribeToUserGroups = (
  userId: string,
  callback: (groups: GroupRow[]) => void
) => {
  return supabase
    .channel('user-groups')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'groups',
        filter: `members.cs.{${userId}}`
      },
      async () => {
        // Fetch updated groups when changes occur
        const groups = await getGroups(userId);
        callback(groups);
      }
    )
    .subscribe();
};

// Generate a random invite code
export const generateInviteCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Find group by invite code
export const findGroupByInviteCode = async (inviteCode: string): Promise<GroupRow | null> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .or(`invite_code.ilike.${inviteCode.toUpperCase()},inviteCode.ilike.${inviteCode.toUpperCase()}`)
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      throw error;
    }
    return data;
  } catch (error) {
    console.error('Error finding group by invite code:', error);
    return null;
  }
};

// User statistics functions
export const calculateUserStudyTime = async (userId: string): Promise<number> => {
  try {
    const sessions = await getStudySessions(userId);
    return sessions.reduce((total, session) => total + (session.duration || 0), 0);
  } catch (error) {
    console.error('Error calculating user study time:', error);
    return 0;
  }
};

export const calculateUserStudyStreak = async (userId: string): Promise<number> => {
  try {
    const sessions = await getStudySessions(userId);
    if (sessions.length === 0) return 0;

    // Get unique dates and sort them
    const dates = [...new Set(sessions.map(session => session.date))].sort();

    let currentStreak = 0;
    let maxStreak = 0;
    let lastDate = '';

    dates.forEach(date => {
      if (lastDate === '') {
        currentStreak = 1;
      } else {
        const dayDiff = Math.floor(
          (new Date(date).getTime() - new Date(lastDate).getTime()) / (1000 * 60 * 60 * 24)
        );
        if (dayDiff === 1) {
          currentStreak++;
        } else {
          currentStreak = 1;
        }
      }
      maxStreak = Math.max(maxStreak, currentStreak);
      lastDate = date;
    });

    return maxStreak;
  } catch (error) {
    console.error('Error calculating user study streak:', error);
    return 0;
  }
};

// Group statistics functions
export interface GroupStats {
  totalStudyTime: number;
  totalSessions: number;
  lastActiveDate: Date | null;
  memberCount: number;
  averageStudyTimePerMember: number;
}

export const getGroupStats = async (groupId: string): Promise<GroupStats> => {
  try {
    // Get the group to access member list
    const { data: group, error: groupError } = await supabase
      .from('groups')
      .select('members')
      .eq('id', groupId)
      .single();

    if (groupError) throw groupError;

    const members = group.members || [];
    let totalStudyTime = 0;
    let totalSessions = 0;
    let lastActiveDate: Date | null = null;

    // Calculate stats for all group members
    for (const memberId of members) {
      const memberSessions = await getStudySessions(memberId);
      const memberStudyTime = memberSessions.reduce((total, session) => total + (session.duration || 0), 0);

      totalStudyTime += memberStudyTime;
      totalSessions += memberSessions.length;

      // Find the most recent session date
      const memberLastActive = memberSessions.reduce((latest, session) => {
        const sessionDate = new Date(session.date);
        return !latest || sessionDate > latest ? sessionDate : latest;
      }, null as Date | null);

      if (memberLastActive && (!lastActiveDate || memberLastActive > lastActiveDate)) {
        lastActiveDate = memberLastActive;
      }
    }

    const averageStudyTimePerMember = members.length > 0 ? totalStudyTime / members.length : 0;

    return {
      totalStudyTime,
      totalSessions,
      lastActiveDate,
      memberCount: members.length,
      averageStudyTimePerMember
    };
  } catch (error) {
    console.error('Error getting group stats:', error);
    return {
      totalStudyTime: 0,
      totalSessions: 0,
      lastActiveDate: null,
      memberCount: 0,
      averageStudyTimePerMember: 0
    };
  }
};

export const getGroupMemberStats = async (groupId: string): Promise<{ [userId: string]: { studyTime: number; sessions: number } }> => {
  try {
    // Get the group to access member list
    const { data: group, error: groupError } = await supabase
      .from('groups')
      .select('members')
      .eq('id', groupId)
      .single();

    if (groupError) throw groupError;

    const members = group.members || [];
    const memberStats: { [userId: string]: { studyTime: number; sessions: number } } = {};

    // Calculate stats for each member
    for (const memberId of members) {
      const memberSessions = await getStudySessions(memberId);
      const studyTime = memberSessions.reduce((total, session) => total + (session.duration || 0), 0);

      memberStats[memberId] = {
        studyTime,
        sessions: memberSessions.length
      };
    }

    return memberStats;
  } catch (error) {
    console.error('Error getting group member stats:', error);
    return {};
  }
};

// TODO: Exams functions - implement when exams table is added to Supabase
// export const getExams = async (userId: string): Promise<ExamRow[]> => {
//   try {
//     const { data, error } = await supabase
//       .from('exams')
//       .select('*')
//       .eq('userId', userId)
//       .order('date', { ascending: false });

//     if (error) throw error;
//     return data || [];
//   } catch (error) {
//     console.error('Error getting exams:', error);
//     return [];
//   }
// };

// export const createExam = async (examData: ExamInsert): Promise<ExamRow> => {
//   try {
//     const { data, error } = await supabase
//       .from('exams')
//       .insert(examData)
//       .select()
//       .single();

//     if (error) throw error;
//     return data;
//   } catch (error) {
//     console.error('Error creating exam:', error);
//     throw error;
//   }
// };

// Mock tests functions
export const getMockTests = async (userId: string): Promise<MockTestRow[]> => {
  try {
    const { data, error } = await supabase
      .from('mock_tests')
      .select('*')
      .eq('user_id', userId)
      .order('test_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting mock tests:', error);
    return [];
  }
};

export const createMockTest = async (mockTestData: MockTestInsert): Promise<MockTestRow> => {
  try {
    const { data, error } = await supabase
      .from('mock_tests')
      .insert(mockTestData)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating mock test:', error);
    throw error;
  }
};

export const updateMockTest = async (mockTestId: string, updates: Partial<MockTestRow>): Promise<MockTestRow> => {
  try {
    const { data, error } = await supabase
      .from('mock_tests')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', mockTestId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating mock test:', error);
    throw error;
  }
};

export const deleteMockTestById = async (mockTestId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('mock_tests')
      .delete()
      .eq('id', mockTestId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting mock test:', error);
    throw error;
  }
};

// Exam schedule functions
export const getExamSchedules = async (userId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('exam_schedules' as any)
      .select('*')
      .eq('user_id', userId)
      .order('date', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting exam schedules:', error);
    return [];
  }
};

export const createExamSchedule = async (examData: any): Promise<any> => {
  try {
    const { data, error } = await supabase
      .from('exam_schedules' as any)
      .insert({
        ...examData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating exam schedule:', error);
    throw error;
  }
};

export const updateExamSchedule = async (examId: string, updates: any): Promise<any> => {
  try {
    const { data, error } = await supabase
      .from('exam_schedules' as any)
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', examId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating exam schedule:', error);
    throw error;
  }
};

export const deleteExamSchedule = async (examId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('exam_schedules' as any)
      .delete()
      .eq('id', examId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting exam schedule:', error);
    throw error;
  }
};

// Extended user profile functions
export const getExtendedUserProfile = async (userId: string): Promise<any> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  } catch (error) {
    console.error('Error getting extended user profile:', error);
    return null;
  }
};

export const updateUserProfilePicture = async (userId: string, file: File): Promise<UserRow> => {
  try {
    // Import uploadImageToCloudinary function
    const { uploadImageToCloudinary } = await import('./imageUtils');

    // Upload image to Cloudinary first
    const uploadResponse = await uploadImageToCloudinary(file, {
      folder: 'profile-pictures',
      maxFileSize: 5 * 1024 * 1024 // 5MB
    });

    const photoUrl = uploadResponse.secure_url;

    const { data, error } = await supabase
      .from('users')
      .update({
        photo_url: photoUrl,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user profile picture:', error);
    throw error;
  }
};

export const updateUserBackgroundImage = async (userId: string, file: File): Promise<string> => {
  try {
    // Import uploadImageToCloudinary function
    const { uploadImageToCloudinary } = await import('./imageUtils');

    // Upload image to Cloudinary first
    const uploadResponse = await uploadImageToCloudinary(file, {
      folder: 'background-images',
      maxFileSize: 10 * 1024 * 1024 // 10MB
    });

    const backgroundImageUrl = uploadResponse.secure_url;

    const { data, error } = await supabase
      .from('users')
      .update({
        backgroundImage: backgroundImageUrl,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return backgroundImageUrl;
  } catch (error) {
    console.error('Error updating user background image:', error);
    throw error;
  }
};

export const checkUsernameAvailability = async (username: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('username', username)
      .single();

    if (error && error.code === 'PGRST116') {
      // No rows returned, username is available
      return true;
    }

    if (error) throw error;

    // If we get data, username is taken
    return data ? false : true;
  } catch (error) {
    console.error('Error checking username availability:', error);
    return false;
  }
};

// Authentication provider functions
export const linkWithEmailPassword = async (password: string): Promise<any> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.email) throw new Error('No user email found');

    const { data, error } = await supabase.auth.updateUser({
      password: password
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error linking with email/password:', error);
    throw error;
  }
};

export const linkWithGoogle = async (): Promise<any> => {
  try {
    const { data, error } = await supabase.auth.linkIdentity({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      }
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error linking with Google:', error);
    throw error;
  }
};

export const unlinkProvider = async (providerId: string): Promise<void> => {
  try {
    // Note: Supabase doesn't currently support unlinking identities
    // This is a placeholder for future implementation
    throw new Error('Provider unlinking is not currently supported by Supabase');
  } catch (error) {
    console.error('Error unlinking provider:', error);
    throw error;
  }
};

export const getUserProviders = async (): Promise<string[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    // Extract provider information from user identities
    const providers = user.identities?.map((identity: any) => identity.provider as string) || [];
    return [...new Set(providers)]; // Remove duplicates
  } catch (error) {
    console.error('Error getting user providers:', error);
    return [];
  }
};
