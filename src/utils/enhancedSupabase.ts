import { supabase } from '../integrations/supabase/client';
import { EnhancedTodoItem, Subject, TodoColumn, PresetExam } from '../types/todo';

/**
 * Advanced Supabase utilities for comprehensive task management
 * Includes all enhanced features with proper Supabase compatibility
 */

// Enhanced error handling with retry logic
class SupabaseTaskError extends Error {
  constructor(message: string, public code?: string, public details?: any) {
    super(message);
    this.name = 'SupabaseTaskError';
  }
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 5000,
};

// Retry wrapper for Supabase operations
async function withRetry<T>(
  operation: () => Promise<T>,
  context: string,
  retries = RETRY_CONFIG.maxRetries
): Promise<T> {
  try {
    return await operation();
  } catch (error: any) {
    console.error(`${context} failed:`, error);

    if (retries > 0 && (error.code === 'PGRST301' || error.message?.includes('timeout'))) {
      const delay = Math.min(
        RETRY_CONFIG.baseDelay * (RETRY_CONFIG.maxRetries - retries + 1),
        RETRY_CONFIG.maxDelay
      );
      console.log(`Retrying ${context} in ${delay}ms... (${retries} retries left)`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return withRetry(operation, context, retries - 1);
    }

    throw new SupabaseTaskError(
      `${context} failed: ${error.message}`,
      error.code,
      error
    );
  }
}

// Enhanced authentication with session management
const ensureAuthenticated = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    console.error('Authentication error:', error);
    throw new SupabaseTaskError('Authentication failed', 'AUTH_ERROR', error);
  }

  if (!session) {
    // Try to refresh the session
    const { data: { session: refreshedSession }, error: refreshError } =
      await supabase.auth.refreshSession();

    if (refreshError || !refreshedSession) {
      throw new SupabaseTaskError('User not authenticated', 'NO_SESSION');
    }

    return refreshedSession;
  }

  return session;
};

// Data transformation utilities
const transformRowToItem = (row: any): EnhancedTodoItem => {
  return {
    id: row.id,
    title: row.title,
    description: row.description || '',
    priority: (row.priority as 'high' | 'medium' | 'low') || 'medium',
    createdAt: row.created_at,
    updatedAt: row.updated_at,
    createdBy: row.created_by,
    dueDate: row.due_date,
    assignedTo: row.assigned_to,
    assignedToName: row.assigned_to_name,
    assignedToPhotoURL: row.assigned_to_photo_url,
    groupId: row.group_id,
    columnId: row.column_id || 'column-1',
    // Enhanced fields
    parentId: row.parent_id,
    subjectId: row.subject_id,
    examId: row.exam_id,
    tags: row.tags || [],
    chapterTags: row.chapter_tags || [],
    difficultyLevel: (row.difficulty_level as 'easy' | 'medium' | 'hard') || 'medium',
    timeEstimate: row.time_estimate,
    actualTimeSpent: row.actual_time_spent,
    completionPercentage: row.completion_percentage || 0,
    notes: row.notes,
    viewCount: row.view_count || 0,
    lastViewed: row.last_viewed,
    // Computed fields (will be calculated separately)
    depth: 0,
    subtasks: [],
    hasSubtasks: false,
    isOverdue: !!(row.due_date && row.due_date < Date.now()),
  };
};

const transformItemToRow = (item: Partial<EnhancedTodoItem>) => {
  const row: any = {};

  // Basic fields
  if (item.id !== undefined) row.id = item.id;
  if (item.title !== undefined) row.title = item.title;
  if (item.description !== undefined) row.description = item.description;
  if (item.priority !== undefined) row.priority = item.priority;
  if (item.createdAt !== undefined) row.created_at = item.createdAt;
  if (item.updatedAt !== undefined) row.updated_at = item.updatedAt;
  if (item.createdBy !== undefined) row.created_by = item.createdBy;
  if (item.dueDate !== undefined) row.due_date = item.dueDate;
  if (item.assignedTo !== undefined) row.assigned_to = item.assignedTo;
  if (item.assignedToName !== undefined) row.assigned_to_name = item.assignedToName;
  if (item.assignedToPhotoURL !== undefined) row.assigned_to_photo_url = item.assignedToPhotoURL;
  if (item.groupId !== undefined) row.group_id = item.groupId;
  if (item.columnId !== undefined) row.column_id = item.columnId;

  // Enhanced fields
  if (item.parentId !== undefined) row.parent_id = item.parentId;
  if (item.subjectId !== undefined) row.subject_id = item.subjectId;
  if (item.examId !== undefined) row.exam_id = item.examId;
  if (item.tags !== undefined) row.tags = item.tags;
  if (item.chapterTags !== undefined) row.chapter_tags = item.chapterTags;
  if (item.difficultyLevel !== undefined) row.difficulty_level = item.difficultyLevel;
  if (item.timeEstimate !== undefined) row.time_estimate = item.timeEstimate;
  if (item.actualTimeSpent !== undefined) row.actual_time_spent = item.actualTimeSpent;
  if (item.completionPercentage !== undefined) row.completion_percentage = item.completionPercentage;
  if (item.notes !== undefined) row.notes = item.notes;
  if (item.viewCount !== undefined) row.view_count = item.viewCount;
  if (item.lastViewed !== undefined) row.last_viewed = item.lastViewed;

  return row;
};

// Validation utilities
const validateEnhancedTodo = (todo: Partial<EnhancedTodoItem>, isUpdate = false): string[] => {
  const errors: string[] = [];

  if (!isUpdate && !todo.title?.trim()) {
    errors.push('Title is required');
  }

  if (todo.title && todo.title.length > 500) {
    errors.push('Title must be less than 500 characters');
  }

  if (todo.description && todo.description.length > 5000) {
    errors.push('Description must be less than 5000 characters');
  }

  if (todo.priority && !['low', 'medium', 'high'].includes(todo.priority)) {
    errors.push('Priority must be low, medium, or high');
  }

  if (todo.difficultyLevel && !['easy', 'medium', 'hard'].includes(todo.difficultyLevel)) {
    errors.push('Difficulty level must be easy, medium, or hard');
  }

  if (todo.completionPercentage !== undefined &&
      (todo.completionPercentage < 0 || todo.completionPercentage > 100)) {
    errors.push('Completion percentage must be between 0 and 100');
  }

  if (todo.timeEstimate !== undefined && todo.timeEstimate < 0) {
    errors.push('Time estimate must be positive');
  }

  if (todo.actualTimeSpent !== undefined && todo.actualTimeSpent < 0) {
    errors.push('Actual time spent must be positive');
  }

  return errors;
};

// Computed fields calculation
const calculateComputedFields = (todos: EnhancedTodoItem[]): EnhancedTodoItem[] => {
  const todoMap = new Map(todos.map(todo => [todo.id, todo]));

  return todos.map(todo => {
    // Calculate depth
    let depth = 0;
    let currentId = todo.parentId;
    const visited = new Set<string>();

    while (currentId && !visited.has(currentId)) {
      visited.add(currentId);
      const parent = todoMap.get(currentId);
      if (parent) {
        depth++;
        currentId = parent.parentId;
      } else {
        break;
      }
    }

    // Find subtasks
    const subtasks = todos
      .filter(t => t.parentId === todo.id)
      .map(t => t.id);

    return {
      ...todo,
      depth,
      subtasks,
      hasSubtasks: subtasks.length > 0,
      isOverdue: !!(todo.dueDate && todo.dueDate < Date.now()),
    };
  });
};

/**
 * Advanced getTodos function with comprehensive features
 */
export const getEnhancedTodos = async (userId: string): Promise<EnhancedTodoItem[]> => {
  return withRetry(async () => {
    const session = await ensureAuthenticated();
    console.log('Fetching enhanced todos for user:', userId);

    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('created_by', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new SupabaseTaskError('Failed to fetch todos', error.code, error);
    }

    console.log('Fetched enhanced todos:', data?.length || 0, 'items');

    // Transform and calculate computed fields
    const todos = (data || []).map(transformRowToItem);
    return calculateComputedFields(todos);
  }, 'getEnhancedTodos');
};

/**
 * Enhanced createTodo function with validation and new columns
 */
export const createEnhancedTodo = async (todoData: Partial<EnhancedTodoItem>): Promise<EnhancedTodoItem> => {
  try {
    const session = await ensureAuthenticated();

    // Create a basic todo with only essential fields using correct column names
    const basicTodoData = {
      id: todoData.id || `todo_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title: todoData.title || 'Untitled Task',
      description: todoData.description || '',
      priority: (todoData.priority as 'high' | 'medium' | 'low') || 'medium',
      created_at: Date.now(),
      updated_at: Date.now(),
      created_by: session.user.id,
      column_id: todoData.columnId || 'column-1',
      // Only include optional fields if they exist
      ...(todoData.dueDate && { due_date: todoData.dueDate }),
      ...(todoData.assignedTo && { assigned_to: todoData.assignedTo }),
      ...(todoData.assignedToName && { assigned_to_name: todoData.assignedToName }),
      ...(todoData.assignedToPhotoURL && { assigned_to_photo_url: todoData.assignedToPhotoURL }),
      ...(todoData.groupId && { group_id: todoData.groupId }),
      // Enhanced fields
      ...(todoData.parentId && { parent_id: todoData.parentId }),
      ...(todoData.subjectId && { subject_id: todoData.subjectId }),
      ...(todoData.examId && { exam_id: todoData.examId }),
      ...(todoData.tags && { tags: todoData.tags }),
      ...(todoData.chapterTags && { chapter_tags: todoData.chapterTags }),
      ...(todoData.difficultyLevel && { difficulty_level: todoData.difficultyLevel }),
      ...(todoData.timeEstimate && { time_estimate: todoData.timeEstimate }),
      ...(todoData.actualTimeSpent && { actual_time_spent: todoData.actualTimeSpent }),
      ...(todoData.completionPercentage !== undefined && { completion_percentage: todoData.completionPercentage }),
      ...(todoData.notes && { notes: todoData.notes }),
      ...(todoData.viewCount !== undefined && { view_count: todoData.viewCount }),
      ...(todoData.lastViewed && { last_viewed: todoData.lastViewed }),
    };

    console.log('Creating basic todo (avoiding enhanced fields for now):', basicTodoData);

    const { data, error } = await supabase
      .from('todos')
      .insert(basicTodoData)
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating todo:', error);
      throw error;
    }

    console.log('Todo created successfully:', data);

    // Convert to enhanced todo item format using transformRowToItem
    const enhancedTodo = transformRowToItem(data);

    return enhancedTodo;
  } catch (error) {
    console.error('Error creating todo:', error);
    throw error;
  }
};

/**
 * Enhanced updateTodo function with validation and new columns
 */
export const updateEnhancedTodo = async (
  todoId: string,
  updates: Partial<EnhancedTodoItem>
): Promise<EnhancedTodoItem> => {
  try {
    const session = await ensureAuthenticated();
    console.log('User authenticated:', session.user.id);

    // Check if the task exists and user has permission to update it
    const { data: existingTask, error: fetchError } = await supabase
      .from('todos')
      .select('id, created_by, title')
      .eq('id', todoId)
      .single();

    if (fetchError) {
      console.error('Error fetching task for update:', JSON.stringify(fetchError, null, 2));
      throw new Error(`Task not found: ${fetchError.message}`);
    }

    if (!existingTask) {
      throw new Error('Task not found');
    }

    console.log('Existing task:', existingTask);
    console.log('Current user can update?', existingTask.created_by === session.user.id);

    // Validate updates (this is an update operation)
    const validationErrors = validateEnhancedTodo(updates, true);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Convert to database format
    console.log('Original updates:', JSON.stringify(updates, null, 2));
    const rowUpdates = transformItemToRow(updates);
    console.log('Converted row updates:', JSON.stringify(rowUpdates, null, 2));

    // Remove any undefined values to avoid Supabase issues
    const cleanedUpdates = Object.fromEntries(
      Object.entries(rowUpdates).filter(([_, value]) => value !== undefined)
    );

    // Add updated timestamp
    const updateData = {
      ...cleanedUpdates,
      updated_at: Date.now(),
    };

    console.log('Updating enhanced todo:', todoId);
    console.log('Update data being sent:', JSON.stringify(updateData, null, 2));

    const { data, error } = await supabase
      .from('todos')
      .update(updateData)
      .eq('id', todoId)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating enhanced todo:', JSON.stringify(error, null, 2));
      console.error('Failed update data:', JSON.stringify(updateData, null, 2));
      console.error('Todo ID that failed:', todoId);
      throw new Error(`Supabase update failed: ${error.message || error.details || JSON.stringify(error)}`);
    }

    console.log('Enhanced todo updated successfully:', data);

    // Convert back to enhanced todo item
    return transformRowToItem(data);
  } catch (error) {
    console.error('Error updating enhanced todo:', JSON.stringify(error, null, 2));
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error(`Update operation failed: ${JSON.stringify(error)}`);
    }
  }
};

/**
 * Simple test function to update only completion percentage
 */
export const updateTaskCompletion = async (
  todoId: string,
  completionPercentage: number
): Promise<void> => {
  try {
    const session = await ensureAuthenticated();
    console.log('Updating task completion - User:', session.user.id, 'Task:', todoId, 'Completion:', completionPercentage);

    const { data, error } = await supabase
      .from('todos')
      .update({
        completion_percentage: completionPercentage,
        updated_at: Date.now()
      })
      .eq('id', todoId)
      .select()
      .single();

    if (error) {
      console.error('Simple completion update failed:', JSON.stringify(error, null, 2));
      throw new Error(`Failed to update completion: ${error.message || JSON.stringify(error)}`);
    }

    console.log('Simple completion update successful:', data);
  } catch (error) {
    console.error('Error in updateTaskCompletion:', JSON.stringify(error, null, 2));
    throw error;
  }
};

/**
 * Enhanced deleteTodo function with cascade handling for subtasks
 */
export const deleteEnhancedTodo = async (todoId: string, deleteSubtasks: boolean = false): Promise<void> => {
  try {
    const session = await ensureAuthenticated();
    console.log('Deleting enhanced todo:', todoId, 'deleteSubtasks:', deleteSubtasks);

    if (deleteSubtasks) {
      // First, get all subtasks
      const { data: subtasks, error: subtasksError } = await supabase
        .from('todos')
        .select('id')
        .eq('parent_id', todoId);

      if (subtasksError) {
        console.error('Error fetching subtasks:', subtasksError);
        throw subtasksError;
      }

      // Delete all subtasks recursively
      if (subtasks && subtasks.length > 0) {
        for (const subtask of subtasks) {
          await deleteEnhancedTodo(subtask.id, true);
        }
      }
    } else {
      // Note: Parent ID operations are skipped until the todos table is updated with enhanced columns
      console.log('Skipping parent ID update - enhanced columns not available in current todos table');
    }

    // Delete the main task
    const { error } = await supabase
      .from('todos')
      .delete()
      .eq('id', todoId);

    if (error) {
      console.error('Supabase error deleting enhanced todo:', error);
      throw error;
    }

    console.log('Enhanced todo deleted successfully:', todoId);
  } catch (error) {
    console.error('Error deleting enhanced todo:', error);
    throw error;
  }
};

/**
 * Get user subjects for task integration
 */
export const getUserSubjects = async (userId: string): Promise<Subject[]> => {
  try {
    const session = await ensureAuthenticated();
    console.log('Fetching user subjects for:', userId);

    const { data, error } = await supabase
      .from('userSubjects')
      .select('*')
      .eq('userId', userId) // Fixed: use camelCase field name
      .order('name', { ascending: true });

    if (error) {
      console.error('Supabase error getting user subjects:', error);
      throw error;
    }

    console.log('Fetched user subjects:', data?.length || 0, 'items');
    return data || [];
  } catch (error) {
    console.error('Error getting user subjects:', error);
    return [];
  }
};

// Note: getUserExams removed - now using preset exams instead of database exams

/**
 * Enhanced subscription for real-time updates
 */
export const subscribeToEnhancedTodos = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('enhanced-todos-changes')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'todos',
        filter: `createdBy=eq.${userId}`
      },
      (payload) => {
        console.log('Enhanced todo real-time update:', payload);
        callback(payload);
      }
    )
    .subscribe();
};

/**
 * Bulk operations for enhanced todos
 */
export const bulkUpdateEnhancedTodos = async (
  todoIds: string[],
  updates: Partial<EnhancedTodoItem>
): Promise<EnhancedTodoItem[]> => {
  try {
    await ensureAuthenticated();

    // Validate updates (this is an update operation)
    const validationErrors = validateEnhancedTodo(updates, true);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Convert to database format
    const rowUpdates = transformItemToRow(updates);
    const updateData = {
      ...rowUpdates,
      updated_at: Date.now(),
    };

    console.log('Bulk updating enhanced todos:', todoIds, updateData);

    const { data, error } = await supabase
      .from('todos')
      .update(updateData)
      .in('id', todoIds)
      .select();

    if (error) {
      console.error('Supabase error bulk updating enhanced todos:', error);
      throw error;
    }

    console.log('Enhanced todos bulk updated successfully:', data?.length || 0, 'items');

    // Convert back to enhanced todo items
    const todos = (data || []).map(transformRowToItem);
    return calculateComputedFields(todos);
  } catch (error) {
    console.error('Error bulk updating enhanced todos:', error);
    throw error;
  }
};

/**
 * Advanced hierarchical task operations
 */
export const getTodosWithHierarchy = async (userId: string): Promise<EnhancedTodoItem[]> => {
  return withRetry(async () => {
    const [todos, subjects] = await Promise.all([
      getEnhancedTodos(userId),
      getUserSubjects(userId)
    ]);

    // Create lookup maps
    const subjectsMap = subjects.reduce((acc, subject) => {
      acc[subject.id] = subject;
      return acc;
    }, {} as Record<string, Subject>);

    // Enhance todos with subject information
    return todos.map(todo => ({
      ...todo,
      subjectName: todo.subjectId && subjectsMap[todo.subjectId]
        ? subjectsMap[todo.subjectId].name
        : undefined,
      subjectColor: todo.subjectId && subjectsMap[todo.subjectId]
        ? subjectsMap[todo.subjectId].color
        : undefined,
      examName: todo.examId || undefined,
    }));
  }, 'getTodosWithHierarchy');
};

/**
 * Advanced task analytics and insights
 */
export const getTaskAnalytics = async (userId: string): Promise<{
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  tasksByPriority: Record<string, number>;
  tasksBySubject: Record<string, number>;
  tasksByDifficulty: Record<string, number>;
  averageCompletionTime: number;
  productivityTrends: Array<{ date: string; completed: number; created: number }>;
}> => {
  return withRetry(async () => {
    const todos = await getEnhancedTodos(userId);
    const now = Date.now();

    const analytics = {
      totalTasks: todos.length,
      completedTasks: todos.filter(t => t.completionPercentage === 100).length,
      overdueTasks: todos.filter(t => t.dueDate && t.dueDate < now && t.completionPercentage < 100).length,
      tasksByPriority: todos.reduce((acc, t) => {
        acc[t.priority] = (acc[t.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      tasksBySubject: todos.reduce((acc, t) => {
        if (t.subjectId) {
          acc[t.subjectId] = (acc[t.subjectId] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>),
      tasksByDifficulty: todos.reduce((acc, t) => {
        acc[t.difficultyLevel] = (acc[t.difficultyLevel] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      averageCompletionTime: todos
        .filter(t => t.actualTimeSpent)
        .reduce((sum, t) => sum + (t.actualTimeSpent || 0), 0) /
        Math.max(1, todos.filter(t => t.actualTimeSpent).length),
      productivityTrends: calculateProductivityTrends(todos),
    };

    return analytics;
  }, 'getTaskAnalytics');
};

const calculateProductivityTrends = (todos: EnhancedTodoItem[]) => {
  const trends: Record<string, { completed: number; created: number }> = {};

  todos.forEach(todo => {
    const createdDate = new Date(todo.createdAt).toISOString().split('T')[0];
    if (!trends[createdDate]) {
      trends[createdDate] = { completed: 0, created: 0 };
    }
    trends[createdDate].created++;

    if (todo.completionPercentage === 100) {
      trends[createdDate].completed++;
    }
  });

  return Object.entries(trends)
    .map(([date, data]) => ({ date, ...data }))
    .sort((a, b) => a.date.localeCompare(b.date));
};

/**
 * Advanced task search and filtering
 */
export const searchTasks = async (
  userId: string,
  query: string,
  filters: {
    priority?: string[];
    subjects?: string[];
    tags?: string[];
    difficulty?: string[];
    dateRange?: { start: number; end: number };
    completionRange?: { min: number; max: number };
    hasSubtasks?: boolean;
    isOverdue?: boolean;
  } = {}
): Promise<EnhancedTodoItem[]> => {
  return withRetry(async () => {
    let todos = await getEnhancedTodos(userId);

    // Text search
    if (query.trim()) {
      const searchTerm = query.toLowerCase();
      todos = todos.filter(todo =>
        todo.title.toLowerCase().includes(searchTerm) ||
        todo.description.toLowerCase().includes(searchTerm) ||
        todo.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        todo.chapterTags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        (todo.notes && todo.notes.toLowerCase().includes(searchTerm))
      );
    }

    // Apply filters
    if (filters.priority?.length) {
      todos = todos.filter(todo => filters.priority!.includes(todo.priority));
    }

    if (filters.subjects?.length) {
      todos = todos.filter(todo => todo.subjectId && filters.subjects!.includes(todo.subjectId));
    }

    if (filters.tags?.length) {
      todos = todos.filter(todo =>
        filters.tags!.some(tag =>
          todo.tags.includes(tag) || todo.chapterTags.includes(tag)
        )
      );
    }

    if (filters.difficulty?.length) {
      todos = todos.filter(todo => filters.difficulty!.includes(todo.difficultyLevel));
    }

    if (filters.dateRange) {
      todos = todos.filter(todo =>
        todo.createdAt >= filters.dateRange!.start &&
        todo.createdAt <= filters.dateRange!.end
      );
    }

    if (filters.completionRange) {
      todos = todos.filter(todo =>
        todo.completionPercentage >= filters.completionRange!.min &&
        todo.completionPercentage <= filters.completionRange!.max
      );
    }

    if (filters.hasSubtasks !== undefined) {
      todos = todos.filter(todo => todo.hasSubtasks === filters.hasSubtasks);
    }

    if (filters.isOverdue !== undefined) {
      todos = todos.filter(todo => todo.isOverdue === filters.isOverdue);
    }

    return todos;
  }, 'searchTasks');
};

/**
 * Advanced task templates and automation
 */
export const createTaskFromTemplate = async (
  userId: string,
  template: {
    name: string;
    tasks: Partial<EnhancedTodoItem>[];
    settings: {
      autoAssignDueDates?: boolean;
      daysBetweenTasks?: number;
      startDate?: number;
    };
  }
): Promise<EnhancedTodoItem[]> => {
  return withRetry(async () => {
    const session = await ensureAuthenticated();
    const createdTasks: EnhancedTodoItem[] = [];

    for (let i = 0; i < template.tasks.length; i++) {
      const taskTemplate = template.tasks[i];
      let dueDate = taskTemplate.dueDate;

      // Auto-assign due dates if enabled
      if (template.settings.autoAssignDueDates && template.settings.startDate) {
        const daysOffset = (template.settings.daysBetweenTasks || 1) * i;
        dueDate = template.settings.startDate + (daysOffset * 24 * 60 * 60 * 1000);
      }

      const taskData = {
        ...taskTemplate,
        id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: session.user.id,
        dueDate,
        columnId: taskTemplate.columnId || 'column-1',
      };

      const createdTask = await createEnhancedTodo(taskData);
      createdTasks.push(createdTask);
    }

    return createdTasks;
  }, 'createTaskFromTemplate');
};



/**
 * Export/Import functionality
 */
export const exportTasks = async (userId: string, format: 'json' | 'csv' = 'json') => {
  const todos = await getEnhancedTodos(userId);

  if (format === 'json') {
    return JSON.stringify(todos, null, 2);
  } else {
    // CSV format
    const headers = ['Title', 'Description', 'Priority', 'Status', 'Due Date', 'Subject', 'Tags'];
    const rows = todos.map(todo => [
      todo.title,
      todo.description,
      todo.priority,
      `${todo.completionPercentage}%`,
      todo.dueDate ? new Date(todo.dueDate).toLocaleDateString() : '',
      todo.subjectId || '',
      todo.tags.join('; ')
    ]);

    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  }
};

export const importTasks = async (userId: string, data: string, format: 'json' | 'csv' = 'json') => {
  const session = await ensureAuthenticated();

  let tasksToImport: Partial<EnhancedTodoItem>[] = [];

  if (format === 'json') {
    tasksToImport = JSON.parse(data);
  } else {
    // Parse CSV
    const lines = data.split('\n');
    const headers = lines[0].split(',').map(h => h.replace(/"/g, ''));

    tasksToImport = lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.replace(/"/g, ''));
      return {
        title: values[0],
        description: values[1],
        priority: values[2] as 'low' | 'medium' | 'high',
        tags: values[6] ? values[6].split('; ') : [],
      };
    });
  }

  const importedTasks = [];
  for (const taskData of tasksToImport) {
    const task = await createEnhancedTodo({
      ...taskData,
      createdBy: session.user.id,
      columnId: 'column-1',
    });
    importedTasks.push(task);
  }

  return importedTasks;
};

/**
 * Advanced task collaboration features
 */
export const assignTaskToUser = async (
  taskId: string,
  assignedTo: string,
  assignedToName: string,
  assignedToPhotoURL?: string
): Promise<EnhancedTodoItem> => {
  return withRetry(async () => {
    const updates = {
      assignedTo,
      assignedToName,
      assignedToPhotoURL,
      updatedAt: Date.now(),
    };

    return await updateEnhancedTodo(taskId, updates);
  }, 'assignTaskToUser');
};

export const unassignTask = async (taskId: string): Promise<EnhancedTodoItem> => {
  return withRetry(async () => {
    const updates = {
      assignedTo: undefined,
      assignedToName: undefined,
      assignedToPhotoURL: undefined,
      updatedAt: Date.now(),
    };

    return await updateEnhancedTodo(taskId, updates);
  }, 'unassignTask');
};

/**
 * Advanced task time tracking
 */
export const startTaskTimer = async (taskId: string): Promise<void> => {
  return withRetry(async () => {
    const session = await ensureAuthenticated();

    // Store timer start time in local storage for persistence
    const timerData = {
      taskId,
      startTime: Date.now(),
      userId: session.user.id,
    };

    localStorage.setItem('activeTaskTimer', JSON.stringify(timerData));

    // Update view count and last viewed
    await updateEnhancedTodo(taskId, {
      viewCount: undefined, // Will be incremented in the update function
      lastViewed: Date.now(),
    });
  }, 'startTaskTimer');
};

export const stopTaskTimer = async (taskId: string): Promise<number> => {
  return withRetry(async () => {
    const timerData = localStorage.getItem('activeTaskTimer');
    if (!timerData) {
      throw new SupabaseTaskError('No active timer found');
    }

    const { startTime, taskId: storedTaskId } = JSON.parse(timerData);
    if (storedTaskId !== taskId) {
      throw new SupabaseTaskError('Timer task ID mismatch');
    }

    const timeSpent = Date.now() - startTime;

    // Get current task to add to existing time
    const todos = await getEnhancedTodos((await ensureAuthenticated()).user.id);
    const currentTask = todos.find(t => t.id === taskId);
    const totalTimeSpent = (currentTask?.actualTimeSpent || 0) + timeSpent;

    // Update task with time spent
    await updateEnhancedTodo(taskId, {
      actualTimeSpent: totalTimeSpent,
      lastViewed: Date.now(),
    });

    // Clear timer
    localStorage.removeItem('activeTaskTimer');

    return timeSpent;
  }, 'stopTaskTimer');
};

/**
 * Advanced task dependencies and relationships
 */
export const addTaskDependency = async (
  taskId: string,
  dependsOnTaskId: string
): Promise<void> => {
  return withRetry(async () => {
    // Get current task
    const todos = await getEnhancedTodos((await ensureAuthenticated()).user.id);
    const task = todos.find(t => t.id === taskId);

    if (!task) {
      throw new SupabaseTaskError('Task not found');
    }

    // Add dependency to tags (using a special format)
    const dependencyTag = `depends:${dependsOnTaskId}`;
    const updatedTags = [...(task.tags || [])];

    if (!updatedTags.includes(dependencyTag)) {
      updatedTags.push(dependencyTag);
    }

    await updateEnhancedTodo(taskId, {
      tags: updatedTags,
    });
  }, 'addTaskDependency');
};

export const removeTaskDependency = async (
  taskId: string,
  dependsOnTaskId: string
): Promise<void> => {
  return withRetry(async () => {
    const todos = await getEnhancedTodos((await ensureAuthenticated()).user.id);
    const task = todos.find(t => t.id === taskId);

    if (!task) {
      throw new SupabaseTaskError('Task not found');
    }

    const dependencyTag = `depends:${dependsOnTaskId}`;
    const updatedTags = (task.tags || []).filter(tag => tag !== dependencyTag);

    await updateEnhancedTodo(taskId, {
      tags: updatedTags,
    });
  }, 'removeTaskDependency');
};
