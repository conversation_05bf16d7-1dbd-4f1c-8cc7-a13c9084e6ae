import { EnhancedTodoItem } from '../types/todo';
import { taskStorage } from './taskLocalStorage';
import { 
  getStudySessions, 
  saveStudySession, 
  getTodos,
  createTodo,
  updateTodo,
  deleteTodo,
  EnhancedStudySessionData,
  SessionFeedback
} from './supabase';

// Sync conflict types
export type SyncConflictType = 
  | 'time_mismatch' 
  | 'session_duplicate' 
  | 'task_not_found' 
  | 'data_corruption'
  | 'task_version_conflict'
  | 'session_integrity_error'
  | 'cross_device_conflict';

export interface SyncConflict {
  type: SyncConflictType;
  taskId: string;
  localData: any;
  remoteData: any;
  timestamp: number;
  severity: 'low' | 'medium' | 'high';
  autoResolvable: boolean;
  description: string;
}

// Offline operation types
export interface OfflineOperation {
  id: string;
  type: 'time_update' | 'session_save' | 'task_update' | 'task_create' | 'task_delete' | 'task_sync';
  taskId: string;
  data: any;
  timestamp: number;
  retryCount: number;
  priority: 'low' | 'medium' | 'high';
  userId: string;
}

// Sync status types
export type SyncStatus = 'synced' | 'pending' | 'conflict' | 'error' | 'offline';

// Data integrity validation result
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fixableIssues: string[];
}

// Backup data structure
export interface BackupData {
  version: string;
  timestamp: number;
  userId: string;
  tasks: EnhancedTodoItem[];
  studySessions: any[];
  metadata: {
    totalTasks: number;
    totalSessions: number;
    lastSyncTimestamp: number;
    deviceInfo: string;
  };
}

/**
 * Enhanced data synchronization utilities for coordinating between localStorage tasks and Supabase sessions
 * Provides robust sync mechanism, conflict resolution, offline operation queuing, and data integrity validation
 */
export class DataSyncUtilities {
  private static instance: DataSyncUtilities;
  private offlineQueue: OfflineOperation[] = [];
  private syncInProgress = false;
  private readonly OFFLINE_QUEUE_KEY = 'task_timer_offline_queue';
  private readonly BACKUP_KEY = 'task_timer_backup_data';
  private readonly SYNC_STATE_KEY = 'task_timer_sync_state';
  private readonly MAX_RETRY_COUNT = 3;
  private readonly SYNC_BATCH_SIZE = 10;
  private readonly BACKUP_RETENTION_DAYS = 30;

  private constructor() {
    this.loadOfflineQueue();
    this.setupNetworkListeners();
    this.setupPeriodicSync();
    this.setupDataIntegrityChecks();
  }

  public static getInstance(): DataSyncUtilities {
    if (!DataSyncUtilities.instance) {
      DataSyncUtilities.instance = new DataSyncUtilities();
    }
    return DataSyncUtilities.instance;
  }

  /**
   * Enhanced sync task time data with Supabase study sessions and todos table
   */
  async syncTaskWithTimer(taskId: string, userId: string): Promise<void> {
    try {
      if (this.syncInProgress) {
        console.log('Sync already in progress, queuing operation...');
        this.queueOfflineOperation({
          type: 'task_sync',
          taskId,
          data: { userId },
          priority: 'medium',
          userId
        });
        return;
      }

      this.syncInProgress = true;
      console.log(`Starting enhanced sync for task ${taskId}...`);

      // Get task from localStorage
      const localTask = taskStorage.getTask(userId, taskId);
      if (!localTask) {
        throw new Error('Task not found in localStorage');
      }

      // Get study sessions from Supabase for this task
      const allSessions = await getStudySessions(userId);
      const taskSessions = allSessions.filter(session => 
        session.task_name === localTask.title || 
        (session.feedback && session.feedback.includes(`"task_id":"${taskId}"`))
      );

      // Get remote task from Supabase todos table if it exists
      const remoteTodos = await getTodos(userId);
      const remoteTask = remoteTodos.find(todo => 
        todo.id === taskId || todo.title === localTask.title
      );

      // Perform comprehensive sync
      await this.performComprehensiveSync(localTask, remoteTask, taskSessions, userId);

      // Mark as synced
      taskStorage.markTimeTrackingSynced(userId, taskId);
      this.updateSyncState(userId, taskId, 'synced');

      console.log(`Enhanced sync completed for task ${taskId}`);

    } catch (error) {
      console.error('Error in enhanced sync:', error);
      this.updateSyncState(userId, taskId, 'error');
      
      // Queue for retry if it's a network error
      if (this.isNetworkError(error)) {
        this.queueOfflineOperation({
          type: 'task_sync',
          taskId,
          data: { userId },
          priority: 'high',
          userId
        });
      }
      
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Perform comprehensive sync between local task, remote task, and study sessions
   */
  private async performComprehensiveSync(
    localTask: EnhancedTodoItem,
    remoteTask: any | null,
    taskSessions: any[],
    userId: string
  ): Promise<void> {
    // Calculate time data from sessions
    const sessionTotalTime = taskSessions.reduce((total, session) => 
      total + (session.duration || 0), 0
    );
    const sessionTotalMinutes = Math.round(sessionTotalTime / 60);

    // Detect conflicts
    const conflicts = await this.detectSyncConflicts(localTask, remoteTask, taskSessions);
    
    if (conflicts.length > 0) {
      console.warn(`Detected ${conflicts.length} sync conflicts for task ${localTask.id}`);
      await this.resolveConflicts(userId, conflicts);
    }

    // Sync with Supabase todos table
    if (remoteTask) {
      // Update existing remote task
      await this.syncWithRemoteTask(localTask, remoteTask, sessionTotalMinutes, userId);
    } else {
      // Create new remote task if it doesn't exist
      await this.createRemoteTask(localTask, sessionTotalMinutes, userId);
    }

    // Update local task with session data
    const updatedTask = {
      ...localTask,
      studySessionIds: taskSessions.map(session => session.id),
      actualTimeSpent: Math.max(sessionTotalMinutes, localTask.actualTimeSpent || 0),
      timeTrackingSync: {
        lastSyncTimestamp: Date.now(),
        syncStatus: 'synced' as const,
        pendingTimeUpdates: 0
      }
    };

    taskStorage.saveTask(userId, updatedTask);
  }

  /**
   * Detect sync conflicts between local and remote data
   */
  private async detectSyncConflicts(
    localTask: EnhancedTodoItem,
    remoteTask: any | null,
    taskSessions: any[]
  ): Promise<SyncConflict[]> {
    const conflicts: SyncConflict[] = [];

    if (!remoteTask) {
      return conflicts; // No remote task, no conflicts
    }

    // Time mismatch conflict
    const sessionTotalMinutes = Math.round(
      taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60
    );
    const localTimeMinutes = localTask.actualTimeSpent || 0;
    const remoteTimeMinutes = remoteTask.actual_time_spent || 0;

    if (Math.abs(sessionTotalMinutes - localTimeMinutes) > 2 || 
        Math.abs(sessionTotalMinutes - remoteTimeMinutes) > 2) {
      conflicts.push({
        type: 'time_mismatch',
        taskId: localTask.id,
        localData: { time: localTimeMinutes },
        remoteData: { time: remoteTimeMinutes, sessionTime: sessionTotalMinutes },
        timestamp: Date.now(),
        severity: 'medium',
        autoResolvable: true,
        description: `Time mismatch: Local=${localTimeMinutes}min, Remote=${remoteTimeMinutes}min, Sessions=${sessionTotalMinutes}min`
      });
    }

    // Version conflict (based on updated_at timestamps)
    if (localTask.updatedAt && remoteTask.updated_at && 
        Math.abs(localTask.updatedAt - remoteTask.updated_at) > 60000) { // 1 minute tolerance
      conflicts.push({
        type: 'task_version_conflict',
        taskId: localTask.id,
        localData: { updatedAt: localTask.updatedAt, title: localTask.title },
        remoteData: { updatedAt: remoteTask.updated_at, title: remoteTask.title },
        timestamp: Date.now(),
        severity: 'high',
        autoResolvable: false,
        description: 'Task has been modified on multiple devices'
      });
    }

    // Session integrity check
    const duplicateSessions = this.findDuplicateSessions(taskSessions);
    if (duplicateSessions.length > 0) {
      conflicts.push({
        type: 'session_duplicate',
        taskId: localTask.id,
        localData: { sessionIds: localTask.studySessionIds },
        remoteData: { duplicates: duplicateSessions },
        timestamp: Date.now(),
        severity: 'low',
        autoResolvable: true,
        description: `Found ${duplicateSessions.length} duplicate sessions`
      });
    }

    return conflicts;
  }

  /**
   * Handle timer state changes and update task accordingly
   */
  async handleTimerStateChange(taskId: string, userId: string, timerState: any): Promise<void> {
    try {
      const task = taskStorage.getTask(userId, taskId);
      if (!task) {
        console.warn('Task not found for timer state change:', taskId);
        return;
      }

      // Update last timer session info
      const sessionInfo = {
        sessionId: timerState.sessionId || `session_${Date.now()}`,
        startTime: timerState.sessionStartTime?.getTime ? timerState.sessionStartTime.getTime() : timerState.sessionStartTime || Date.now(),
        endTime: timerState.status === 'completed' || timerState.status === 'idle' ? Date.now() : undefined,
        status: timerState.status || 'active'
      };

      taskStorage.updateLastTimerSession(userId, taskId, sessionInfo);

      // If timer completed or stopped, sync the data
      if (timerState.status === 'completed' || timerState.status === 'idle') {
        await this.syncTaskWithTimer(taskId, userId);
      }

      // Store timer state for cross-device sync
      this.storeTimerStateForSync(userId, taskId, timerState);

    } catch (error) {
      console.error('Error handling timer state change:', error);
      // Don't throw - this is a background operation
    }
  }

  /**
   * Store timer state for cross-device synchronization
   */
  private storeTimerStateForSync(userId: string, taskId: string, timerState: any): void {
    try {
      const syncKey = `timer_sync_${userId}`;
      const syncData = {
        taskId,
        status: timerState.status,
        sessionId: timerState.sessionId,
        startTime: timerState.sessionStartTime?.getTime ? timerState.sessionStartTime.getTime() : timerState.sessionStartTime,
        pausedDuration: timerState.pausedDuration || 0,
        lastUpdate: Date.now(),
      };

      localStorage.setItem(syncKey, JSON.stringify(syncData));
    } catch (error) {
      console.error('Error storing timer state for sync:', error);
    }
  }

  /**
   * Get stored timer state for cross-device synchronization
   */
  getStoredTimerState(userId: string): any {
    try {
      const syncKey = `timer_sync_${userId}`;
      const syncData = localStorage.getItem(syncKey);
      return syncData ? JSON.parse(syncData) : null;
    } catch (error) {
      console.error('Error getting stored timer state:', error);
      return null;
    }
  }

  /**
   * Clear stored timer state
   */
  clearStoredTimerState(userId: string): void {
    try {
      const syncKey = `timer_sync_${userId}`;
      localStorage.removeItem(syncKey);
    } catch (error) {
      console.error('Error clearing stored timer state:', error);
    }
  }

  /**
   * Enhanced conflict resolution with multiple strategies
   */
  async resolveConflicts(userId: string, conflicts: SyncConflict[]): Promise<void> {
    console.log(`Resolving ${conflicts.length} conflicts for user ${userId}`);

    for (const conflict of conflicts) {
      try {
        console.log(`Resolving conflict: ${conflict.type} - ${conflict.description}`);

        switch (conflict.type) {
          case 'time_mismatch':
            await this.resolveTimeMismatchConflict(userId, conflict);
            break;
          
          case 'session_duplicate':
            await this.resolveSessionDuplicateConflict(userId, conflict);
            break;
          
          case 'task_not_found':
            await this.resolveTaskNotFoundConflict(userId, conflict);
            break;

          case 'task_version_conflict':
            await this.resolveTaskVersionConflict(userId, conflict);
            break;

          case 'session_integrity_error':
            await this.resolveSessionIntegrityConflict(userId, conflict);
            break;

          case 'cross_device_conflict':
            await this.resolveCrossDeviceConflict(userId, conflict);
            break;
          
          default:
            console.warn('Unknown conflict type:', conflict.type);
            await this.handleUnknownConflict(userId, conflict);
        }

        console.log(`Successfully resolved conflict: ${conflict.type}`);
      } catch (error) {
        console.error('Error resolving conflict:', conflict, error);
        await this.logConflictResolutionFailure(userId, conflict, error);
      }
    }
  }

  /**
   * Enhanced queue operation for offline processing with deduplication
   */
  queueOfflineOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): void {
    // Check for duplicate operations
    const existingOp = this.offlineQueue.find(op => 
      op.type === operation.type && 
      op.taskId === operation.taskId && 
      op.userId === operation.userId
    );

    if (existingOp) {
      // Update existing operation with new data and reset retry count
      existingOp.data = { ...existingOp.data, ...operation.data };
      existingOp.timestamp = Date.now();
      existingOp.retryCount = 0;
      existingOp.priority = operation.priority;
      console.log('Updated existing queued operation:', existingOp);
    } else {
      // Create new operation
      const offlineOp: OfflineOperation = {
        ...operation,
        id: `offline_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: Date.now(),
        retryCount: 0
      };

      this.offlineQueue.push(offlineOp);
      console.log('Queued new offline operation:', offlineOp);
    }

    this.saveOfflineQueue();
    
    // Auto-process queue if online
    if (navigator.onLine) {
      setTimeout(() => this.processOfflineQueue(), 1000);
    }
  }

  /**
   * Enhanced offline queue processing with batching and prioritization
   */
  async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) {
      return;
    }

    console.log(`Processing ${this.offlineQueue.length} offline operations...`);

    // Sort operations by priority and timestamp
    const sortedOperations = [...this.offlineQueue].sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
    });

    // Process operations in batches
    const batches = this.createBatches(sortedOperations, this.SYNC_BATCH_SIZE);
    const failedOperations: OfflineOperation[] = [];

    for (const batch of batches) {
      console.log(`Processing batch of ${batch.length} operations...`);
      
      const batchResults = await Promise.allSettled(
        batch.map(operation => this.processOfflineOperation(operation))
      );

      // Handle batch results
      for (let i = 0; i < batch.length; i++) {
        const operation = batch[i];
        const result = batchResults[i];

        if (result.status === 'fulfilled') {
          // Remove successful operation from queue
          this.offlineQueue = this.offlineQueue.filter(op => op.id !== operation.id);
          console.log(`Successfully processed operation: ${operation.type} for task ${operation.taskId}`);
        } else {
          console.error('Failed to process offline operation:', operation, result.reason);
          
          // Increment retry count
          operation.retryCount++;
          
          if (operation.retryCount >= this.MAX_RETRY_COUNT) {
            console.error('Max retries reached for operation:', operation);
            // Log failed operation for manual review
            await this.logFailedOperation(operation, result.reason);
            this.offlineQueue = this.offlineQueue.filter(op => op.id !== operation.id);
          } else {
            // Exponential backoff for retries
            operation.timestamp = Date.now() + (Math.pow(2, operation.retryCount) * 1000);
            failedOperations.push(operation);
          }
        }
      }

      // Small delay between batches to avoid overwhelming the server
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Update queue with failed operations
    this.offlineQueue = failedOperations;
    this.saveOfflineQueue();

    console.log(`Offline queue processing complete. ${failedOperations.length} operations remain.`);
    
    // Schedule next processing if there are remaining operations
    if (failedOperations.length > 0) {
      setTimeout(() => this.processOfflineQueue(), 30000); // Retry in 30 seconds
    }
  }

  /**
   * Get current sync status for a task
   */
  getTaskSyncStatus(userId: string, taskId: string): 'synced' | 'pending' | 'conflict' | 'unknown' {
    const task = taskStorage.getTask(userId, taskId);
    if (!task || !task.timeTrackingSync) {
      return 'unknown';
    }
    return task.timeTrackingSync.syncStatus;
  }

  /**
   * Force sync all tasks with pending updates
   */
  async forceSyncAllPendingTasks(userId: string): Promise<void> {
    const allTasks = taskStorage.getAllTasks(userId);
    const pendingTasks = allTasks.filter(task => 
      task.timeTrackingSync?.syncStatus === 'pending' || 
      (task.timeTrackingSync?.pendingTimeUpdates || 0) > 0
    );

    console.log(`Force syncing ${pendingTasks.length} pending tasks...`);

    // Create backup before force sync
    await this.createBackup(userId);

    const syncResults = {
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const task of pendingTasks) {
      try {
        await this.syncTaskWithTimer(task.id, userId);
        syncResults.successful++;
      } catch (error) {
        console.error('Error force syncing task:', task.id, error);
        syncResults.failed++;
        syncResults.errors.push(`Task ${task.title}: ${error.message}`);
      }
    }

    console.log(`Force sync completed: ${syncResults.successful} successful, ${syncResults.failed} failed`);
    
    if (syncResults.errors.length > 0) {
      console.warn('Force sync errors:', syncResults.errors);
    }
  }

  /**
   * Validate data integrity between localStorage and Supabase
   */
  async validateDataIntegrity(userId: string): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      fixableIssues: []
    };

    try {
      console.log('Starting data integrity validation...');

      // Get all local tasks and remote data
      const localTasks = taskStorage.getAllTasks(userId);
      const remoteTodos = await getTodos(userId);
      const studySessions = await getStudySessions(userId);

      // Validate task consistency
      await this.validateTaskConsistency(localTasks, remoteTodos, result);

      // Validate session integrity
      await this.validateSessionIntegrity(localTasks, studySessions, result);

      // Validate time tracking accuracy
      await this.validateTimeTrackingAccuracy(localTasks, studySessions, result);

      // Check for orphaned data
      await this.checkForOrphanedData(localTasks, remoteTodos, studySessions, result);

      result.isValid = result.errors.length === 0;
      console.log(`Data integrity validation completed. Valid: ${result.isValid}`);

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation failed: ${error.message}`);
      console.error('Data integrity validation error:', error);
    }

    return result;
  }

  /**
   * Create backup of task time tracking data
   */
  async createBackup(userId: string): Promise<void> {
    try {
      console.log('Creating backup for user:', userId);

      const localTasks = taskStorage.getAllTasks(userId);
      const studySessions = await getStudySessions(userId);

      const backupData: BackupData = {
        version: '1.0',
        timestamp: Date.now(),
        userId,
        tasks: localTasks,
        studySessions,
        metadata: {
          totalTasks: localTasks.length,
          totalSessions: studySessions.length,
          lastSyncTimestamp: Date.now(),
          deviceInfo: navigator.userAgent
        }
      };

      // Store backup in localStorage with rotation
      const backupKey = `${this.BACKUP_KEY}_${userId}`;
      const existingBackups = this.getExistingBackups(userId);
      
      // Keep only recent backups (within retention period)
      const cutoffTime = Date.now() - (this.BACKUP_RETENTION_DAYS * 24 * 60 * 60 * 1000);
      const validBackups = existingBackups.filter(backup => backup.timestamp > cutoffTime);
      
      // Add new backup
      validBackups.push(backupData);
      
      // Keep only the most recent 10 backups
      const recentBackups = validBackups
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 10);

      localStorage.setItem(backupKey, JSON.stringify(recentBackups));
      console.log(`Backup created successfully. Total backups: ${recentBackups.length}`);

    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(userId: string, backupTimestamp?: number): Promise<void> {
    try {
      console.log('Restoring from backup for user:', userId);

      const backups = this.getExistingBackups(userId);
      if (backups.length === 0) {
        throw new Error('No backups found');
      }

      // Use specific backup or most recent
      const backup = backupTimestamp 
        ? backups.find(b => b.timestamp === backupTimestamp)
        : backups.sort((a, b) => b.timestamp - a.timestamp)[0];

      if (!backup) {
        throw new Error('Backup not found');
      }

      console.log(`Restoring backup from ${new Date(backup.timestamp).toISOString()}`);

      // Restore tasks
      for (const task of backup.tasks) {
        taskStorage.saveTask(userId, task);
      }

      // Note: Study sessions are in Supabase and don't need local restoration
      // They would need to be restored through Supabase admin functions if needed

      console.log(`Restored ${backup.tasks.length} tasks from backup`);

    } catch (error) {
      console.error('Error restoring from backup:', error);
      throw error;
    }
  }

  /**
   * Get queue status for monitoring
   */
  getQueueStatus(): {
    queueLength: number;
    oldestOperation: number | null;
    operationsByType: Record<string, number>;
    operationsByPriority: Record<string, number>;
  } {
    const operationsByType: Record<string, number> = {};
    const operationsByPriority: Record<string, number> = {};
    let oldestTimestamp: number | null = null;

    for (const operation of this.offlineQueue) {
      operationsByType[operation.type] = (operationsByType[operation.type] || 0) + 1;
      operationsByPriority[operation.priority] = (operationsByPriority[operation.priority] || 0) + 1;
      
      if (!oldestTimestamp || operation.timestamp < oldestTimestamp) {
        oldestTimestamp = operation.timestamp;
      }
    }

    return {
      queueLength: this.offlineQueue.length,
      oldestOperation: oldestTimestamp,
      operationsByType,
      operationsByPriority
    };
  }

  // Private helper methods

  /**
   * Sync local task with remote task in Supabase
   */
  private async syncWithRemoteTask(
    localTask: EnhancedTodoItem, 
    remoteTask: any, 
    sessionTotalMinutes: number, 
    userId: string
  ): Promise<void> {
    const updates = {
      title: localTask.title,
      description: localTask.description,
      priority: localTask.priority,
      actual_time_spent: Math.max(sessionTotalMinutes, localTask.actualTimeSpent || 0),
      time_estimate: localTask.timeEstimate,
      completion_percentage: localTask.completionPercentage,
      updated_at: Date.now()
    };

    await updateTodo(remoteTask.id, updates);
  }

  /**
   * Create new remote task in Supabase
   */
  private async createRemoteTask(
    localTask: EnhancedTodoItem, 
    sessionTotalMinutes: number, 
    userId: string
  ): Promise<void> {
    const remoteTaskData = {
      id: localTask.id,
      title: localTask.title,
      description: localTask.description || '',
      priority: localTask.priority,
      created_by: userId,
      created_at: localTask.createdAt,
      updated_at: Date.now(),
      actual_time_spent: Math.max(sessionTotalMinutes, localTask.actualTimeSpent || 0),
      time_estimate: localTask.timeEstimate,
      completion_percentage: localTask.completionPercentage,
      column_id: localTask.columnId,
      subject_id: localTask.subjectId,
      due_date: localTask.dueDate,
      tags: localTask.tags,
      chapter_tags: localTask.chapterTags,
      difficulty_level: localTask.difficultyLevel,
      notes: localTask.notes
    };

    await createTodo(remoteTaskData);
  }

  /**
   * Find duplicate sessions based on start time and duration
   */
  private findDuplicateSessions(sessions: any[]): any[] {
    const duplicates: any[] = [];
    const seen = new Map<string, any>();

    for (const session of sessions) {
      const key = `${session.start_time}_${session.duration}_${session.task_name}`;
      if (seen.has(key)) {
        duplicates.push(session);
      } else {
        seen.set(key, session);
      }
    }

    return duplicates;
  }

  /**
   * Resolve time mismatch conflicts using session data as source of truth
   */
  private async resolveTimeMismatchConflict(userId: string, conflict: SyncConflict): Promise<void> {
    const { taskId, remoteData } = conflict;
    const sessionTime = remoteData.sessionTime || remoteData.time;

    // Use session time as the authoritative source
    const task = taskStorage.getTask(userId, taskId);
    if (task) {
      const updatedTask = {
        ...task,
        actualTimeSpent: sessionTime,
        timeTrackingSync: {
          lastSyncTimestamp: Date.now(),
          syncStatus: 'synced' as const,
          pendingTimeUpdates: 0
        }
      };
      taskStorage.saveTask(userId, updatedTask);

      // Update remote task if it exists
      try {
        const remoteTodos = await getTodos(userId);
        const remoteTask = remoteTodos.find(todo => todo.id === taskId);
        if (remoteTask) {
          await updateTodo(taskId, { actual_time_spent: sessionTime });
        }
      } catch (error) {
        console.warn('Could not update remote task time:', error);
      }
    }
  }

  /**
   * Resolve session duplicate conflicts by deduplicating sessions
   */
  private async resolveSessionDuplicateConflict(userId: string, conflict: SyncConflict): Promise<void> {
    const { taskId, remoteData } = conflict;
    const duplicates = remoteData.duplicates || [];

    // Remove duplicate session references from task
    const task = taskStorage.getTask(userId, taskId);
    if (task && task.studySessionIds) {
      const uniqueSessionIds = [...new Set(task.studySessionIds)];
      
      // Remove known duplicates
      const cleanedSessionIds = uniqueSessionIds.filter(id => 
        !duplicates.some((dup: any) => dup.id === id)
      );

      const updatedTask = {
        ...task,
        studySessionIds: cleanedSessionIds
      };
      taskStorage.saveTask(userId, updatedTask);
    }
  }

  /**
   * Resolve task version conflicts using last-write-wins with user notification
   */
  private async resolveTaskVersionConflict(userId: string, conflict: SyncConflict): Promise<void> {
    const { taskId, localData, remoteData } = conflict;

    // Use the most recently updated version
    const useRemote = remoteData.updatedAt > localData.updatedAt;
    
    if (useRemote) {
      // Fetch and apply remote version
      try {
        const remoteTodos = await getTodos(userId);
        const remoteTask = remoteTodos.find(todo => todo.id === taskId);
        if (remoteTask) {
          // Convert remote task to local format and save
          const localTask = this.convertRemoteTaskToLocal(remoteTask, userId);
          taskStorage.saveTask(userId, localTask);
        }
      } catch (error) {
        console.error('Error applying remote task version:', error);
      }
    } else {
      // Keep local version and update remote
      const localTask = taskStorage.getTask(userId, taskId);
      if (localTask) {
        try {
          const remoteTaskData = this.convertLocalTaskToRemote(localTask);
          await updateTodo(taskId, remoteTaskData);
        } catch (error) {
          console.error('Error updating remote task version:', error);
        }
      }
    }

    // Log the conflict resolution for user awareness
    console.warn(`Task version conflict resolved for "${localData.title}". Used ${useRemote ? 'remote' : 'local'} version.`);
  }

  /**
   * Convert remote task to local format
   */
  private convertRemoteTaskToLocal(remoteTask: any, userId: string): EnhancedTodoItem {
    return {
      id: remoteTask.id,
      title: remoteTask.title,
      description: remoteTask.description || '',
      priority: remoteTask.priority || 'medium',
      createdAt: remoteTask.created_at,
      updatedAt: remoteTask.updated_at,
      createdBy: userId,
      columnId: remoteTask.column_id || 'column-1',
      tags: remoteTask.tags || [],
      chapterTags: remoteTask.chapter_tags || [],
      difficultyLevel: remoteTask.difficulty_level || 'medium',
      completionPercentage: remoteTask.completion_percentage || 0,
      viewCount: remoteTask.view_count || 0,
      dueDate: remoteTask.due_date,
      subjectId: remoteTask.subject_id,
      examId: remoteTask.exam_id,
      timeEstimate: remoteTask.time_estimate,
      actualTimeSpent: remoteTask.actual_time_spent || 0,
      notes: remoteTask.notes,
      assignedTo: remoteTask.assigned_to,
      assignedToName: remoteTask.assigned_to_name,
      assignedToPhotoURL: remoteTask.assigned_to_photo_url,
      parentId: remoteTask.parent_id,
      lastViewed: remoteTask.last_viewed
    };
  }

  /**
   * Convert local task to remote format
   */
  private convertLocalTaskToRemote(localTask: EnhancedTodoItem): any {
    return {
      title: localTask.title,
      description: localTask.description,
      priority: localTask.priority,
      updated_at: Date.now(),
      actual_time_spent: localTask.actualTimeSpent,
      time_estimate: localTask.timeEstimate,
      completion_percentage: localTask.completionPercentage,
      column_id: localTask.columnId,
      subject_id: localTask.subjectId,
      due_date: localTask.dueDate,
      tags: localTask.tags,
      chapter_tags: localTask.chapterTags,
      difficulty_level: localTask.difficultyLevel,
      notes: localTask.notes,
      assigned_to: localTask.assignedTo,
      assigned_to_name: localTask.assignedToName,
      assigned_to_photo_url: localTask.assignedToPhotoURL,
      parent_id: localTask.parentId,
      last_viewed: localTask.lastViewed,
      view_count: localTask.viewCount
    };
  }

  /**
   * Create batches from operations array
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Enhanced offline operation processing
   */
  private async processOfflineOperation(operation: OfflineOperation): Promise<void> {
    console.log(`Processing offline operation: ${operation.type} for task ${operation.taskId}`);

    switch (operation.type) {
      case 'time_update':
        await this.processTimeUpdateOperation(operation);
        break;

      case 'session_save':
        await this.processSessionSaveOperation(operation);
        break;

      case 'task_update':
        await this.processTaskUpdateOperation(operation);
        break;

      case 'task_create':
        await this.processTaskCreateOperation(operation);
        break;

      case 'task_delete':
        await this.processTaskDeleteOperation(operation);
        break;

      case 'task_sync':
        await this.processTaskSyncOperation(operation);
        break;

      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  /**
   * Process time update operation
   */
  private async processTimeUpdateOperation(operation: OfflineOperation): Promise<void> {
    const { taskId, data, userId } = operation;
    const task = taskStorage.getTask(userId, taskId);
    
    if (task) {
      taskStorage.updateTaskTimeSpent(userId, taskId, data.additionalTime);
      
      // Also update remote task if it exists
      try {
        const remoteTodos = await getTodos(userId);
        const remoteTask = remoteTodos.find(todo => todo.id === taskId);
        if (remoteTask) {
          const newTimeSpent = (remoteTask.actual_time_spent || 0) + data.additionalTime;
          await updateTodo(taskId, { actual_time_spent: newTimeSpent });
        }
      } catch (error) {
        console.warn('Could not update remote task time:', error);
      }
    }
  }

  /**
   * Process session save operation
   */
  private async processSessionSaveOperation(operation: OfflineOperation): Promise<void> {
    const sessionData: EnhancedStudySessionData = operation.data;
    await saveStudySession(sessionData);
    
    // Update associated task's session IDs
    if (sessionData.task_name) {
      const task = taskStorage.getAllTasks(operation.userId)
        .find(t => t.title === sessionData.task_name);
      
      if (task) {
        const savedSession = await saveStudySession(sessionData);
        taskStorage.addStudySessionToTask(operation.userId, task.id, savedSession.id);
      }
    }
  }

  /**
   * Process task update operation
   */
  private async processTaskUpdateOperation(operation: OfflineOperation): Promise<void> {
    const { taskId, data, userId } = operation;
    
    // Update local task
    taskStorage.saveTask(userId, data);
    
    // Update remote task if it exists
    try {
      const remoteTodos = await getTodos(userId);
      const remoteTask = remoteTodos.find(todo => todo.id === taskId);
      if (remoteTask) {
        const remoteData = this.convertLocalTaskToRemote(data);
        await updateTodo(taskId, remoteData);
      }
    } catch (error) {
      console.warn('Could not update remote task:', error);
    }
  }

  /**
   * Process task create operation
   */
  private async processTaskCreateOperation(operation: OfflineOperation): Promise<void> {
    const { data, userId } = operation;
    
    // Task should already be in localStorage, now create in Supabase
    try {
      const remoteData = this.convertLocalTaskToRemote(data);
      await createTodo(remoteData);
    } catch (error) {
      console.error('Could not create remote task:', error);
      throw error;
    }
  }

  /**
   * Process task delete operation
   */
  private async processTaskDeleteOperation(operation: OfflineOperation): Promise<void> {
    const { taskId, userId } = operation;
    
    // Delete from Supabase (should already be deleted from localStorage)
    try {
      await deleteTodo(taskId);
    } catch (error) {
      console.warn('Could not delete remote task:', error);
      // Don't throw error as local deletion was successful
    }
  }

  /**
   * Process task sync operation
   */
  private async processTaskSyncOperation(operation: OfflineOperation): Promise<void> {
    const { taskId, data } = operation;
    await this.syncTaskWithTimer(taskId, data.userId);
  }

  /**
   * Check if error is network-related
   */
  private isNetworkError(error: any): boolean {
    return error.message?.includes('network') || 
           error.message?.includes('fetch') ||
           error.code === 'NETWORK_ERROR' ||
           !navigator.onLine;
  }

  /**
   * Update sync state for a task
   */
  private updateSyncState(userId: string, taskId: string, status: SyncStatus): void {
    try {
      const syncStateKey = `${this.SYNC_STATE_KEY}_${userId}`;
      const syncState = JSON.parse(localStorage.getItem(syncStateKey) || '{}');
      
      syncState[taskId] = {
        status,
        timestamp: Date.now()
      };

      localStorage.setItem(syncStateKey, JSON.stringify(syncState));
    } catch (error) {
      console.error('Error updating sync state:', error);
    }
  }

  /**
   * Get existing backups for a user
   */
  private getExistingBackups(userId: string): BackupData[] {
    try {
      const backupKey = `${this.BACKUP_KEY}_${userId}`;
      const backupData = localStorage.getItem(backupKey);
      return backupData ? JSON.parse(backupData) : [];
    } catch (error) {
      console.error('Error getting existing backups:', error);
      return [];
    }
  }

  private loadOfflineQueue(): void {
    try {
      const queueData = localStorage.getItem(this.OFFLINE_QUEUE_KEY);
      if (queueData) {
        this.offlineQueue = JSON.parse(queueData);
      }
    } catch (error) {
      console.error('Error loading offline queue:', error);
      this.offlineQueue = [];
    }
  }

  private saveOfflineQueue(): void {
    try {
      localStorage.setItem(this.OFFLINE_QUEUE_KEY, JSON.stringify(this.offlineQueue));
    } catch (error) {
      console.error('Error saving offline queue:', error);
    }
  }

  /**
   * Setup network listeners for online/offline handling
   */
  private setupNetworkListeners(): void {
    // Process queue when coming back online
    window.addEventListener('online', () => {
      console.log('Network connection restored, processing offline queue...');
      setTimeout(() => this.processOfflineQueue(), 1000);
    });

    // Log when going offline
    window.addEventListener('offline', () => {
      console.log('Network connection lost, operations will be queued...');
    });
  }

  /**
   * Setup periodic sync for background synchronization
   */
  private setupPeriodicSync(): void {
    // Sync every 5 minutes when online
    setInterval(() => {
      if (navigator.onLine && !this.syncInProgress) {
        this.processOfflineQueue();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Setup periodic data integrity checks
   */
  private setupDataIntegrityChecks(): void {
    // Run integrity check every hour
    setInterval(() => {
      if (navigator.onLine) {
        // Get current user ID (this would need to be passed from the app context)
        const userId = this.getCurrentUserId();
        if (userId) {
          this.validateDataIntegrity(userId).then(result => {
            if (!result.isValid) {
              console.warn('Data integrity issues detected:', result.errors);
            }
          }).catch(error => {
            console.error('Data integrity check failed:', error);
          });
        }
      }
    }, 60 * 60 * 1000);
  }

  /**
   * Get current user ID (placeholder - should be implemented based on app's auth system)
   */
  private getCurrentUserId(): string | null {
    // This should be implemented to get the current user ID from your auth system
    // For now, return null to avoid errors
    return null;
  }

  /**
   * Validate task consistency between local and remote
   */
  private async validateTaskConsistency(
    localTasks: EnhancedTodoItem[], 
    remoteTodos: any[], 
    result: ValidationResult
  ): Promise<void> {
    const localTaskIds = new Set(localTasks.map(t => t.id));
    const remoteTaskIds = new Set(remoteTodos.map(t => t.id));

    // Check for tasks that exist locally but not remotely
    for (const localTask of localTasks) {
      if (!remoteTaskIds.has(localTask.id)) {
        result.warnings.push(`Task "${localTask.title}" exists locally but not in Supabase`);
        result.fixableIssues.push(`Create remote task for "${localTask.title}"`);
      }
    }

    // Check for tasks that exist remotely but not locally
    for (const remoteTask of remoteTodos) {
      if (!localTaskIds.has(remoteTask.id)) {
        result.warnings.push(`Task "${remoteTask.title}" exists in Supabase but not locally`);
        result.fixableIssues.push(`Sync remote task "${remoteTask.title}" to local storage`);
      }
    }

    // Check for data inconsistencies in matching tasks
    for (const localTask of localTasks) {
      const remoteTask = remoteTodos.find(t => t.id === localTask.id);
      if (remoteTask) {
        // Check time tracking consistency
        const localTime = localTask.actualTimeSpent || 0;
        const remoteTime = remoteTask.actual_time_spent || 0;
        
        if (Math.abs(localTime - remoteTime) > 5) { // 5 minute tolerance
          result.warnings.push(
            `Time mismatch for "${localTask.title}": local=${localTime}min, remote=${remoteTime}min`
          );
          result.fixableIssues.push(`Sync time data for "${localTask.title}"`);
        }

        // Check title consistency
        if (localTask.title !== remoteTask.title) {
          result.errors.push(
            `Title mismatch for task ${localTask.id}: local="${localTask.title}", remote="${remoteTask.title}"`
          );
        }
      }
    }
  }

  /**
   * Validate session integrity
   */
  private async validateSessionIntegrity(
    localTasks: EnhancedTodoItem[], 
    studySessions: any[], 
    result: ValidationResult
  ): Promise<void> {
    // Check for orphaned sessions
    const taskTitles = new Set(localTasks.map(t => t.title));
    
    for (const session of studySessions) {
      if (session.task_name && !taskTitles.has(session.task_name)) {
        result.warnings.push(`Orphaned session found for non-existent task: "${session.task_name}"`);
        result.fixableIssues.push(`Clean up orphaned session for "${session.task_name}"`);
      }
    }

    // Check for duplicate sessions
    const sessionMap = new Map<string, any[]>();
    for (const session of studySessions) {
      const key = `${session.task_name}_${session.start_time}_${session.duration}`;
      if (!sessionMap.has(key)) {
        sessionMap.set(key, []);
      }
      sessionMap.get(key)!.push(session);
    }

    for (const [key, sessions] of sessionMap) {
      if (sessions.length > 1) {
        result.warnings.push(`Duplicate sessions detected: ${sessions.length} sessions with key ${key}`);
        result.fixableIssues.push(`Remove duplicate sessions for ${key}`);
      }
    }
  }

  /**
   * Validate time tracking accuracy
   */
  private async validateTimeTrackingAccuracy(
    localTasks: EnhancedTodoItem[], 
    studySessions: any[], 
    result: ValidationResult
  ): Promise<void> {
    for (const task of localTasks) {
      const taskSessions = studySessions.filter(s => s.task_name === task.title);
      const sessionTotalMinutes = Math.round(
        taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60
      );
      const taskTimeMinutes = task.actualTimeSpent || 0;

      if (Math.abs(sessionTotalMinutes - taskTimeMinutes) > 2) { // 2 minute tolerance
        result.warnings.push(
          `Time tracking mismatch for "${task.title}": task=${taskTimeMinutes}min, sessions=${sessionTotalMinutes}min`
        );
        result.fixableIssues.push(`Recalculate time for "${task.title}"`);
      }
    }
  }

  /**
   * Check for orphaned data
   */
  private async checkForOrphanedData(
    localTasks: EnhancedTodoItem[], 
    remoteTodos: any[], 
    studySessions: any[], 
    result: ValidationResult
  ): Promise<void> {
    // Check for sessions without corresponding tasks
    const allTaskTitles = new Set([
      ...localTasks.map(t => t.title),
      ...remoteTodos.map(t => t.title)
    ]);

    const orphanedSessions = studySessions.filter(session => 
      session.task_name && !allTaskTitles.has(session.task_name)
    );

    if (orphanedSessions.length > 0) {
      result.warnings.push(`Found ${orphanedSessions.length} orphaned study sessions`);
      result.fixableIssues.push(`Clean up ${orphanedSessions.length} orphaned sessions`);
    }
  }

  /**
   * Handle unknown conflict types
   */
  private async handleUnknownConflict(userId: string, conflict: SyncConflict): Promise<void> {
    console.warn('Handling unknown conflict type:', conflict);
    // Log the conflict for manual review
    await this.logConflictResolutionFailure(userId, conflict, new Error('Unknown conflict type'));
  }

  /**
   * Resolve task not found conflicts
   */
  private async resolveTaskNotFoundConflict(userId: string, conflict: SyncConflict): Promise<void> {
    console.warn(`Task ${conflict.taskId} not found in localStorage but has sessions in Supabase`);
    // Could implement task recreation from session data if needed
  }

  /**
   * Resolve session integrity conflicts
   */
  private async resolveSessionIntegrityConflict(userId: string, conflict: SyncConflict): Promise<void> {
    // Handle session integrity issues like missing or corrupted session data
    console.warn('Resolving session integrity conflict:', conflict);
  }

  /**
   * Resolve cross-device conflicts
   */
  private async resolveCrossDeviceConflict(userId: string, conflict: SyncConflict): Promise<void> {
    // Handle conflicts that arise from simultaneous edits on different devices
    console.warn('Resolving cross-device conflict:', conflict);
  }

  /**
   * Log conflict resolution failure
   */
  private async logConflictResolutionFailure(
    userId: string, 
    conflict: SyncConflict, 
    error: any
  ): Promise<void> {
    const logEntry = {
      timestamp: Date.now(),
      userId,
      conflict,
      error: error.message,
      stack: error.stack
    };

    try {
      // Store in localStorage for now (could be sent to logging service)
      const logKey = `sync_conflict_failures_${userId}`;
      const existingLogs = JSON.parse(localStorage.getItem(logKey) || '[]');
      existingLogs.push(logEntry);
      
      // Keep only the most recent 50 log entries
      const recentLogs = existingLogs.slice(-50);
      localStorage.setItem(logKey, JSON.stringify(recentLogs));
    } catch (logError) {
      console.error('Failed to log conflict resolution failure:', logError);
    }
  }

  /**
   * Log failed operation for manual review
   */
  private async logFailedOperation(operation: OfflineOperation, error: any): Promise<void> {
    const logEntry = {
      timestamp: Date.now(),
      operation,
      error: error.message,
      stack: error.stack
    };

    try {
      const logKey = `failed_operations_${operation.userId}`;
      const existingLogs = JSON.parse(localStorage.getItem(logKey) || '[]');
      existingLogs.push(logEntry);
      
      // Keep only the most recent 100 log entries
      const recentLogs = existingLogs.slice(-100);
      localStorage.setItem(logKey, JSON.stringify(recentLogs));
    } catch (logError) {
      console.error('Failed to log failed operation:', logError);
    }
  }
}

// Export singleton instance
export const dataSyncUtilities = DataSyncUtilities.getInstance();