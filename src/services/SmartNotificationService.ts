import { EnhancedTodoItem } from '@/types/todo';
import { taskStorage } from '@/utils/taskLocalStorage';
import { useToast } from '@/hooks/use-toast';
import { useEnhancedTimerStore } from '@/stores/enhancedTimerStore';

// Notification types
export type NotificationType = 
  | 'deadline_reminder'
  | 'break_suggestion'
  | 'procrastination_nudge'
  | 'productivity_streak'
  | 'context_reminder'
  | 'time_estimate_exceeded'
  | 'task_completion_prompt';

// Notification priority levels
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

// Notification interface
export interface SmartNotification {
  id: string;
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  description: string;
  taskId?: string;
  scheduledFor: number; // timestamp
  createdAt: number;
  isRead: boolean;
  isDismissed: boolean;
  actionable: boolean;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
}

// Notification actions
export interface NotificationAction {
  id: string;
  label: string;
  type: 'primary' | 'secondary' | 'destructive';
  action: () => void;
}

// User productivity patterns
export interface ProductivityPattern {
  userId: string;
  peakHours: number[]; // Hours of day (0-23) when most productive
  averageSessionLength: number; // in minutes
  preferredBreakLength: number; // in minutes
  procrastinationTriggers: string[]; // Task types or subjects that are often delayed
  completionStreak: number;
  lastActiveDate: number;
  taskTypePreferences: Record<string, number>; // Task type -> productivity score
  subjectProductivity: Record<string, number>; // Subject -> productivity score
}

// Notification settings
export interface NotificationSettings {
  enabled: boolean;
  deadlineReminders: boolean;
  breakSuggestions: boolean;
  procrastinationNudges: boolean;
  productivityStreaks: boolean;
  contextReminders: boolean;
  quietHours: { start: number; end: number }; // Hours (0-23)
  maxNotificationsPerHour: number;
  reminderAdvanceTime: number; // Hours before deadline to remind
}

// Default notification settings
const DEFAULT_NOTIFICATION_SETTINGS: NotificationSettings = {
  enabled: true,
  deadlineReminders: true,
  breakSuggestions: true,
  procrastinationNudges: true,
  productivityStreaks: true,
  contextReminders: true,
  quietHours: { start: 22, end: 7 }, // 10 PM to 7 AM
  maxNotificationsPerHour: 3,
  reminderAdvanceTime: 24, // 24 hours before deadline
};

class SmartNotificationService {
  private notifications: SmartNotification[] = [];
  private patterns: Map<string, ProductivityPattern> = new Map();
  private settings: Map<string, NotificationSettings> = new Map();
  private notificationQueue: SmartNotification[] = [];
  private isProcessing = false;
  private intervalId: NodeJS.Timeout | null = null;

  constructor() {
    this.loadFromStorage();
    this.startNotificationProcessor();
  }

  // Initialize the service for a user
  public initialize(userId: string): void {
    if (!this.patterns.has(userId)) {
      this.patterns.set(userId, this.createDefaultPattern(userId));
    }
    if (!this.settings.has(userId)) {
      this.settings.set(userId, { ...DEFAULT_NOTIFICATION_SETTINGS });
    }
    this.analyzeProductivityPatterns(userId);
  }

  // Main notification scheduling methods

  /**
   * Requirement 13.1: Intelligent deadline reminders based on task complexity and available time
   */
  public scheduleDeadlineReminders(userId: string): void {
    const tasks = taskStorage.getAllTasks(userId);
    const settings = this.getSettings(userId);
    const pattern = this.getPattern(userId);

    if (!settings.deadlineReminders) return;

    const now = Date.now();
    const upcomingTasks = tasks.filter(task => 
      task.dueDate && 
      task.dueDate > now && 
      task.completionPercentage < 100
    );

    upcomingTasks.forEach(task => {
      const timeUntilDeadline = task.dueDate! - now;
      const hoursUntilDeadline = timeUntilDeadline / (1000 * 60 * 60);
      
      // Calculate task complexity score (1-10)
      const complexityScore = this.calculateTaskComplexity(task);
      
      // Calculate estimated time needed based on complexity and historical data
      const estimatedTimeNeeded = this.estimateTimeNeeded(task, pattern);
      
      // Determine when to send reminder based on complexity and available time
      const reminderTimes = this.calculateReminderTimes(
        hoursUntilDeadline,
        estimatedTimeNeeded,
        complexityScore
      );

      reminderTimes.forEach((reminderTime, index) => {
        const priority = this.determineReminderPriority(hoursUntilDeadline, complexityScore);
        
        this.scheduleNotification({
          type: 'deadline_reminder',
          priority,
          title: this.generateDeadlineReminderTitle(task, hoursUntilDeadline),
          description: this.generateDeadlineReminderDescription(task, estimatedTimeNeeded, complexityScore),
          taskId: task.id,
          scheduledFor: now + reminderTime,
          actionable: true,
          actions: [
            {
              id: 'start_task',
              label: 'Start Task',
              type: 'primary',
              action: () => this.startTaskTimer(userId, task.id)
            },
            {
              id: 'reschedule',
              label: 'Reschedule',
              type: 'secondary',
              action: () => this.openTaskReschedule(task.id)
            }
          ],
          metadata: {
            complexityScore,
            estimatedTimeNeeded,
            reminderIndex: index
          }
        });
      });
    });
  }

  /**
   * Requirement 13.2: Optimal break timing notifications during study sessions
   */
  public scheduleBreakSuggestions(userId: string): void {
    const settings = this.getSettings(userId);
    const pattern = this.getPattern(userId);
    const timerStore = useEnhancedTimerStore.getState();

    if (!settings.breakSuggestions || timerStore.status !== 'running') return;

    const sessionStartTime = timerStore.sessionStartTime.getTime();
    const now = Date.now();
    const sessionDuration = (now - sessionStartTime) / (1000 * 60); // minutes

    // Calculate optimal break time based on user patterns and current task
    const optimalBreakTime = this.calculateOptimalBreakTime(pattern, timerStore.linkedTask);
    
    if (sessionDuration >= optimalBreakTime) {
      this.scheduleNotification({
        type: 'break_suggestion',
        priority: 'medium',
        title: 'Time for a Break! 🧠',
        description: `You've been focused for ${Math.round(sessionDuration)} minutes. Taking a break can help maintain your productivity.`,
        scheduledFor: now,
        actionable: true,
        actions: [
          {
            id: 'take_break',
            label: 'Take Break',
            type: 'primary',
            action: () => this.initiateBreak(userId)
          },
          {
            id: 'continue_5min',
            label: 'Continue 5 min',
            type: 'secondary',
            action: () => this.snoozeBreakSuggestion(5)
          },
          {
            id: 'dismiss',
            label: 'Keep Going',
            type: 'secondary',
            action: () => this.dismissBreakSuggestion()
          }
        ],
        metadata: {
          sessionDuration,
          optimalBreakTime,
          taskType: timerStore.linkedTask?.taskTitle
        }
      });
    }
  }

  /**
   * Requirement 13.3: Procrastination pattern detection and motivational nudges
   */
  public detectProcrastinationAndNudge(userId: string): void {
    const settings = this.getSettings(userId);
    const pattern = this.getPattern(userId);
    const tasks = taskStorage.getAllTasks(userId);

    if (!settings.procrastinationNudges) return;

    const now = Date.now();
    const highPriorityTasks = tasks.filter(task => 
      task.priority === 'high' && 
      task.completionPercentage < 100
    );

    highPriorityTasks.forEach(task => {
      const daysSinceCreated = (now - task.createdAt) / (1000 * 60 * 60 * 24);
      const lastWorkedOn = this.getLastWorkedOnTime(userId, task.id);
      const daysSinceLastWorked = lastWorkedOn ? (now - lastWorkedOn) / (1000 * 60 * 60 * 24) : daysSinceCreated;

      // Detect procrastination patterns
      const isProcrastinating = this.detectProcrastination(task, daysSinceLastWorked, pattern);

      if (isProcrastinating) {
        const motivationalMessage = this.generateMotivationalMessage(task, pattern);
        const strategy = this.suggestProcrastinationStrategy(task, pattern);

        this.scheduleNotification({
          type: 'procrastination_nudge',
          priority: 'high',
          title: '💪 Let\'s tackle this together!',
          description: motivationalMessage,
          taskId: task.id,
          scheduledFor: now,
          actionable: true,
          actions: [
            {
              id: 'start_small',
              label: 'Start Small (5 min)',
              type: 'primary',
              action: () => this.startSmallSession(userId, task.id, 5)
            },
            {
              id: 'break_down',
              label: 'Break It Down',
              type: 'secondary',
              action: () => this.openTaskBreakdown(task.id)
            }
          ],
          metadata: {
            daysSinceLastWorked,
            procrastinationReason: this.identifyProcrastinationReason(task, pattern),
            suggestedStrategy: strategy
          }
        });
      }
    });
  }

  /**
   * Requirement 13.4: Productivity streak tracking and positive reinforcement
   */
  public trackProductivityStreak(userId: string): void {
    const settings = this.getSettings(userId);
    const pattern = this.getPattern(userId);

    if (!settings.productivityStreaks) return;

    const streakData = this.calculateCurrentStreak(userId);
    const now = Date.now();

    // Celebrate streak milestones
    if (this.isStreakMilestone(streakData.current)) {
      this.scheduleNotification({
        type: 'productivity_streak',
        priority: 'medium',
        title: `🔥 ${streakData.current} Day Streak!`,
        description: `Amazing! You've been consistently productive for ${streakData.current} days. Keep up the great work!`,
        scheduledFor: now,
        actionable: false,
        metadata: {
          streakLength: streakData.current,
          milestone: true,
          previousBest: streakData.longest
        }
      });
    }

    // Encourage streak continuation
    if (streakData.current > 0 && this.shouldEncourageStreak(pattern)) {
      this.scheduleNotification({
        type: 'productivity_streak',
        priority: 'low',
        title: '⚡ Keep the momentum going!',
        description: `You're on a ${streakData.current} day streak. Complete one more task today to keep it alive!`,
        scheduledFor: now,
        actionable: true,
        actions: [
          {
            id: 'view_tasks',
            label: 'View Tasks',
            type: 'primary',
            action: () => this.navigateToTasks()
          }
        ],
        metadata: {
          streakLength: streakData.current,
          encouragement: true
        }
      });
    }
  }

  /**
   * Requirement 13.5: Context-aware reminder system that respects user's current activity
   */
  public scheduleContextAwareReminders(userId: string): void {
    const settings = this.getSettings(userId);
    const pattern = this.getPattern(userId);
    const timerStore = useEnhancedTimerStore.getState();

    if (!settings.contextReminders) return;

    const now = Date.now();
    const currentHour = new Date().getHours();
    
    // Respect quiet hours
    if (this.isQuietHour(currentHour, settings)) return;

    // Don't interrupt active timer sessions
    if (timerStore.status === 'running') return;

    // Check if user is in a productive time window
    const isProductiveTime = pattern.peakHours.includes(currentHour);
    
    if (isProductiveTime) {
      const suggestedTasks = this.getSuggestedTasksForContext(userId, pattern);
      
      if (suggestedTasks.length > 0) {
        const topTask = suggestedTasks[0];
        
        this.scheduleNotification({
          type: 'context_reminder',
          priority: 'low',
          title: '🎯 Perfect time to be productive!',
          description: `Based on your patterns, now is a great time to work on "${topTask.title}".`,
          taskId: topTask.id,
          scheduledFor: now,
          actionable: true,
          actions: [
            {
              id: 'start_suggested',
              label: 'Start Task',
              type: 'primary',
              action: () => this.startTaskTimer(userId, topTask.id)
            },
            {
              id: 'view_all',
              label: 'View All Tasks',
              type: 'secondary',
              action: () => this.navigateToTasks()
            }
          ],
          metadata: {
            isProductiveTime: true,
            suggestedTasksCount: suggestedTasks.length,
            contextReason: 'peak_productivity_hour'
          }
        });
      }
    }
  }

  // Utility methods for calculations and analysis

  private calculateTaskComplexity(task: EnhancedTodoItem): number {
    let complexity = 5; // Base complexity

    // Adjust based on task properties
    if (task.priority === 'high') complexity += 2;
    if (task.priority === 'low') complexity -= 1;
    
    if (task.difficultyLevel === 'hard') complexity += 2;
    if (task.difficultyLevel === 'easy') complexity -= 2;

    // Consider description length as complexity indicator
    if (task.description.length > 200) complexity += 1;
    if (task.description.length > 500) complexity += 1;

    // Consider tags and subtasks
    if (task.tags.length > 3) complexity += 1;
    if (task.hasSubtasks) complexity += 1;

    return Math.max(1, Math.min(10, complexity));
  }

  private estimateTimeNeeded(task: EnhancedTodoItem, pattern: ProductivityPattern): number {
    // Start with user's estimate if available
    if (task.timeEstimate) {
      return task.timeEstimate;
    }

    // Use historical data for similar tasks
    const baseEstimate = pattern.averageSessionLength;
    const complexity = this.calculateTaskComplexity(task);
    
    // Adjust based on complexity
    return Math.round(baseEstimate * (complexity / 5));
  }

  private calculateReminderTimes(hoursUntilDeadline: number, estimatedTimeNeeded: number, complexityScore: number): number[] {
    const reminderTimes: number[] = [];
    const hoursNeeded = estimatedTimeNeeded / 60;

    // More complex tasks get earlier and more frequent reminders
    const reminderMultiplier = Math.max(1, complexityScore / 5);
    
    if (hoursUntilDeadline > 168) { // More than a week
      reminderTimes.push(168 * 60 * 60 * 1000 * reminderMultiplier); // 1 week before
      reminderTimes.push(72 * 60 * 60 * 1000); // 3 days before
    }
    
    if (hoursUntilDeadline > 48) { // More than 2 days
      reminderTimes.push(48 * 60 * 60 * 1000); // 2 days before
    }
    
    if (hoursUntilDeadline > 24) { // More than 1 day
      reminderTimes.push(24 * 60 * 60 * 1000); // 1 day before
    }
    
    if (hoursUntilDeadline > hoursNeeded * 2) { // Enough time for completion
      reminderTimes.push(hoursNeeded * 2 * 60 * 60 * 1000); // 2x estimated time before
    }

    return reminderTimes;
  }

  private determineReminderPriority(hoursUntilDeadline: number, complexityScore: number): NotificationPriority {
    if (hoursUntilDeadline < 2) return 'urgent';
    if (hoursUntilDeadline < 12 && complexityScore > 7) return 'high';
    if (hoursUntilDeadline < 24) return 'high';
    if (hoursUntilDeadline < 72) return 'medium';
    return 'low';
  }

  private calculateOptimalBreakTime(pattern: ProductivityPattern, currentTask?: any): number {
    let baseBreakTime = pattern.averageSessionLength;
    
    // Adjust based on task type if available
    if (currentTask?.subjectId && pattern.subjectProductivity[currentTask.subjectId]) {
      const productivity = pattern.subjectProductivity[currentTask.subjectId];
      // Higher productivity subjects can go longer before breaks
      baseBreakTime *= (productivity / 5);
    }

    return Math.max(15, Math.min(90, baseBreakTime)); // Between 15-90 minutes
  }

  private detectProcrastination(task: EnhancedTodoItem, daysSinceLastWorked: number, pattern: ProductivityPattern): boolean {
    // High priority tasks not worked on for 2+ days
    if (task.priority === 'high' && daysSinceLastWorked >= 2) return true;
    
    // Tasks with approaching deadlines
    if (task.dueDate) {
      const daysUntilDeadline = (task.dueDate - Date.now()) / (1000 * 60 * 60 * 24);
      if (daysUntilDeadline <= 3 && daysSinceLastWorked >= 1) return true;
    }

    // Tasks in subjects/types user typically procrastinates on
    if (task.subjectId && pattern.procrastinationTriggers.includes(task.subjectId)) {
      return daysSinceLastWorked >= 1;
    }

    return false;
  }

  private generateMotivationalMessage(task: EnhancedTodoItem, pattern: ProductivityPattern): string {
    const messages = [
      `"${task.title}" is waiting for your attention. You've got this! 💪`,
      `Every expert was once a beginner. Let's make progress on "${task.title}" together.`,
      `Small steps lead to big achievements. Ready to tackle "${task.title}"?`,
      `Your future self will thank you for starting "${task.title}" today.`,
      `Progress, not perfection. Let's work on "${task.title}" for just a few minutes.`
    ];

    return messages[Math.floor(Math.random() * messages.length)];
  }

  private suggestProcrastinationStrategy(task: EnhancedTodoItem, pattern: ProductivityPattern): string {
    const strategies = [
      'Break the task into smaller, manageable chunks',
      'Set a timer for just 5 minutes to get started',
      'Work on it during your most productive hours',
      'Remove distractions and create a focused environment',
      'Reward yourself after completing a portion'
    ];

    return strategies[Math.floor(Math.random() * strategies.length)];
  }

  private calculateCurrentStreak(userId: string): { current: number; longest: number } {
    const tasks = taskStorage.getAllTasks(userId);
    const completedTasks = tasks
      .filter(t => t.completionPercentage === 100)
      .sort((a, b) => a.updatedAt - b.updatedAt);

    if (completedTasks.length === 0) {
      return { current: 0, longest: 0 };
    }

    const dayInMs = 24 * 60 * 60 * 1000;
    const today = new Date().setHours(0, 0, 0, 0);

    // Group tasks by day
    const tasksByDay = completedTasks.reduce((acc, task) => {
      const day = new Date(task.updatedAt).setHours(0, 0, 0, 0);
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const days = Object.keys(tasksByDay).map(Number).sort((a, b) => b - a);

    // Calculate current streak
    let currentStreak = 0;
    let checkDay = today;

    while (days.includes(checkDay)) {
      currentStreak++;
      checkDay -= dayInMs;
    }

    // Calculate longest streak
    let longestStreak = 0;
    let tempStreak = 0;

    for (let i = days.length - 1; i >= 0; i--) {
      if (i === days.length - 1 || days[i] === days[i + 1] + dayInMs) {
        tempStreak++;
        longestStreak = Math.max(longestStreak, tempStreak);
      } else {
        tempStreak = 1;
      }
    }

    return { current: currentStreak, longest: longestStreak };
  }

  private isStreakMilestone(streak: number): boolean {
    const milestones = [3, 7, 14, 30, 60, 100];
    return milestones.includes(streak);
  }

  private shouldEncourageStreak(pattern: ProductivityPattern): boolean {
    const now = Date.now();
    const today = new Date().setHours(0, 0, 0, 0);
    const daysSinceLastActive = (now - pattern.lastActiveDate) / (1000 * 60 * 60 * 24);
    
    // Encourage if user hasn't been active today but has an active streak
    return daysSinceLastActive < 1 && pattern.completionStreak > 0;
  }

  private getSuggestedTasksForContext(userId: string, pattern: ProductivityPattern): EnhancedTodoItem[] {
    const tasks = taskStorage.getAllTasks(userId);
    const currentHour = new Date().getHours();
    
    return tasks
      .filter(task => task.completionPercentage < 100)
      .sort((a, b) => {
        // Prioritize by deadline, priority, and user's historical productivity with similar tasks
        let scoreA = 0, scoreB = 0;

        // Deadline urgency
        if (a.dueDate) scoreA += Math.max(0, 10 - (a.dueDate - Date.now()) / (1000 * 60 * 60 * 24));
        if (b.dueDate) scoreB += Math.max(0, 10 - (b.dueDate - Date.now()) / (1000 * 60 * 60 * 24));

        // Priority
        const priorityScore = { high: 3, medium: 2, low: 1 };
        scoreA += priorityScore[a.priority];
        scoreB += priorityScore[b.priority];

        // Historical productivity
        if (a.subjectId && pattern.subjectProductivity[a.subjectId]) {
          scoreA += pattern.subjectProductivity[a.subjectId];
        }
        if (b.subjectId && pattern.subjectProductivity[b.subjectId]) {
          scoreB += pattern.subjectProductivity[b.subjectId];
        }

        return scoreB - scoreA;
      })
      .slice(0, 3);
  }

  private isQuietHour(currentHour: number, settings: NotificationSettings): boolean {
    const { start, end } = settings.quietHours;
    
    if (start <= end) {
      return currentHour >= start && currentHour < end;
    } else {
      // Quiet hours span midnight
      return currentHour >= start || currentHour < end;
    }
  }

  // Notification management methods

  private scheduleNotification(notification: Omit<SmartNotification, 'id' | 'createdAt' | 'isRead' | 'isDismissed'>): void {
    const fullNotification: SmartNotification = {
      ...notification,
      id: this.generateNotificationId(),
      createdAt: Date.now(),
      isRead: false,
      isDismissed: false
    };

    this.notificationQueue.push(fullNotification);
    this.saveToStorage();
  }

  private processNotificationQueue(): void {
    if (this.isProcessing) return;
    this.isProcessing = true;

    const now = Date.now();
    const readyNotifications = this.notificationQueue.filter(n => n.scheduledFor <= now);

    readyNotifications.forEach(notification => {
      this.showNotification(notification);
      this.notifications.push(notification);
      
      // Remove from queue
      const index = this.notificationQueue.indexOf(notification);
      if (index > -1) {
        this.notificationQueue.splice(index, 1);
      }
    });

    this.isProcessing = false;
    this.saveToStorage();
  }

  private showNotification(notification: SmartNotification): void {
    const { toast } = useToast();

    toast({
      title: notification.title,
      description: notification.description,
      variant: notification.priority === 'urgent' ? 'destructive' : 'default',
      duration: this.getNotificationDuration(notification.priority),
    });
  }

  private getNotificationDuration(priority: NotificationPriority): number {
    switch (priority) {
      case 'urgent': return 10000; // 10 seconds
      case 'high': return 8000; // 8 seconds
      case 'medium': return 6000; // 6 seconds
      case 'low': return 4000; // 4 seconds
      default: return 5000;
    }
  }

  // Action handlers

  private startTaskTimer(userId: string, taskId: string): void {
    const timerStore = useEnhancedTimerStore.getState();
    timerStore.linkTaskToTimer(taskId, userId);
    timerStore.startTimer(taskId, userId);
  }

  private openTaskReschedule(taskId: string): void {
    // This would open a modal or navigate to task editing
    console.log('Opening task reschedule for:', taskId);
  }

  private initiateBreak(userId: string): void {
    const timerStore = useEnhancedTimerStore.getState();
    timerStore.pauseTimer();
    
    // Schedule break end reminder
    this.scheduleNotification({
      type: 'break_suggestion',
      priority: 'low',
      title: 'Break time is over! 🚀',
      description: 'Ready to get back to work? Your task is waiting for you.',
      scheduledFor: Date.now() + (15 * 60 * 1000), // 15 minute break
      actionable: true,
      actions: [
        {
          id: 'resume_timer',
          label: 'Resume Timer',
          type: 'primary',
          action: () => timerStore.startTimer()
        }
      ]
    });
  }

  private snoozeBreakSuggestion(minutes: number): void {
    this.scheduleNotification({
      type: 'break_suggestion',
      priority: 'medium',
      title: 'Time for that break! 🧠',
      description: `You've been working hard. Consider taking a break now.`,
      scheduledFor: Date.now() + (minutes * 60 * 1000),
      actionable: true,
      actions: [
        {
          id: 'take_break',
          label: 'Take Break',
          type: 'primary',
          action: () => this.initiateBreak('')
        }
      ]
    });
  }

  private dismissBreakSuggestion(): void {
    // Just dismiss, no further action needed
  }

  private startSmallSession(userId: string, taskId: string, minutes: number): void {
    this.startTaskTimer(userId, taskId);
    
    // Schedule reminder to stop after small session
    this.scheduleNotification({
      type: 'context_reminder',
      priority: 'low',
      title: `Great job! 🎉`,
      description: `You've completed your ${minutes}-minute session. How do you feel about continuing?`,
      scheduledFor: Date.now() + (minutes * 60 * 1000),
      actionable: true,
      actions: [
        {
          id: 'continue',
          label: 'Keep Going',
          type: 'primary',
          action: () => {}
        },
        {
          id: 'stop',
          label: 'Stop Here',
          type: 'secondary',
          action: () => useEnhancedTimerStore.getState().stopTimer()
        }
      ]
    });
  }

  private openTaskBreakdown(taskId: string): void {
    // This would open a modal for breaking down the task
    console.log('Opening task breakdown for:', taskId);
  }

  private navigateToTasks(): void {
    // This would navigate to the tasks page
    window.location.href = '/tasks';
  }

  // Pattern analysis and learning

  private analyzeProductivityPatterns(userId: string): void {
    const tasks = taskStorage.getAllTasks(userId);
    const completedTasks = tasks.filter(t => t.completionPercentage === 100);
    
    if (completedTasks.length === 0) return;

    const pattern = this.getPattern(userId);

    // Analyze peak hours
    const hourCounts = new Array(24).fill(0);
    completedTasks.forEach(task => {
      const hour = new Date(task.updatedAt).getHours();
      hourCounts[hour]++;
    });

    const maxCount = Math.max(...hourCounts);
    pattern.peakHours = hourCounts
      .map((count, hour) => ({ hour, count }))
      .filter(({ count }) => count >= maxCount * 0.7) // Top 70% of productive hours
      .map(({ hour }) => hour);

    // Analyze subject productivity
    const subjectStats: Record<string, { total: number; completed: number; avgTime: number }> = {};
    
    tasks.forEach(task => {
      if (task.subjectId) {
        if (!subjectStats[task.subjectId]) {
          subjectStats[task.subjectId] = { total: 0, completed: 0, avgTime: 0 };
        }
        subjectStats[task.subjectId].total++;
        if (task.completionPercentage === 100) {
          subjectStats[task.subjectId].completed++;
        }
        if (task.actualTimeSpent) {
          subjectStats[task.subjectId].avgTime += task.actualTimeSpent;
        }
      }
    });

    Object.entries(subjectStats).forEach(([subjectId, stats]) => {
      const completionRate = stats.completed / stats.total;
      const efficiency = stats.avgTime > 0 ? stats.completed / (stats.avgTime / 60) : 0; // tasks per hour
      pattern.subjectProductivity[subjectId] = Math.round((completionRate * 5) + (efficiency * 2));
    });

    // Update pattern
    pattern.lastActiveDate = Date.now();
    this.patterns.set(userId, pattern);
    this.saveToStorage();
  }

  private createDefaultPattern(userId: string): ProductivityPattern {
    return {
      userId,
      peakHours: [9, 10, 11, 14, 15, 16], // Default productive hours
      averageSessionLength: 45, // 45 minutes
      preferredBreakLength: 15, // 15 minutes
      procrastinationTriggers: [],
      completionStreak: 0,
      lastActiveDate: Date.now(),
      taskTypePreferences: {},
      subjectProductivity: {}
    };
  }

  // Storage and persistence

  private loadFromStorage(): void {
    try {
      const notificationsData = localStorage.getItem('smart_notifications');
      if (notificationsData) {
        this.notifications = JSON.parse(notificationsData);
      }

      const queueData = localStorage.getItem('notification_queue');
      if (queueData) {
        this.notificationQueue = JSON.parse(queueData);
      }

      const patternsData = localStorage.getItem('productivity_patterns');
      if (patternsData) {
        const patterns = JSON.parse(patternsData);
        this.patterns = new Map(Object.entries(patterns));
      }

      const settingsData = localStorage.getItem('notification_settings');
      if (settingsData) {
        const settings = JSON.parse(settingsData);
        this.settings = new Map(Object.entries(settings));
      }
    } catch (error) {
      console.error('Error loading notification data from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem('smart_notifications', JSON.stringify(this.notifications));
      localStorage.setItem('notification_queue', JSON.stringify(this.notificationQueue));
      localStorage.setItem('productivity_patterns', JSON.stringify(Object.fromEntries(this.patterns)));
      localStorage.setItem('notification_settings', JSON.stringify(Object.fromEntries(this.settings)));
    } catch (error) {
      console.error('Error saving notification data to storage:', error);
    }
  }

  // Utility methods

  private generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getPattern(userId: string): ProductivityPattern {
    return this.patterns.get(userId) || this.createDefaultPattern(userId);
  }

  private getSettings(userId: string): NotificationSettings {
    return this.settings.get(userId) || { ...DEFAULT_NOTIFICATION_SETTINGS };
  }

  private getLastWorkedOnTime(userId: string, taskId: string): number | null {
    const task = taskStorage.getTask(userId, taskId);
    return task?.lastTimerSession?.endTime || null;
  }

  private identifyProcrastinationReason(task: EnhancedTodoItem, pattern: ProductivityPattern): string {
    if (task.difficultyLevel === 'hard') return 'task_difficulty';
    if (task.subjectId && pattern.procrastinationTriggers.includes(task.subjectId)) return 'subject_avoidance';
    if (!task.timeEstimate) return 'unclear_scope';
    if (task.description.length < 50) return 'insufficient_detail';
    return 'general_procrastination';
  }

  // Public API methods

  public getNotifications(userId: string): SmartNotification[] {
    return this.notifications.filter(n => !n.isDismissed);
  }

  public markNotificationRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      this.saveToStorage();
    }
  }

  public dismissNotification(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isDismissed = true;
      this.saveToStorage();
    }
  }

  public updateSettings(userId: string, newSettings: Partial<NotificationSettings>): void {
    const currentSettings = this.getSettings(userId);
    this.settings.set(userId, { ...currentSettings, ...newSettings });
    this.saveToStorage();
  }

  public getUserSettings(userId: string): NotificationSettings {
    return this.settings.get(userId) || { ...DEFAULT_NOTIFICATION_SETTINGS };
  }

  public runAnalysis(userId: string): void {
    this.initialize(userId);
    this.scheduleDeadlineReminders(userId);
    this.scheduleBreakSuggestions(userId);
    this.detectProcrastinationAndNudge(userId);
    this.trackProductivityStreak(userId);
    this.scheduleContextAwareReminders(userId);
  }

  // Lifecycle methods

  private startNotificationProcessor(): void {
    // Process notifications every minute
    this.intervalId = setInterval(() => {
      this.processNotificationQueue();
    }, 60000);
  }

  public destroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}

// Export singleton instance
export const smartNotificationService = new SmartNotificationService();

// Export types for use in other components
export type {
  SmartNotification,
  NotificationAction,
  ProductivityPattern,
  NotificationSettings,
  NotificationType,
  NotificationPriority
};