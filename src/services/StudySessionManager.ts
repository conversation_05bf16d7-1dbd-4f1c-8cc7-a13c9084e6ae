import { 
  saveStudySessionWithTaskAssociation, 
  getStudySessionsByTaskId, 
  getTaskTimeAnalytics,
  EnhancedStudySessionData,
  SessionFeedback,
  TaskTimeAnalytics
} from '../utils/supabase';
import { taskStorage } from '../utils/taskLocalStorage';
import { EnhancedTodoItem } from '../types/todo';
import { aiTaskRecommendationEngine } from './AITaskRecommendationEngine';

export interface ActiveSession {
  id: string;
  taskId?: string;
  taskName: string;
  startTime: Date;
  pausedTime?: Date;
  totalPausedDuration: number; // in seconds
  status: 'active' | 'paused' | 'completed';
  subject?: string;
  taskType?: string;
  estimatedDuration?: number;
}

export interface SessionPauseRecord {
  sessionId: string;
  pauseStart: Date;
  pauseEnd?: Date;
  reason?: string;
}

class StudySessionManager {
  private activeSession: ActiveSession | null = null;
  private pauseRecords: SessionPauseRecord[] = [];
  private sessionStorageKey = 'active_study_session';
  private pauseStorageKey = 'session_pause_records';

  constructor() {
    this.loadActiveSession();
    this.loadPauseRecords();
  }

  // Load active session from localStorage
  private loadActiveSession(): void {
    try {
      const stored = localStorage.getItem(this.sessionStorageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.activeSession = {
          ...parsed,
          startTime: new Date(parsed.startTime),
          pausedTime: parsed.pausedTime ? new Date(parsed.pausedTime) : undefined
        };
      }
    } catch (error) {
      console.error('Error loading active session:', error);
      this.activeSession = null;
    }
  }

  // Save active session to localStorage
  private saveActiveSession(): void {
    try {
      if (this.activeSession) {
        localStorage.setItem(this.sessionStorageKey, JSON.stringify(this.activeSession));
      } else {
        localStorage.removeItem(this.sessionStorageKey);
      }
    } catch (error) {
      console.error('Error saving active session:', error);
    }
  }

  // Load pause records from localStorage
  private loadPauseRecords(): void {
    try {
      const stored = localStorage.getItem(this.pauseStorageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.pauseRecords = parsed.map((record: any) => ({
          ...record,
          pauseStart: new Date(record.pauseStart),
          pauseEnd: record.pauseEnd ? new Date(record.pauseEnd) : undefined
        }));
      }
    } catch (error) {
      console.error('Error loading pause records:', error);
      this.pauseRecords = [];
    }
  }

  // Save pause records to localStorage
  private savePauseRecords(): void {
    try {
      localStorage.setItem(this.pauseStorageKey, JSON.stringify(this.pauseRecords));
    } catch (error) {
      console.error('Error saving pause records:', error);
    }
  }

  // Start a new study session with task association
  async startSession(
    taskId: string | undefined,
    taskName: string,
    subject?: string,
    taskType?: string,
    estimatedDuration?: number
  ): Promise<ActiveSession> {
    // End any existing session first
    if (this.activeSession) {
      await this.endSession();
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.activeSession = {
      id: sessionId,
      taskId,
      taskName,
      startTime: new Date(),
      totalPausedDuration: 0,
      status: 'active',
      subject,
      taskType,
      estimatedDuration
    };

    this.saveActiveSession();

    // Update task status to "In Progress" if taskId is provided
    if (taskId) {
      try {
        const tasks = taskStorage.getTasks();
        const taskIndex = tasks.findIndex(t => t.id === taskId);
        if (taskIndex !== -1) {
          tasks[taskIndex].column_id = 'in-progress';
          tasks[taskIndex].updated_at = Date.now();
          taskStorage.saveTasks(tasks);
        }
      } catch (error) {
        console.error('Error updating task status:', error);
      }
    }

    return this.activeSession;
  }

  // Pause the current session
  pauseSession(reason?: string): boolean {
    if (!this.activeSession || this.activeSession.status !== 'active') {
      return false;
    }

    const pauseRecord: SessionPauseRecord = {
      sessionId: this.activeSession.id,
      pauseStart: new Date(),
      reason
    };

    this.pauseRecords.push(pauseRecord);
    this.activeSession.status = 'paused';
    this.activeSession.pausedTime = new Date();

    this.saveActiveSession();
    this.savePauseRecords();

    return true;
  }

  // Resume the current session
  resumeSession(): boolean {
    if (!this.activeSession || this.activeSession.status !== 'paused') {
      return false;
    }

    // Find the most recent pause record for this session
    const pauseRecord = this.pauseRecords
      .filter(p => p.sessionId === this.activeSession!.id && !p.pauseEnd)
      .pop();

    if (pauseRecord && this.activeSession.pausedTime) {
      pauseRecord.pauseEnd = new Date();
      const pauseDuration = (pauseRecord.pauseEnd.getTime() - pauseRecord.pauseStart.getTime()) / 1000;
      this.activeSession.totalPausedDuration += pauseDuration;
    }

    this.activeSession.status = 'active';
    this.activeSession.pausedTime = undefined;

    this.saveActiveSession();
    this.savePauseRecords();

    return true;
  }

  // End the current session and save to Supabase
  async endSession(feedback?: SessionFeedback): Promise<void> {
    if (!this.activeSession) {
      return;
    }

    try {
      const endTime = new Date();
      const totalDuration = Math.floor((endTime.getTime() - this.activeSession.startTime.getTime()) / 1000);
      const activeDuration = totalDuration - this.activeSession.totalPausedDuration;

      // Prepare session data for Supabase
      const sessionData: EnhancedStudySessionData = {
        id: this.activeSession.id,
        user_id: '', // Will be set by the calling component with actual user ID
        task_name: this.activeSession.taskName,
        subject: this.activeSession.subject || null,
        task_type: this.activeSession.taskType || null,
        start_time: this.activeSession.startTime.toISOString(),
        end_time: endTime.toISOString(),
        date: this.activeSession.startTime.toISOString().split('T')[0],
        duration: activeDuration,
        completed: true,
        estimated_duration: this.activeSession.estimatedDuration,
        productivity_rating: feedback?.productivityRating || null,
        difficulty_rating: feedback?.difficultyRating || null,
        focus_rating: feedback?.focusRating || null,
        notes: feedback?.notes || null
      };

      // Save session to Supabase (will be called by the component with user context)
      return sessionData as any; // Return data for the component to save with user context

    } catch (error) {
      console.error('Error ending session:', error);
      throw error;
    } finally {
      // Clean up local state
      this.activeSession = null;
      this.pauseRecords = [];
      this.saveActiveSession();
      this.savePauseRecords();
    }
  }

  // End session with AI learning updates
  async endSessionWithAILearning(userId: string, feedback?: SessionFeedback): Promise<any> {
    if (!this.activeSession) {
      return;
    }

    try {
      const endTime = new Date();
      const totalDuration = Math.floor((endTime.getTime() - this.activeSession.startTime.getTime()) / 1000);
      const activeDuration = totalDuration - this.activeSession.totalPausedDuration;
      const activeDurationMinutes = Math.floor(activeDuration / 60);

      // Prepare session data for Supabase
      const sessionData: EnhancedStudySessionData = {
        id: this.activeSession.id,
        user_id: userId,
        task_name: this.activeSession.taskName,
        subject: this.activeSession.subject || null,
        task_type: this.activeSession.taskType || null,
        start_time: this.activeSession.startTime.toISOString(),
        end_time: endTime.toISOString(),
        date: this.activeSession.startTime.toISOString().split('T')[0],
        duration: activeDuration,
        completed: true,
        estimated_duration: this.activeSession.estimatedDuration,
        productivity_rating: feedback?.productivityRating || null,
        difficulty_rating: feedback?.difficultyRating || null,
        focus_rating: feedback?.focusRating || null,
        notes: feedback?.notes || null
      };

      // Save session to Supabase
      const savedSession = await saveStudySessionWithTaskAssociation(sessionData, feedback);

      // Update AI learning data if we have a task ID
      if (this.activeSession.taskId && feedback?.productivityRating) {
        try {
          const estimatedDurationMinutes = this.activeSession.estimatedDuration || 0;
          await aiTaskRecommendationEngine.updateLearningData(
            userId,
            this.activeSession.taskId,
            activeDurationMinutes,
            estimatedDurationMinutes,
            feedback.productivityRating,
            true // session completed
          );
        } catch (aiError) {
          console.error('Error updating AI learning data:', aiError);
          // Don't throw - AI learning failure shouldn't break session saving
        }
      }

      return savedSession;

    } catch (error) {
      console.error('Error ending session with AI learning:', error);
      throw error;
    } finally {
      // Clean up local state
      this.activeSession = null;
      this.pauseRecords = [];
      this.saveActiveSession();
      this.savePauseRecords();
    }
  }

  // Get current active session
  getActiveSession(): ActiveSession | null {
    return this.activeSession;
  }

  // Get current session duration (excluding paused time)
  getCurrentSessionDuration(): number {
    if (!this.activeSession) {
      return 0;
    }

    const now = new Date();
    const totalDuration = (now.getTime() - this.activeSession.startTime.getTime()) / 1000;
    
    // Add current pause duration if session is paused
    let currentPauseDuration = this.activeSession.totalPausedDuration;
    if (this.activeSession.status === 'paused' && this.activeSession.pausedTime) {
      currentPauseDuration += (now.getTime() - this.activeSession.pausedTime.getTime()) / 1000;
    }

    return Math.max(0, totalDuration - currentPauseDuration);
  }

  // Update task time when session completes
  async updateTaskTime(taskId: string, sessionDuration: number): Promise<void> {
    try {
      const tasks = taskStorage.getTasks();
      const taskIndex = tasks.findIndex(t => t.id === taskId);
      
      if (taskIndex !== -1) {
        const task = tasks[taskIndex];
        const currentTime = task.actualTimeSpent || 0;
        const newTime = currentTime + Math.floor(sessionDuration / 60); // Convert to minutes
        
        tasks[taskIndex] = {
          ...task,
          actualTimeSpent: newTime,
          updated_at: Date.now()
        };
        
        taskStorage.saveTasks(tasks);
      }
    } catch (error) {
      console.error('Error updating task time:', error);
    }
  }

  // Get session history for a specific task
  async getTaskSessionHistory(userId: string, taskId: string): Promise<TaskTimeAnalytics | null> {
    try {
      // First try to get by task ID, then fall back to task name
      const task = taskStorage.getTasks().find(t => t.id === taskId);
      if (!task) {
        return null;
      }

      return await getTaskTimeAnalytics(userId, task.title);
    } catch (error) {
      console.error('Error getting task session history:', error);
      return null;
    }
  }

  // Check if there's an active timer for a specific task
  isTaskTimerActive(taskId: string): boolean {
    return this.activeSession?.taskId === taskId && this.activeSession.status === 'active';
  }

  // Get pause records for current session
  getCurrentSessionPauses(): SessionPauseRecord[] {
    if (!this.activeSession) {
      return [];
    }
    return this.pauseRecords.filter(p => p.sessionId === this.activeSession!.id);
  }
}

// Export singleton instance
export const studySessionManager = new StudySessionManager();
export default StudySessionManager;