import { EnhancedTodoItem } from '../types/todo';
import { taskStorage } from '../utils/taskLocalStorage';
import { getStudySessions, getTaskTimeAnalytics } from '../utils/supabase';
import { calculatePrepInsightMetrics, StudySession } from '../utils/studyAnalytics';

// Task recommendation interfaces
export interface TaskRecommendation {
  taskId: string;
  task: EnhancedTodoItem;
  score: number; // 0-100 recommendation score
  reason: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number; // in minutes
  optimalTimeSlot?: TimeSlot;
  confidence: number; // 0-100 confidence in recommendation
}

export interface TimeSlot {
  startTime: Date;
  endTime: Date;
  type: 'morning' | 'afternoon' | 'evening' | 'night';
  productivityScore: number; // based on historical data
}

export interface ProductivityPattern {
  userId: string;
  hourlyProductivity: Record<number, number>; // hour (0-23) -> productivity score
  subjectPreferences: Record<string, number>; // subject -> productivity score
  taskTypePreferences: Record<string, number>; // task type -> productivity score
  optimalSessionLength: number; // in minutes
  breakFrequency: number; // minutes between breaks
  weeklyPatterns: Record<string, number>; // day of week -> productivity score
  lastUpdated: Date;
}

export interface BreakSuggestion {
  type: 'short' | 'long' | 'task-switch';
  duration: number; // in minutes
  reason: string;
  activities: string[];
}

export interface LearningData {
  taskCompletionAccuracy: Record<string, number>; // task type -> accuracy
  timeEstimationAccuracy: Record<string, number>; // task type -> accuracy
  productivityTrends: Array<{
    date: Date;
    productivity: number;
    factors: string[];
  }>;
  improvementSuggestions: string[];
}

/**
 * AI-powered task recommendation engine that learns from user patterns
 */
export class AITaskRecommendationEngine {
  private static instance: AITaskRecommendationEngine;
  private productivityPatterns: Map<string, ProductivityPattern> = new Map();
  private learningData: Map<string, LearningData> = new Map();

  private constructor() {
    this.loadStoredPatterns();
  }

  public static getInstance(): AITaskRecommendationEngine {
    if (!AITaskRecommendationEngine.instance) {
      AITaskRecommendationEngine.instance = new AITaskRecommendationEngine();
    }
    return AITaskRecommendationEngine.instance;
  }

  /**
   * Get AI-powered task recommendations based on current context
   */
  async getTaskRecommendations(
    userId: string,
    currentTime: Date = new Date(),
    availableTime: number = 60, // minutes
    context: 'productivity' | 'break' | 'planning' = 'productivity'
  ): Promise<TaskRecommendation[]> {
    try {
      // Get all tasks for the user
      const tasks = taskStorage.getTasksWithHierarchy(userId);
      const incompleteTasks = tasks.filter(task => task.completionPercentage < 100);

      if (incompleteTasks.length === 0) {
        return [];
      }

      // Update productivity patterns
      await this.updateProductivityPatterns(userId);

      // Get user's productivity pattern
      const pattern = this.productivityPatterns.get(userId);
      
      // Score each task
      const recommendations: TaskRecommendation[] = [];
      
      for (const task of incompleteTasks) {
        const score = await this.calculateTaskScore(task, userId, currentTime, availableTime, pattern);
        const urgency = this.calculateUrgency(task, currentTime);
        const estimatedDuration = this.estimateTaskDuration(task, pattern);
        const optimalTimeSlot = this.findOptimalTimeSlot(task, currentTime, estimatedDuration, pattern);
        const confidence = this.calculateConfidence(task, pattern);
        const reason = this.generateRecommendationReason(task, score, urgency, currentTime, pattern);

        recommendations.push({
          taskId: task.id,
          task,
          score,
          reason,
          urgency,
          estimatedDuration,
          optimalTimeSlot,
          confidence
        });
      }

      // Sort by score and return top recommendations
      return recommendations
        .sort((a, b) => b.score - a.score)
        .slice(0, 10); // Return top 10 recommendations

    } catch (error) {
      console.error('Error generating task recommendations:', error);
      return [];
    }
  }

  /**
   * Get optimal time block suggestions for task completion
   */
  async getOptimalTimeBlocks(
    userId: string,
    targetDate: Date = new Date(),
    workingHours: { start: number; end: number } = { start: 9, end: 17 }
  ): Promise<TimeSlot[]> {
    try {
      const pattern = this.productivityPatterns.get(userId);
      if (!pattern) {
        return this.getDefaultTimeBlocks(workingHours);
      }

      const timeSlots: TimeSlot[] = [];
      const dayOfWeek = targetDate.toLocaleDateString('en-US', { weekday: 'long' });
      const dayProductivity = pattern.weeklyPatterns[dayOfWeek] || 0.7;

      // Generate 2-hour time blocks within working hours
      for (let hour = workingHours.start; hour < workingHours.end - 1; hour += 2) {
        const startTime = new Date(targetDate);
        startTime.setHours(hour, 0, 0, 0);
        
        const endTime = new Date(startTime);
        endTime.setHours(hour + 2);

        const hourlyProductivity = pattern.hourlyProductivity[hour] || 0.5;
        const productivityScore = (hourlyProductivity + dayProductivity) / 2;

        const timeType = this.getTimeType(hour);

        timeSlots.push({
          startTime,
          endTime,
          type: timeType,
          productivityScore: productivityScore * 100
        });
      }

      return timeSlots.sort((a, b) => b.productivityScore - a.productivityScore);

    } catch (error) {
      console.error('Error generating optimal time blocks:', error);
      return this.getDefaultTimeBlocks(workingHours);
    }
  }

  /**
   * Analyze productivity patterns for personalized recommendations
   */
  async analyzeProductivityPatterns(userId: string): Promise<ProductivityPattern> {
    try {
      // Get study sessions from Supabase
      const sessions = await getStudySessions(userId);
      
      // Initialize pattern
      const pattern: ProductivityPattern = {
        userId,
        hourlyProductivity: {},
        subjectPreferences: {},
        taskTypePreferences: {},
        optimalSessionLength: 45, // default
        breakFrequency: 25, // default pomodoro
        weeklyPatterns: {},
        lastUpdated: new Date()
      };

      if (sessions.length === 0) {
        return pattern;
      }

      // Analyze hourly productivity
      const hourlyData: Record<number, { total: number; productive: number }> = {};
      
      sessions.forEach(session => {
        const startTime = new Date(session.start_time);
        const hour = startTime.getHours();
        
        if (!hourlyData[hour]) {
          hourlyData[hour] = { total: 0, productive: 0 };
        }
        
        hourlyData[hour].total++;
        if (session.productivity_rating && session.productivity_rating >= 4) {
          hourlyData[hour].productive++;
        }
      });

      // Calculate hourly productivity scores
      Object.keys(hourlyData).forEach(hourStr => {
        const hour = parseInt(hourStr);
        const data = hourlyData[hour];
        pattern.hourlyProductivity[hour] = data.total > 0 ? data.productive / data.total : 0.5;
      });

      // Analyze subject preferences
      const subjectData: Record<string, { total: number; productive: number }> = {};
      
      sessions.forEach(session => {
        if (session.subject) {
          if (!subjectData[session.subject]) {
            subjectData[session.subject] = { total: 0, productive: 0 };
          }
          
          subjectData[session.subject].total++;
          if (session.productivity_rating && session.productivity_rating >= 4) {
            subjectData[session.subject].productive++;
          }
        }
      });

      Object.keys(subjectData).forEach(subject => {
        const data = subjectData[subject];
        pattern.subjectPreferences[subject] = data.total > 0 ? data.productive / data.total : 0.5;
      });

      // Analyze task type preferences
      const taskTypeData: Record<string, { total: number; productive: number }> = {};
      
      sessions.forEach(session => {
        if (session.task_type) {
          if (!taskTypeData[session.task_type]) {
            taskTypeData[session.task_type] = { total: 0, productive: 0 };
          }
          
          taskTypeData[session.task_type].total++;
          if (session.productivity_rating && session.productivity_rating >= 4) {
            taskTypeData[session.task_type].productive++;
          }
        }
      });

      Object.keys(taskTypeData).forEach(taskType => {
        const data = taskTypeData[taskType];
        pattern.taskTypePreferences[taskType] = data.total > 0 ? data.productive / data.total : 0.5;
      });

      // Calculate optimal session length
      const completedSessions = sessions.filter(s => s.completed && s.duration > 0);
      if (completedSessions.length > 0) {
        const avgDuration = completedSessions.reduce((sum, s) => sum + s.duration, 0) / completedSessions.length;
        pattern.optimalSessionLength = Math.round(avgDuration / 60); // convert to minutes
      }

      // Analyze weekly patterns
      const weeklyData: Record<string, { total: number; productive: number }> = {};
      
      sessions.forEach(session => {
        const date = new Date(session.date);
        const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
        
        if (!weeklyData[dayOfWeek]) {
          weeklyData[dayOfWeek] = { total: 0, productive: 0 };
        }
        
        weeklyData[dayOfWeek].total++;
        if (session.productivity_rating && session.productivity_rating >= 4) {
          weeklyData[dayOfWeek].productive++;
        }
      });

      Object.keys(weeklyData).forEach(day => {
        const data = weeklyData[day];
        pattern.weeklyPatterns[day] = data.total > 0 ? data.productive / data.total : 0.5;
      });

      // Store the pattern
      this.productivityPatterns.set(userId, pattern);
      this.savePatternToStorage(userId, pattern);

      return pattern;

    } catch (error) {
      console.error('Error analyzing productivity patterns:', error);
      // Return default pattern
      return {
        userId,
        hourlyProductivity: {},
        subjectPreferences: {},
        taskTypePreferences: {},
        optimalSessionLength: 45,
        breakFrequency: 25,
        weeklyPatterns: {},
        lastUpdated: new Date()
      };
    }
  }

  /**
   * Generate smart break and task switching suggestions
   */
  getBreakSuggestions(
    currentTask: EnhancedTodoItem,
    sessionDuration: number, // in minutes
    userId: string
  ): BreakSuggestion[] {
    const pattern = this.productivityPatterns.get(userId);
    const suggestions: BreakSuggestion[] = [];

    // Short break suggestion
    if (sessionDuration >= 25) {
      suggestions.push({
        type: 'short',
        duration: 5,
        reason: 'You\'ve been focused for 25 minutes. A short break will help maintain concentration.',
        activities: ['Stretch', 'Deep breathing', 'Look away from screen', 'Hydrate']
      });
    }

    // Long break suggestion
    if (sessionDuration >= 90) {
      suggestions.push({
        type: 'long',
        duration: 15,
        reason: 'Extended focus session detected. A longer break will help restore mental energy.',
        activities: ['Walk around', 'Light exercise', 'Snack', 'Fresh air', 'Meditation']
      });
    }

    // Task switch suggestion based on difficulty
    if (currentTask.difficultyLevel === 'hard' && sessionDuration >= 45) {
      suggestions.push({
        type: 'task-switch',
        duration: 0,
        reason: 'Consider switching to an easier task to maintain momentum.',
        activities: ['Review completed work', 'Organize notes', 'Plan next steps']
      });
    }

    return suggestions;
  }

  /**
   * Learning algorithm that improves recommendations over time
   */
  async updateLearningData(
    userId: string,
    taskId: string,
    actualDuration: number,
    estimatedDuration: number,
    productivityRating: number,
    completed: boolean
  ): Promise<void> {
    try {
      let learningData = this.learningData.get(userId);
      if (!learningData) {
        learningData = {
          taskCompletionAccuracy: {},
          timeEstimationAccuracy: {},
          productivityTrends: [],
          improvementSuggestions: []
        };
      }

      const task = taskStorage.getTask(userId, taskId);
      if (!task) return;

      // Update time estimation accuracy
      const taskType = this.getTaskType(task);
      if (estimatedDuration > 0) {
        const accuracy = Math.max(0, 1 - Math.abs(actualDuration - estimatedDuration) / estimatedDuration);
        
        if (!learningData.timeEstimationAccuracy[taskType]) {
          learningData.timeEstimationAccuracy[taskType] = accuracy;
        } else {
          // Weighted average with more weight on recent data
          learningData.timeEstimationAccuracy[taskType] = 
            (learningData.timeEstimationAccuracy[taskType] * 0.7) + (accuracy * 0.3);
        }
      }

      // Update task completion accuracy
      if (completed) {
        if (!learningData.taskCompletionAccuracy[taskType]) {
          learningData.taskCompletionAccuracy[taskType] = 1;
        } else {
          learningData.taskCompletionAccuracy[taskType] = 
            (learningData.taskCompletionAccuracy[taskType] * 0.8) + (1 * 0.2);
        }
      }

      // Add productivity trend data
      learningData.productivityTrends.push({
        date: new Date(),
        productivity: productivityRating,
        factors: [taskType, task.priority, task.difficultyLevel]
      });

      // Keep only last 100 trend points
      if (learningData.productivityTrends.length > 100) {
        learningData.productivityTrends = learningData.productivityTrends.slice(-100);
      }

      // Generate improvement suggestions
      learningData.improvementSuggestions = this.generateImprovementSuggestions(learningData);

      // Store updated learning data
      this.learningData.set(userId, learningData);
      this.saveLearningDataToStorage(userId, learningData);

    } catch (error) {
      console.error('Error updating learning data:', error);
    }
  }

  // Private helper methods

  private async calculateTaskScore(
    task: EnhancedTodoItem,
    userId: string,
    currentTime: Date,
    availableTime: number,
    pattern?: ProductivityPattern
  ): Promise<number> {
    let score = 0;

    // Base score from priority (30% weight)
    const priorityScore = task.priority === 'high' ? 30 : task.priority === 'medium' ? 20 : 10;
    score += priorityScore;

    // Urgency score based on due date (25% weight)
    if (task.dueDate) {
      const dueDate = new Date(task.dueDate);
      const timeUntilDue = dueDate.getTime() - currentTime.getTime();
      const daysUntilDue = timeUntilDue / (1000 * 60 * 60 * 24);
      
      if (daysUntilDue < 0) {
        score += 25; // Overdue - highest urgency
      } else if (daysUntilDue < 1) {
        score += 20; // Due today
      } else if (daysUntilDue < 3) {
        score += 15; // Due soon
      } else if (daysUntilDue < 7) {
        score += 10; // Due this week
      } else {
        score += 5; // Due later
      }
    }

    // Time fit score (20% weight)
    const estimatedDuration = this.estimateTaskDuration(task, pattern);
    if (estimatedDuration <= availableTime) {
      score += 20;
    } else if (estimatedDuration <= availableTime * 1.5) {
      score += 10;
    }

    // Productivity pattern score (15% weight)
    if (pattern) {
      const currentHour = currentTime.getHours();
      const hourlyProductivity = pattern.hourlyProductivity[currentHour] || 0.5;
      
      const subjectProductivity = task.subjectName ? 
        pattern.subjectPreferences[task.subjectName] || 0.5 : 0.5;
      
      const taskType = this.getTaskType(task);
      const taskTypeProductivity = pattern.taskTypePreferences[taskType] || 0.5;
      
      const avgProductivity = (hourlyProductivity + subjectProductivity + taskTypeProductivity) / 3;
      score += avgProductivity * 15;
    }

    // Progress score (10% weight) - favor tasks that are started but not complete
    if (task.completionPercentage > 0 && task.completionPercentage < 100) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  private calculateUrgency(task: EnhancedTodoItem, currentTime: Date): 'low' | 'medium' | 'high' | 'critical' {
    if (!task.dueDate) {
      return task.priority === 'high' ? 'medium' : 'low';
    }

    const dueDate = new Date(task.dueDate);
    const timeUntilDue = dueDate.getTime() - currentTime.getTime();
    const daysUntilDue = timeUntilDue / (1000 * 60 * 60 * 24);

    if (daysUntilDue < 0) return 'critical'; // Overdue
    if (daysUntilDue < 1) return 'critical'; // Due today
    if (daysUntilDue < 3) return 'high'; // Due soon
    if (daysUntilDue < 7) return 'medium'; // Due this week
    return 'low'; // Due later
  }

  private estimateTaskDuration(task: EnhancedTodoItem, pattern?: ProductivityPattern): number {
    // Use task's time estimate if available
    if (task.timeEstimate && task.timeEstimate > 0) {
      return task.timeEstimate;
    }

    // Use learning data to estimate based on task type
    const taskType = this.getTaskType(task);
    const learningData = this.learningData.get(task.createdBy);
    
    if (learningData && learningData.timeEstimationAccuracy[taskType]) {
      // Use historical data to estimate
      const baseEstimate = this.getBaseEstimateForTaskType(taskType);
      const accuracy = learningData.timeEstimationAccuracy[taskType];
      return Math.round(baseEstimate * (1 + (1 - accuracy)));
    }

    // Default estimates based on task type and difficulty
    const baseEstimate = this.getBaseEstimateForTaskType(taskType);
    const difficultyMultiplier = task.difficultyLevel === 'hard' ? 1.5 : 
                                task.difficultyLevel === 'easy' ? 0.7 : 1.0;
    
    return Math.round(baseEstimate * difficultyMultiplier);
  }

  private findOptimalTimeSlot(
    task: EnhancedTodoItem,
    currentTime: Date,
    estimatedDuration: number,
    pattern?: ProductivityPattern
  ): TimeSlot | undefined {
    if (!pattern) return undefined;

    const currentHour = currentTime.getHours();
    let bestHour = currentHour;
    let bestScore = pattern.hourlyProductivity[currentHour] || 0.5;

    // Look for better time slots in the next 8 hours
    for (let i = 1; i <= 8; i++) {
      const hour = (currentHour + i) % 24;
      const score = pattern.hourlyProductivity[hour] || 0.5;
      
      if (score > bestScore) {
        bestScore = score;
        bestHour = hour;
      }
    }

    const startTime = new Date(currentTime);
    startTime.setHours(bestHour, 0, 0, 0);
    
    const endTime = new Date(startTime);
    endTime.setMinutes(endTime.getMinutes() + estimatedDuration);

    return {
      startTime,
      endTime,
      type: this.getTimeType(bestHour),
      productivityScore: bestScore * 100
    };
  }

  private calculateConfidence(task: EnhancedTodoItem, pattern?: ProductivityPattern): number {
    let confidence = 50; // Base confidence

    // Increase confidence if we have productivity pattern data
    if (pattern) {
      confidence += 20;
    }

    // Increase confidence if task has time estimate
    if (task.timeEstimate && task.timeEstimate > 0) {
      confidence += 15;
    }

    // Increase confidence if we have learning data
    const learningData = this.learningData.get(task.createdBy);
    if (learningData) {
      confidence += 15;
    }

    return Math.min(confidence, 100);
  }

  private generateRecommendationReason(
    task: EnhancedTodoItem,
    score: number,
    urgency: 'low' | 'medium' | 'high' | 'critical',
    currentTime: Date,
    pattern?: ProductivityPattern
  ): string {
    const reasons: string[] = [];

    // Urgency-based reasons
    if (urgency === 'critical') {
      if (task.dueDate && new Date(task.dueDate) < currentTime) {
        reasons.push('This task is overdue');
      } else {
        reasons.push('This task is due today');
      }
    } else if (urgency === 'high') {
      reasons.push('This task is due soon');
    }

    // Priority-based reasons
    if (task.priority === 'high') {
      reasons.push('High priority task');
    }

    // Progress-based reasons
    if (task.completionPercentage > 0 && task.completionPercentage < 100) {
      reasons.push('Continue your progress on this task');
    }

    // Time-based reasons
    if (pattern) {
      const currentHour = currentTime.getHours();
      const hourlyProductivity = pattern.hourlyProductivity[currentHour] || 0.5;
      
      if (hourlyProductivity > 0.7) {
        reasons.push('You\'re typically productive at this time');
      }
      
      if (task.subjectName && pattern.subjectPreferences[task.subjectName] > 0.7) {
        reasons.push(`You work well on ${task.subjectName} tasks`);
      }
    }

    // Default reason if no specific reasons
    if (reasons.length === 0) {
      reasons.push('Good task to work on now');
    }

    return reasons.join(', ');
  }

  private getTaskType(task: EnhancedTodoItem): string {
    if (task.tags.includes('lecture')) return 'Lecture';
    if (task.tags.includes('exercise')) return 'Exercise';
    if (task.tags.includes('reading')) return 'Reading';
    if (task.tags.includes('practice')) return 'Practice';
    if (task.tags.includes('assignment')) return 'Assignment';
    if (task.tags.includes('project')) return 'Project';
    return 'Study';
  }

  private getBaseEstimateForTaskType(taskType: string): number {
    const estimates: Record<string, number> = {
      'Lecture': 60,
      'Exercise': 30,
      'Reading': 45,
      'Practice': 40,
      'Assignment': 90,
      'Project': 120,
      'Study': 45
    };
    
    return estimates[taskType] || 45;
  }

  private getTimeType(hour: number): 'morning' | 'afternoon' | 'evening' | 'night' {
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  }

  private getDefaultTimeBlocks(workingHours: { start: number; end: number }): TimeSlot[] {
    const timeSlots: TimeSlot[] = [];
    const today = new Date();

    for (let hour = workingHours.start; hour < workingHours.end - 1; hour += 2) {
      const startTime = new Date(today);
      startTime.setHours(hour, 0, 0, 0);
      
      const endTime = new Date(startTime);
      endTime.setHours(hour + 2);

      timeSlots.push({
        startTime,
        endTime,
        type: this.getTimeType(hour),
        productivityScore: 70 // Default score
      });
    }

    return timeSlots;
  }

  private generateImprovementSuggestions(learningData: LearningData): string[] {
    const suggestions: string[] = [];

    // Analyze time estimation accuracy
    const avgTimeAccuracy = Object.values(learningData.timeEstimationAccuracy)
      .reduce((sum, acc) => sum + acc, 0) / Object.keys(learningData.timeEstimationAccuracy).length;

    if (avgTimeAccuracy < 0.7) {
      suggestions.push('Try breaking down large tasks into smaller, more predictable chunks');
      suggestions.push('Track your actual time spent to improve future estimates');
    }

    // Analyze productivity trends
    const recentTrends = learningData.productivityTrends.slice(-20);
    const avgProductivity = recentTrends.reduce((sum, trend) => sum + trend.productivity, 0) / recentTrends.length;

    if (avgProductivity < 3) {
      suggestions.push('Consider taking more frequent breaks to maintain focus');
      suggestions.push('Try working on easier tasks when energy is low');
    }

    // Task completion patterns
    const completionRates = Object.values(learningData.taskCompletionAccuracy);
    const avgCompletion = completionRates.reduce((sum, rate) => sum + rate, 0) / completionRates.length;

    if (avgCompletion < 0.8) {
      suggestions.push('Focus on completing tasks before starting new ones');
      suggestions.push('Set smaller, more achievable goals');
    }

    return suggestions.slice(0, 3); // Return top 3 suggestions
  }

  private async updateProductivityPatterns(userId: string): Promise<void> {
    const existingPattern = this.productivityPatterns.get(userId);
    
    // Update patterns if they're older than 24 hours or don't exist
    if (!existingPattern || 
        (new Date().getTime() - existingPattern.lastUpdated.getTime()) > 24 * 60 * 60 * 1000) {
      await this.analyzeProductivityPatterns(userId);
    }
  }

  private loadStoredPatterns(): void {
    try {
      const stored = localStorage.getItem('aiTaskRecommendationPatterns');
      if (stored) {
        const patterns = JSON.parse(stored);
        Object.keys(patterns).forEach(userId => {
          const pattern = patterns[userId];
          pattern.lastUpdated = new Date(pattern.lastUpdated);
          this.productivityPatterns.set(userId, pattern);
        });
      }

      const storedLearning = localStorage.getItem('aiTaskRecommendationLearning');
      if (storedLearning) {
        const learningData = JSON.parse(storedLearning);
        Object.keys(learningData).forEach(userId => {
          const data = learningData[userId];
          data.productivityTrends = data.productivityTrends.map((trend: any) => ({
            ...trend,
            date: new Date(trend.date)
          }));
          this.learningData.set(userId, data);
        });
      }
    } catch (error) {
      console.error('Error loading stored patterns:', error);
    }
  }

  private savePatternToStorage(userId: string, pattern: ProductivityPattern): void {
    try {
      const stored = localStorage.getItem('aiTaskRecommendationPatterns');
      const patterns = stored ? JSON.parse(stored) : {};
      patterns[userId] = pattern;
      localStorage.setItem('aiTaskRecommendationPatterns', JSON.stringify(patterns));
    } catch (error) {
      console.error('Error saving pattern to storage:', error);
    }
  }

  private saveLearningDataToStorage(userId: string, learningData: LearningData): void {
    try {
      const stored = localStorage.getItem('aiTaskRecommendationLearning');
      const allLearningData = stored ? JSON.parse(stored) : {};
      allLearningData[userId] = learningData;
      localStorage.setItem('aiTaskRecommendationLearning', JSON.stringify(allLearningData));
    } catch (error) {
      console.error('Error saving learning data to storage:', error);
    }
  }
}

// Export singleton instance
export const aiTaskRecommendationEngine = AITaskRecommendationEngine.getInstance();