import { EnhancedTodoItem } from '../types/todo';
import { taskStorage } from '../utils/taskLocalStorage';
import { 
  getStudySessions, 
  getTaskTimeAnalytics,
  TaskTimeAnalytics,
  StudySessionRow 
} from '../utils/supabase';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subWeeks, subMonths } from 'date-fns';

// Comprehensive analytics interfaces
export interface TaskCompletionMetrics {
  taskId: string;
  taskTitle: string;
  subject?: string;
  priority: 'low' | 'medium' | 'high';
  status: string;
  estimatedTime?: number; // minutes
  actualTimeSpent: number; // minutes
  timeEfficiency: number; // percentage (estimated/actual * 100)
  completionVelocity: number; // tasks completed per day
  daysToComplete?: number;
  isCompleted: boolean;
  createdAt: Date;
  completedAt?: Date;
  dueDate?: Date;
  isOverdue: boolean;
}

export interface ProductivityPattern {
  timeOfDay: string; // '00:00' format
  dayOfWeek: string; // 'Monday', 'Tuesday', etc.
  averageProductivity: number; // 1-5 rating
  totalSessions: number;
  totalTime: number; // seconds
  taskTypes: string[];
  subjects: string[];
}

export interface TimeEstimationAccuracy {
  taskId: string;
  taskTitle: string;
  estimatedTime: number; // minutes
  actualTime: number; // minutes
  accuracyPercentage: number; // how close estimate was to actual
  estimationError: number; // positive = overestimated, negative = underestimated
  taskType?: string;
  subject?: string;
  difficulty?: number;
}

export interface TaskTypeComparison {
  taskType: string;
  totalTasks: number;
  completedTasks: number;
  completionRate: number; // percentage
  averageTimeSpent: number; // minutes
  averageProductivity: number; // 1-5 rating
  averageEstimationAccuracy: number; // percentage
  mostProductiveTimeOfDay: string;
  averageSessionLength: number; // minutes
}

export interface SubjectComparison {
  subject: string;
  totalTasks: number;
  completedTasks: number;
  completionRate: number; // percentage
  totalTimeSpent: number; // minutes
  averageProductivity: number; // 1-5 rating
  averageEstimationAccuracy: number; // percentage
  taskTypes: string[];
  mostProductiveDay: string;
  trendDirection: 'improving' | 'declining' | 'stable';
}

export interface ComprehensiveAnalytics {
  overview: {
    totalTasks: number;
    completedTasks: number;
    totalTimeSpent: number; // minutes
    averageProductivity: number;
    overallEfficiency: number;
  };
  taskMetrics: TaskCompletionMetrics[];
  productivityPatterns: ProductivityPattern[];
  estimationAccuracy: TimeEstimationAccuracy[];
  taskTypeComparisons: TaskTypeComparison[];
  subjectComparisons: SubjectComparison[];
  trends: {
    completionVelocity: { date: string; velocity: number }[];
    productivityTrend: { date: string; productivity: number }[];
    timeSpentTrend: { date: string; timeSpent: number }[];
  };
  insights: {
    bestPerformingSubject: string;
    mostAccurateEstimationTaskType: string;
    mostProductiveTimeOfDay: string;
    mostProductiveDayOfWeek: string;
    improvementSuggestions: string[];
  };
}

export class TaskAnalyticsService {
  private static instance: TaskAnalyticsService;

  private constructor() {}

  public static getInstance(): TaskAnalyticsService {
    if (!TaskAnalyticsService.instance) {
      TaskAnalyticsService.instance = new TaskAnalyticsService();
    }
    return TaskAnalyticsService.instance;
  }

  /**
   * Get comprehensive analytics for all tasks and study sessions
   */
  async getComprehensiveAnalytics(userId: string, timeRange: 'week' | 'month' | 'quarter' | 'all' = 'month'): Promise<ComprehensiveAnalytics> {
    try {
      // Get all tasks and study sessions
      const tasks = taskStorage.getTasks(userId);
      const allSessions = await getStudySessions(userId);
      
      // Filter sessions by time range
      const filteredSessions = this.filterSessionsByTimeRange(allSessions, timeRange);
      
      // Calculate task completion metrics
      const taskMetrics = await this.calculateTaskCompletionMetrics(tasks, filteredSessions);
      
      // Analyze productivity patterns
      const productivityPatterns = this.analyzeProductivityPatterns(filteredSessions);
      
      // Calculate time estimation accuracy
      const estimationAccuracy = this.calculateTimeEstimationAccuracy(tasks, filteredSessions);
      
      // Compare task types
      const taskTypeComparisons = this.compareTaskTypes(tasks, filteredSessions);
      
      // Compare subjects
      const subjectComparisons = this.compareSubjects(tasks, filteredSessions);
      
      // Calculate trends
      const trends = this.calculateTrends(tasks, filteredSessions, timeRange);
      
      // Generate insights
      const insights = this.generateInsights(taskMetrics, productivityPatterns, estimationAccuracy, taskTypeComparisons, subjectComparisons);
      
      // Calculate overview metrics
      const overview = this.calculateOverviewMetrics(tasks, filteredSessions);

      return {
        overview,
        taskMetrics,
        productivityPatterns,
        estimationAccuracy,
        taskTypeComparisons,
        subjectComparisons,
        trends,
        insights
      };
    } catch (error) {
      console.error('Error getting comprehensive analytics:', error);
      throw error;
    }
  }

  /**
   * Get task completion velocity metrics
   */
  async getTaskCompletionVelocity(userId: string, timeRange: 'week' | 'month' | 'quarter' = 'month'): Promise<{ date: string; completed: number; velocity: number }[]> {
    try {
      const tasks = taskStorage.getTasks(userId);
      const completedTasks = tasks.filter(task => task.columnId === 'done' || task.completed);
      
      const now = new Date();
      const startDate = timeRange === 'week' ? subWeeks(now, 4) : 
                      timeRange === 'month' ? subMonths(now, 6) : 
                      subMonths(now, 12);
      
      const velocityData: { date: string; completed: number; velocity: number }[] = [];
      const interval = timeRange === 'week' ? 7 : 30; // days
      
      for (let i = 0; i < (timeRange === 'week' ? 4 : timeRange === 'month' ? 6 : 12); i++) {
        const periodStart = timeRange === 'week' ? subWeeks(now, i + 1) : subMonths(now, i + 1);
        const periodEnd = timeRange === 'week' ? subWeeks(now, i) : subMonths(now, i);
        
        const completedInPeriod = completedTasks.filter(task => {
          const completedDate = new Date(task.updatedAt || task.createdAt);
          return completedDate >= periodStart && completedDate <= periodEnd;
        });
        
        const velocity = completedInPeriod.length / interval; // tasks per day
        
        velocityData.unshift({
          date: format(periodStart, 'yyyy-MM-dd'),
          completed: completedInPeriod.length,
          velocity: Math.round(velocity * 100) / 100
        });
      }
      
      return velocityData;
    } catch (error) {
      console.error('Error calculating task completion velocity:', error);
      throw error;
    }
  }

  /**
   * Get time vs completion correlation analysis
   */
  async getTimeCompletionCorrelation(userId: string): Promise<{
    correlation: number;
    analysis: string;
    dataPoints: { timeSpent: number; completed: boolean; taskType: string; subject: string }[];
  }> {
    try {
      const tasks = taskStorage.getTasks(userId);
      const allSessions = await getStudySessions(userId);
      
      const dataPoints = tasks.map(task => {
        const taskSessions = allSessions.filter(session => 
          session.task_name === task.title || session.task_id === task.id
        );
        const timeSpent = taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60; // minutes
        
        return {
          timeSpent,
          completed: task.columnId === 'done' || task.completed || false,
          taskType: this.getTaskTypeFromTask(task),
          subject: task.subjectName || 'Unknown'
        };
      });
      
      // Calculate Pearson correlation coefficient
      const completedTasks = dataPoints.filter(dp => dp.completed);
      const incompleteTasks = dataPoints.filter(dp => !dp.completed);
      
      const avgTimeCompleted = completedTasks.length > 0 
        ? completedTasks.reduce((sum, dp) => sum + dp.timeSpent, 0) / completedTasks.length 
        : 0;
      const avgTimeIncomplete = incompleteTasks.length > 0 
        ? incompleteTasks.reduce((sum, dp) => sum + dp.timeSpent, 0) / incompleteTasks.length 
        : 0;
      
      // Simple correlation analysis
      const correlation = avgTimeCompleted > avgTimeIncomplete ? 
        Math.min((avgTimeCompleted - avgTimeIncomplete) / Math.max(avgTimeCompleted, 1), 1) : 
        -Math.min((avgTimeIncomplete - avgTimeCompleted) / Math.max(avgTimeIncomplete, 1), 1);
      
      let analysis = '';
      if (correlation > 0.3) {
        analysis = 'Strong positive correlation: More time spent generally leads to task completion.';
      } else if (correlation > 0.1) {
        analysis = 'Moderate positive correlation: Time spent has some impact on completion.';
      } else if (correlation < -0.1) {
        analysis = 'Negative correlation: Tasks with less time investment are more likely to be completed.';
      } else {
        analysis = 'Weak correlation: Time spent and completion rates are not strongly related.';
      }
      
      return {
        correlation: Math.round(correlation * 100) / 100,
        analysis,
        dataPoints
      };
    } catch (error) {
      console.error('Error calculating time completion correlation:', error);
      throw error;
    }
  }

  // Private helper methods

  private filterSessionsByTimeRange(sessions: StudySessionRow[], timeRange: 'week' | 'month' | 'quarter' | 'all'): StudySessionRow[] {
    if (timeRange === 'all') return sessions;
    
    const now = new Date();
    const startDate = timeRange === 'week' ? subWeeks(now, 1) :
                     timeRange === 'month' ? subMonths(now, 1) :
                     subMonths(now, 3);
    
    return sessions.filter(session => new Date(session.start_time) >= startDate);
  }

  private async calculateTaskCompletionMetrics(tasks: EnhancedTodoItem[], sessions: StudySessionRow[]): Promise<TaskCompletionMetrics[]> {
    return tasks.map(task => {
      const taskSessions = sessions.filter(session => 
        session.task_name === task.title || session.task_id === task.id
      );
      
      const actualTimeSpent = taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60; // minutes
      const estimatedTime = task.timeEstimate || 0;
      const timeEfficiency = estimatedTime > 0 ? (estimatedTime / Math.max(actualTimeSpent, 1)) * 100 : 0;
      
      const isCompleted = task.columnId === 'done' || task.completed || false;
      const createdAt = new Date(task.createdAt);
      const completedAt = isCompleted ? new Date(task.updatedAt || task.createdAt) : undefined;
      const daysToComplete = completedAt ? Math.ceil((completedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24)) : undefined;
      
      const now = new Date();
      const dueDate = task.dueDate ? new Date(task.dueDate) : undefined;
      const isOverdue = dueDate ? now > dueDate && !isCompleted : false;
      
      return {
        taskId: task.id,
        taskTitle: task.title,
        subject: task.subjectName,
        priority: task.priority || 'medium',
        status: task.columnId || 'todo',
        estimatedTime,
        actualTimeSpent: Math.round(actualTimeSpent),
        timeEfficiency: Math.round(timeEfficiency),
        completionVelocity: daysToComplete ? 1 / daysToComplete : 0,
        daysToComplete,
        isCompleted,
        createdAt,
        completedAt,
        dueDate,
        isOverdue
      };
    });
  }

  private analyzeProductivityPatterns(sessions: StudySessionRow[]): ProductivityPattern[] {
    const patterns: { [key: string]: ProductivityPattern } = {};
    
    sessions.forEach(session => {
      if (!session.productivity_rating) return;
      
      const startTime = new Date(session.start_time);
      const timeOfDay = format(startTime, 'HH:00');
      const dayOfWeek = format(startTime, 'EEEE');
      const key = `${dayOfWeek}-${timeOfDay}`;
      
      if (!patterns[key]) {
        patterns[key] = {
          timeOfDay,
          dayOfWeek,
          averageProductivity: 0,
          totalSessions: 0,
          totalTime: 0,
          taskTypes: [],
          subjects: []
        };
      }
      
      const pattern = patterns[key];
      pattern.averageProductivity = (pattern.averageProductivity * pattern.totalSessions + session.productivity_rating) / (pattern.totalSessions + 1);
      pattern.totalSessions += 1;
      pattern.totalTime += session.duration || 0;
      
      if (session.task_type && !pattern.taskTypes.includes(session.task_type)) {
        pattern.taskTypes.push(session.task_type);
      }
      if (session.subject && !pattern.subjects.includes(session.subject)) {
        pattern.subjects.push(session.subject);
      }
    });
    
    return Object.values(patterns).sort((a, b) => b.averageProductivity - a.averageProductivity);
  }

  private calculateTimeEstimationAccuracy(tasks: EnhancedTodoItem[], sessions: StudySessionRow[]): TimeEstimationAccuracy[] {
    return tasks
      .filter(task => task.timeEstimate && task.timeEstimate > 0)
      .map(task => {
        const taskSessions = sessions.filter(session => 
          session.task_name === task.title || session.task_id === task.id
        );
        
        const actualTime = taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60; // minutes
        const estimatedTime = task.timeEstimate || 0;
        
        const accuracyPercentage = actualTime > 0 ? Math.min((estimatedTime / actualTime) * 100, 200) : 0;
        const estimationError = estimatedTime - actualTime;
        
        return {
          taskId: task.id,
          taskTitle: task.title,
          estimatedTime,
          actualTime: Math.round(actualTime),
          accuracyPercentage: Math.round(accuracyPercentage),
          estimationError: Math.round(estimationError),
          taskType: this.getTaskTypeFromTask(task),
          subject: task.subjectName,
          difficulty: this.getDifficultyFromTask(task)
        };
      })
      .filter(accuracy => accuracy.actualTime > 0);
  }

  private compareTaskTypes(tasks: EnhancedTodoItem[], sessions: StudySessionRow[]): TaskTypeComparison[] {
    const taskTypeMap: { [key: string]: {
      tasks: EnhancedTodoItem[];
      sessions: StudySessionRow[];
    } } = {};
    
    // Group tasks by type
    tasks.forEach(task => {
      const taskType = this.getTaskTypeFromTask(task);
      if (!taskTypeMap[taskType]) {
        taskTypeMap[taskType] = { tasks: [], sessions: [] };
      }
      taskTypeMap[taskType].tasks.push(task);
    });
    
    // Group sessions by task type
    sessions.forEach(session => {
      const taskType = session.task_type || 'Unknown';
      if (!taskTypeMap[taskType]) {
        taskTypeMap[taskType] = { tasks: [], sessions: [] };
      }
      taskTypeMap[taskType].sessions.push(session);
    });
    
    return Object.entries(taskTypeMap).map(([taskType, data]) => {
      const totalTasks = data.tasks.length;
      const completedTasks = data.tasks.filter(task => task.columnId === 'done' || task.completed).length;
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
      
      const totalTime = data.sessions.reduce((sum, session) => sum + (session.duration || 0), 0) / 60; // minutes
      const averageTimeSpent = data.sessions.length > 0 ? totalTime / data.sessions.length : 0;
      
      const productivityRatings = data.sessions.filter(s => s.productivity_rating).map(s => s.productivity_rating!);
      const averageProductivity = productivityRatings.length > 0 
        ? productivityRatings.reduce((sum, rating) => sum + rating, 0) / productivityRatings.length 
        : 0;
      
      // Calculate estimation accuracy for this task type
      const tasksWithEstimates = data.tasks.filter(task => task.timeEstimate && task.timeEstimate > 0);
      const accuracies = tasksWithEstimates.map(task => {
        const taskSessions = data.sessions.filter(session => 
          session.task_name === task.title || session.task_id === task.id
        );
        const actualTime = taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60;
        return actualTime > 0 ? Math.min((task.timeEstimate! / actualTime) * 100, 200) : 0;
      }).filter(acc => acc > 0);
      
      const averageEstimationAccuracy = accuracies.length > 0 
        ? accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length 
        : 0;
      
      // Find most productive time of day
      const timePatterns: { [key: string]: { count: number; totalProductivity: number } } = {};
      data.sessions.forEach(session => {
        if (session.productivity_rating) {
          const hour = format(new Date(session.start_time), 'HH:00');
          if (!timePatterns[hour]) timePatterns[hour] = { count: 0, totalProductivity: 0 };
          timePatterns[hour].count += 1;
          timePatterns[hour].totalProductivity += session.productivity_rating;
        }
      });
      
      const mostProductiveTimeOfDay = Object.entries(timePatterns)
        .map(([time, data]) => ({ time, avgProductivity: data.totalProductivity / data.count }))
        .sort((a, b) => b.avgProductivity - a.avgProductivity)[0]?.time || 'N/A';
      
      const averageSessionLength = data.sessions.length > 0 ? totalTime / data.sessions.length : 0;
      
      return {
        taskType,
        totalTasks,
        completedTasks,
        completionRate: Math.round(completionRate),
        averageTimeSpent: Math.round(averageTimeSpent),
        averageProductivity: Math.round(averageProductivity * 10) / 10,
        averageEstimationAccuracy: Math.round(averageEstimationAccuracy),
        mostProductiveTimeOfDay,
        averageSessionLength: Math.round(averageSessionLength)
      };
    }).sort((a, b) => b.averageProductivity - a.averageProductivity);
  }

  private compareSubjects(tasks: EnhancedTodoItem[], sessions: StudySessionRow[]): SubjectComparison[] {
    const subjectMap: { [key: string]: {
      tasks: EnhancedTodoItem[];
      sessions: StudySessionRow[];
    } } = {};
    
    // Group tasks by subject
    tasks.forEach(task => {
      const subject = task.subjectName || 'Unknown';
      if (!subjectMap[subject]) {
        subjectMap[subject] = { tasks: [], sessions: [] };
      }
      subjectMap[subject].tasks.push(task);
    });
    
    // Group sessions by subject
    sessions.forEach(session => {
      const subject = session.subject || 'Unknown';
      if (!subjectMap[subject]) {
        subjectMap[subject] = { tasks: [], sessions: [] };
      }
      subjectMap[subject].sessions.push(session);
    });
    
    return Object.entries(subjectMap).map(([subject, data]) => {
      const totalTasks = data.tasks.length;
      const completedTasks = data.tasks.filter(task => task.columnId === 'done' || task.completed).length;
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
      
      const totalTimeSpent = data.sessions.reduce((sum, session) => sum + (session.duration || 0), 0) / 60; // minutes
      
      const productivityRatings = data.sessions.filter(s => s.productivity_rating).map(s => s.productivity_rating!);
      const averageProductivity = productivityRatings.length > 0 
        ? productivityRatings.reduce((sum, rating) => sum + rating, 0) / productivityRatings.length 
        : 0;
      
      // Calculate estimation accuracy
      const tasksWithEstimates = data.tasks.filter(task => task.timeEstimate && task.timeEstimate > 0);
      const accuracies = tasksWithEstimates.map(task => {
        const taskSessions = data.sessions.filter(session => 
          session.task_name === task.title || session.task_id === task.id
        );
        const actualTime = taskSessions.reduce((total, session) => total + (session.duration || 0), 0) / 60;
        return actualTime > 0 ? Math.min((task.timeEstimate! / actualTime) * 100, 200) : 0;
      }).filter(acc => acc > 0);
      
      const averageEstimationAccuracy = accuracies.length > 0 
        ? accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length 
        : 0;
      
      // Get unique task types
      const taskTypes = [...new Set(data.tasks.map(task => this.getTaskTypeFromTask(task)))];
      
      // Find most productive day
      const dayPatterns: { [key: string]: { count: number; totalProductivity: number } } = {};
      data.sessions.forEach(session => {
        if (session.productivity_rating) {
          const day = format(new Date(session.start_time), 'EEEE');
          if (!dayPatterns[day]) dayPatterns[day] = { count: 0, totalProductivity: 0 };
          dayPatterns[day].count += 1;
          dayPatterns[day].totalProductivity += session.productivity_rating;
        }
      });
      
      const mostProductiveDay = Object.entries(dayPatterns)
        .map(([day, data]) => ({ day, avgProductivity: data.totalProductivity / data.count }))
        .sort((a, b) => b.avgProductivity - a.avgProductivity)[0]?.day || 'N/A';
      
      // Calculate trend (simplified - based on recent vs older productivity)
      const recentSessions = data.sessions.filter(session => 
        new Date(session.start_time) >= subWeeks(new Date(), 2)
      );
      const olderSessions = data.sessions.filter(session => 
        new Date(session.start_time) < subWeeks(new Date(), 2)
      );
      
      const recentAvgProductivity = recentSessions.length > 0 
        ? recentSessions.filter(s => s.productivity_rating).reduce((sum, s) => sum + s.productivity_rating!, 0) / recentSessions.filter(s => s.productivity_rating).length
        : 0;
      const olderAvgProductivity = olderSessions.length > 0 
        ? olderSessions.filter(s => s.productivity_rating).reduce((sum, s) => sum + s.productivity_rating!, 0) / olderSessions.filter(s => s.productivity_rating).length
        : 0;
      
      let trendDirection: 'improving' | 'declining' | 'stable' = 'stable';
      if (recentAvgProductivity > olderAvgProductivity + 0.3) {
        trendDirection = 'improving';
      } else if (recentAvgProductivity < olderAvgProductivity - 0.3) {
        trendDirection = 'declining';
      }
      
      return {
        subject,
        totalTasks,
        completedTasks,
        completionRate: Math.round(completionRate),
        totalTimeSpent: Math.round(totalTimeSpent),
        averageProductivity: Math.round(averageProductivity * 10) / 10,
        averageEstimationAccuracy: Math.round(averageEstimationAccuracy),
        taskTypes,
        mostProductiveDay,
        trendDirection
      };
    }).sort((a, b) => b.averageProductivity - a.averageProductivity);
  }

  private calculateTrends(tasks: EnhancedTodoItem[], sessions: StudySessionRow[], timeRange: 'week' | 'month' | 'quarter' | 'all') {
    const now = new Date();
    const periods = timeRange === 'week' ? 7 : timeRange === 'month' ? 30 : 90;
    const interval = timeRange === 'week' ? 1 : timeRange === 'month' ? 7 : 30; // days per data point
    
    const completionVelocity: { date: string; velocity: number }[] = [];
    const productivityTrend: { date: string; productivity: number }[] = [];
    const timeSpentTrend: { date: string; timeSpent: number }[] = [];
    
    for (let i = 0; i < periods; i += interval) {
      const periodStart = subDays(now, i + interval);
      const periodEnd = subDays(now, i);
      
      // Completion velocity
      const completedInPeriod = tasks.filter(task => {
        const completedDate = new Date(task.updatedAt || task.createdAt);
        return (task.columnId === 'done' || task.completed) && 
               completedDate >= periodStart && completedDate <= periodEnd;
      });
      const velocity = completedInPeriod.length / interval;
      
      // Productivity trend
      const sessionsInPeriod = sessions.filter(session => {
        const sessionDate = new Date(session.start_time);
        return sessionDate >= periodStart && sessionDate <= periodEnd;
      });
      const productivityRatings = sessionsInPeriod.filter(s => s.productivity_rating).map(s => s.productivity_rating!);
      const avgProductivity = productivityRatings.length > 0 
        ? productivityRatings.reduce((sum, rating) => sum + rating, 0) / productivityRatings.length 
        : 0;
      
      // Time spent trend
      const totalTimeInPeriod = sessionsInPeriod.reduce((sum, session) => sum + (session.duration || 0), 0) / 60; // minutes
      
      completionVelocity.unshift({
        date: format(periodStart, 'yyyy-MM-dd'),
        velocity: Math.round(velocity * 100) / 100
      });
      
      productivityTrend.unshift({
        date: format(periodStart, 'yyyy-MM-dd'),
        productivity: Math.round(avgProductivity * 10) / 10
      });
      
      timeSpentTrend.unshift({
        date: format(periodStart, 'yyyy-MM-dd'),
        timeSpent: Math.round(totalTimeInPeriod)
      });
    }
    
    return {
      completionVelocity,
      productivityTrend,
      timeSpentTrend
    };
  }

  private generateInsights(
    taskMetrics: TaskCompletionMetrics[],
    productivityPatterns: ProductivityPattern[],
    estimationAccuracy: TimeEstimationAccuracy[],
    taskTypeComparisons: TaskTypeComparison[],
    subjectComparisons: SubjectComparison[]
  ) {
    const bestPerformingSubject = subjectComparisons.length > 0 
      ? subjectComparisons[0].subject 
      : 'N/A';
    
    const mostAccurateEstimationTaskType = taskTypeComparisons
      .filter(tc => tc.averageEstimationAccuracy > 0)
      .sort((a, b) => Math.abs(100 - b.averageEstimationAccuracy) - Math.abs(100 - a.averageEstimationAccuracy))[0]?.taskType || 'N/A';
    
    const mostProductiveTimeOfDay = productivityPatterns.length > 0 
      ? productivityPatterns[0].timeOfDay 
      : 'N/A';
    
    const dayProductivity: { [key: string]: { total: number; count: number } } = {};
    productivityPatterns.forEach(pattern => {
      if (!dayProductivity[pattern.dayOfWeek]) {
        dayProductivity[pattern.dayOfWeek] = { total: 0, count: 0 };
      }
      dayProductivity[pattern.dayOfWeek].total += pattern.averageProductivity * pattern.totalSessions;
      dayProductivity[pattern.dayOfWeek].count += pattern.totalSessions;
    });
    
    const mostProductiveDayOfWeek = Object.entries(dayProductivity)
      .map(([day, data]) => ({ day, avgProductivity: data.total / data.count }))
      .sort((a, b) => b.avgProductivity - a.avgProductivity)[0]?.day || 'N/A';
    
    // Generate improvement suggestions
    const improvementSuggestions: string[] = [];
    
    // Time estimation suggestions
    const overestimators = estimationAccuracy.filter(ea => ea.estimationError > 30);
    const underestimators = estimationAccuracy.filter(ea => ea.estimationError < -30);
    
    if (overestimators.length > underestimators.length) {
      improvementSuggestions.push('You tend to overestimate task durations. Try breaking tasks into smaller, more predictable chunks.');
    } else if (underestimators.length > overestimators.length) {
      improvementSuggestions.push('You tend to underestimate task durations. Consider adding buffer time for unexpected challenges.');
    }
    
    // Productivity suggestions
    if (productivityPatterns.length > 0) {
      const lowProductivityPatterns = productivityPatterns.filter(p => p.averageProductivity < 3);
      if (lowProductivityPatterns.length > 0) {
        improvementSuggestions.push(`Consider avoiding work during ${lowProductivityPatterns[0].timeOfDay} on ${lowProductivityPatterns[0].dayOfWeek}s when your productivity is typically lower.`);
      }
    }
    
    // Task completion suggestions
    const incompleteTasks = taskMetrics.filter(tm => !tm.isCompleted);
    const overdueTasks = incompleteTasks.filter(tm => tm.isOverdue);
    
    if (overdueTasks.length > 0) {
      improvementSuggestions.push(`You have ${overdueTasks.length} overdue tasks. Consider prioritizing these or adjusting their deadlines.`);
    }
    
    // Subject-specific suggestions
    const decliningSubjects = subjectComparisons.filter(sc => sc.trendDirection === 'declining');
    if (decliningSubjects.length > 0) {
      improvementSuggestions.push(`Your productivity in ${decliningSubjects[0].subject} has been declining. Consider changing your approach or taking a break.`);
    }
    
    return {
      bestPerformingSubject,
      mostAccurateEstimationTaskType,
      mostProductiveTimeOfDay,
      mostProductiveDayOfWeek,
      improvementSuggestions
    };
  }

  private calculateOverviewMetrics(tasks: EnhancedTodoItem[], sessions: StudySessionRow[]) {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.columnId === 'done' || task.completed).length;
    const totalTimeSpent = sessions.reduce((sum, session) => sum + (session.duration || 0), 0) / 60; // minutes
    
    const productivityRatings = sessions.filter(s => s.productivity_rating).map(s => s.productivity_rating!);
    const averageProductivity = productivityRatings.length > 0 
      ? productivityRatings.reduce((sum, rating) => sum + rating, 0) / productivityRatings.length 
      : 0;
    
    // Calculate overall efficiency (tasks completed per hour)
    const overallEfficiency = totalTimeSpent > 0 ? (completedTasks / (totalTimeSpent / 60)) : 0;
    
    return {
      totalTasks,
      completedTasks,
      totalTimeSpent: Math.round(totalTimeSpent),
      averageProductivity: Math.round(averageProductivity * 10) / 10,
      overallEfficiency: Math.round(overallEfficiency * 100) / 100
    };
  }

  private getTaskTypeFromTask(task: EnhancedTodoItem): string {
    if (task.tags.includes('lecture')) return 'Lecture';
    if (task.tags.includes('exercise')) return 'Exercise';
    if (task.tags.includes('reading')) return 'Reading';
    if (task.tags.includes('practice')) return 'Practice';
    if (task.tags.includes('assignment')) return 'Assignment';
    if (task.tags.includes('project')) return 'Project';
    if (task.tags.includes('review')) return 'Review';
    return 'Study';
  }

  private getDifficultyFromTask(task: EnhancedTodoItem): number {
    if (task.tags.includes('easy')) return 1;
    if (task.tags.includes('medium')) return 2;
    if (task.tags.includes('hard')) return 3;
    if (task.priority === 'high') return 3;
    if (task.priority === 'medium') return 2;
    return 1;
  }
}

// Export singleton instance
export const taskAnalyticsService = TaskAnalyticsService.getInstance();