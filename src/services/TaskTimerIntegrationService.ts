import { EnhancedTodoItem } from '../types/todo';
import { taskStorage } from '../utils/taskLocalStorage';
import { 
  saveStudySessionWithTaskAssociation, 
  getStudySessionsByTaskId, 
  getTaskTimeAnalytics,
  getStudySessions,
  EnhancedStudySessionData,
  SessionFeedback as SupabaseSessionFeedback,
  TaskTimeAnalytics
} from '../utils/supabase';
import { studySessionManager, ActiveSession } from './StudySessionManager';

// Enhanced timer state with task context
export interface TimerTaskContext {
  taskId: string;
  taskTitle: string;
  taskDescription?: string;
  subjectId?: string;
  subjectName?: string;
  subjectColor?: string;
  priority: 'low' | 'medium' | 'high';
  estimatedTime?: number; // in minutes
  actualTimeSpent: number; // in minutes
  completionPercentage: number;
  dueDate?: number;
}

// Enhanced timer state interface
export interface EnhancedTimerState {
  status: 'idle' | 'running' | 'paused';
  mode: 'pomodoro' | 'stopwatch';
  displayTime: number; // in seconds
  
  // Task integration
  linkedTask?: TimerTaskContext;
  sessionId?: string;
  estimatedEndTime?: Date;
  
  // Analytics
  sessionStartTime: Date;
  pausedDuration: number; // in milliseconds
  breaksSuggested: number;
  productivityScore?: number;
}

// Timer session interface for active sessions
export interface TimerSession {
  id: string;
  taskId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  status: 'active' | 'paused' | 'completed';
  mode: 'pomodoro' | 'stopwatch';
  phase: 'work' | 'shortBreak' | 'longBreak';
}

// Session feedback interface
export interface SessionFeedback {
  productivityRating: number; // 1-5
  notes?: string;
  difficultyRating?: number; // 1-5
  focusRating?: number; // 1-5
  completedSubtasks?: string[];
  nextSteps?: string;
}

// Task time tracking data interface
export interface TaskTimeData {
  taskId: string;
  totalTimeSpent: number; // in seconds
  estimatedTime?: number; // in seconds
  sessions: any[]; // Study sessions from Supabase
  averageSessionLength: number;
  productivityRating: number; // 1-5 average
  lastWorkedOn?: Date;
  timeProgress: number; // percentage of estimate completed
  projectedCompletion?: Date;
}

// Data sync utilities interface
export interface DataSyncUtilities {
  syncTaskWithTimer: (taskId: string) => Promise<void>;
  handleTimerStateChange: (state: EnhancedTimerState) => Promise<void>;
  resolveConflicts: (localData: any, remoteData: any) => any;
  queueOfflineOperation: (operation: any) => void;
  processOfflineQueue: () => Promise<void>;
}

/**
 * Core service that coordinates between task management and timer functionality
 */
export class TaskTimerIntegrationService {
  private static instance: TaskTimerIntegrationService;
  private activeSession: TimerSession | null = null;
  private syncUtilities: DataSyncUtilities;

  private constructor() {
    this.syncUtilities = this.createSyncUtilities();
  }

  public static getInstance(): TaskTimerIntegrationService {
    if (!TaskTimerIntegrationService.instance) {
      TaskTimerIntegrationService.instance = new TaskTimerIntegrationService();
    }
    return TaskTimerIntegrationService.instance;
  }

  /**
   * Start a timer for a specific task using StudySessionManager
   */
  async startTimerForTask(taskId: string, userId: string): Promise<TimerSession> {
    try {
      // Get task details from localStorage
      const task = taskStorage.getTask(userId, taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Check if there's already an active timer
      const activeSession = studySessionManager.getActiveSession();
      if (activeSession && activeSession.status === 'active') {
        throw new Error('Another timer is already running. Please stop it first.');
      }

      // Start session using StudySessionManager
      const activeStudySession = await studySessionManager.startSession(
        taskId,
        task.title,
        task.subjectName,
        this.getTaskTypeFromTask(task),
        task.timeEstimate
      );

      // Convert to TimerSession format for compatibility
      const timerSession: TimerSession = {
        id: activeStudySession.id,
        taskId: taskId,
        userId: userId,
        startTime: activeStudySession.startTime,
        duration: 0,
        status: activeStudySession.status as 'active' | 'paused' | 'completed',
        mode: 'stopwatch',
        phase: 'work'
      };

      this.activeSession = timerSession;
      return timerSession;
    } catch (error) {
      console.error('Error starting timer for task:', error);
      throw error;
    }
  }

  /**
   * Pause timer for a specific task using StudySessionManager
   */
  async pauseTimerForTask(taskId: string): Promise<void> {
    try {
      const activeSession = studySessionManager.getActiveSession();
      if (!activeSession || activeSession.taskId !== taskId) {
        throw new Error('No active timer found for this task');
      }

      if (activeSession.status !== 'active') {
        throw new Error('Timer is not currently running');
      }

      // Pause using StudySessionManager
      const success = studySessionManager.pauseSession('User requested pause');
      if (!success) {
        throw new Error('Failed to pause timer');
      }

      // Update local session state
      if (this.activeSession) {
        this.activeSession.status = 'paused';
      }
    } catch (error) {
      console.error('Error pausing timer for task:', error);
      throw error;
    }
  }

  /**
   * Stop timer for a specific task and save study session using StudySessionManager
   */
  async stopTimerForTask(taskId: string, userId: string, feedback?: SessionFeedback): Promise<any> {
    try {
      const activeSession = studySessionManager.getActiveSession();
      if (!activeSession || activeSession.taskId !== taskId) {
        throw new Error('No active timer found for this task');
      }

      // End session using StudySessionManager with AI learning updates
      const savedSession = await studySessionManager.endSessionWithAILearning(userId, feedback);

      // Update task's actual time spent using StudySessionManager
      const duration = studySessionManager.getCurrentSessionDuration();
      await studySessionManager.updateTaskTime(taskId, duration);

      // Clear local session state
      this.activeSession = null;

      return savedSession;
    } catch (error) {
      console.error('Error stopping timer for task:', error);
      throw error;
    }
  }

  /**
   * Get time tracking data for a specific task using enhanced analytics
   */
  async getTaskTimeTracking(taskId: string, userId: string): Promise<TaskTimeData> {
    try {
      // Get task from localStorage
      const task = taskStorage.getTask(userId, taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Get analytics using StudySessionManager
      const analytics = await studySessionManager.getTaskSessionHistory(userId, taskId);
      
      if (!analytics) {
        // Return empty analytics if no sessions found
        return {
          taskId,
          totalTimeSpent: 0,
          estimatedTime: (task.timeEstimate || 0) * 60,
          sessions: [],
          averageSessionLength: 0,
          productivityRating: 0,
          lastWorkedOn: undefined,
          timeProgress: 0,
          projectedCompletion: undefined
        };
      }

      const estimatedTimeSeconds = (task.timeEstimate || 0) * 60;
      const timeProgress = estimatedTimeSeconds > 0 ? (analytics.totalTimeSpent / estimatedTimeSeconds) * 100 : 0;

      return {
        taskId,
        totalTimeSpent: analytics.totalTimeSpent,
        estimatedTime: estimatedTimeSeconds,
        sessions: analytics.sessions,
        averageSessionLength: analytics.averageSessionLength,
        productivityRating: analytics.averageProductivityRating,
        lastWorkedOn: analytics.lastWorkedOn,
        timeProgress: Math.min(timeProgress, 100),
        projectedCompletion: this.calculateProjectedCompletion(task, analytics.totalTimeSpent, estimatedTimeSeconds)
      };
    } catch (error) {
      console.error('Error getting task time tracking:', error);
      throw error;
    }
  }

  /**
   * Update time estimate for a task
   */
  async updateTaskTimeEstimate(taskId: string, userId: string, estimate: number): Promise<void> {
    try {
      const task = taskStorage.getTask(userId, taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      const updatedTask = {
        ...task,
        timeEstimate: estimate, // in minutes
        updatedAt: Date.now()
      };

      taskStorage.saveTask(userId, updatedTask);
    } catch (error) {
      console.error('Error updating task time estimate:', error);
      throw error;
    }
  }

  /**
   * Resume a paused timer for a specific task
   */
  async resumeTimerForTask(taskId: string): Promise<void> {
    try {
      const activeSession = studySessionManager.getActiveSession();
      if (!activeSession || activeSession.taskId !== taskId) {
        throw new Error('No timer found for this task');
      }

      if (activeSession.status !== 'paused') {
        throw new Error('Timer is not currently paused');
      }

      // Resume using StudySessionManager
      const success = studySessionManager.resumeSession();
      if (!success) {
        throw new Error('Failed to resume timer');
      }

      // Update local session state
      if (this.activeSession) {
        this.activeSession.status = 'active';
      }
    } catch (error) {
      console.error('Error resuming timer for task:', error);
      throw error;
    }
  }

  /**
   * Get current active session
   */
  getActiveSession(): TimerSession | null {
    // Sync with StudySessionManager
    const studySession = studySessionManager.getActiveSession();
    if (studySession && this.activeSession) {
      this.activeSession.status = studySession.status as 'active' | 'paused' | 'completed';
    }
    return this.activeSession;
  }

  /**
   * Check if a task has an active timer
   */
  isTaskTimerActive(taskId: string): boolean {
    return studySessionManager.isTaskTimerActive(taskId);
  }

  /**
   * Get current session duration for a task
   */
  getCurrentSessionDuration(taskId: string): number {
    const activeSession = studySessionManager.getActiveSession();
    if (activeSession?.taskId === taskId) {
      return studySessionManager.getCurrentSessionDuration();
    }
    return 0;
  }

  // Private helper methods

  private calculateSessionDuration(): number {
    if (!this.activeSession) return 0;
    const now = new Date();
    return Math.floor((now.getTime() - this.activeSession.startTime.getTime()) / 1000);
  }

  private async updateTaskStatus(userId: string, taskId: string, newColumnId: string): Promise<void> {
    const task = taskStorage.getTask(userId, taskId);
    if (task) {
      const updatedTask = {
        ...task,
        columnId: newColumnId,
        updatedAt: Date.now()
      };
      taskStorage.saveTask(userId, updatedTask);
    }
  }

  private async updateTaskTimeSpent(userId: string, taskId: string, newTimeSpent: number): Promise<void> {
    const task = taskStorage.getTask(userId, taskId);
    if (task) {
      const updatedTask = {
        ...task,
        actualTimeSpent: newTimeSpent,
        updatedAt: Date.now()
      };
      taskStorage.saveTask(userId, updatedTask);
    }
  }

  private getTaskTypeFromTask(task: EnhancedTodoItem): string {
    // Determine task type based on task properties
    if (task.tags.includes('lecture')) return 'Lecture';
    if (task.tags.includes('exercise')) return 'Exercise';
    if (task.tags.includes('reading')) return 'Reading';
    if (task.tags.includes('practice')) return 'Practice';
    return 'Study';
  }

  private formatDateForSession(date: Date): string {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  private calculateProjectedCompletion(task: EnhancedTodoItem, timeSpent: number, estimatedTime: number): Date | undefined {
    if (!estimatedTime || !task.dueDate) return undefined;
    
    const remainingTime = estimatedTime - timeSpent;
    if (remainingTime <= 0) return new Date(); // Already complete
    
    // Simple projection: assume same pace as historical average
    const now = new Date();
    const dueDate = new Date(task.dueDate);
    const timeUntilDue = dueDate.getTime() - now.getTime();
    
    if (timeUntilDue <= 0) return dueDate; // Overdue
    
    // Project completion based on remaining time and current pace
    const projectedTime = now.getTime() + (remainingTime * 1000);
    return new Date(projectedTime);
  }

  private saveSessionToStorage(session: TimerSession): void {
    try {
      localStorage.setItem('activeTimerSession', JSON.stringify(session));
    } catch (error) {
      console.error('Error saving session to storage:', error);
    }
  }

  private clearSessionFromStorage(): void {
    try {
      localStorage.removeItem('activeTimerSession');
    } catch (error) {
      console.error('Error clearing session from storage:', error);
    }
  }

  private createSyncUtilities(): DataSyncUtilities {
    return {
      syncTaskWithTimer: async (taskId: string) => {
        // Implementation for syncing task data with timer state
        console.log('Syncing task with timer:', taskId);
      },

      handleTimerStateChange: async (state: EnhancedTimerState) => {
        // Implementation for handling timer state changes
        console.log('Handling timer state change:', state);
      },

      resolveConflicts: (localData: any, remoteData: any) => {
        // Simple last-write-wins strategy for now
        return remoteData.updatedAt > localData.updatedAt ? remoteData : localData;
      },

      queueOfflineOperation: (operation: any) => {
        // Queue operations for offline processing
        const queue = JSON.parse(localStorage.getItem('offlineOperationQueue') || '[]');
        queue.push({ ...operation, timestamp: Date.now() });
        localStorage.setItem('offlineOperationQueue', JSON.stringify(queue));
      },

      processOfflineQueue: async () => {
        // Process queued offline operations
        const queue = JSON.parse(localStorage.getItem('offlineOperationQueue') || '[]');
        for (const operation of queue) {
          try {
            // Process each operation
            console.log('Processing offline operation:', operation);
          } catch (error) {
            console.error('Error processing offline operation:', error);
          }
        }
        localStorage.removeItem('offlineOperationQueue');
      }
    };
  }
}

// Export singleton instance
export const taskTimerIntegration = TaskTimerIntegrationService.getInstance();