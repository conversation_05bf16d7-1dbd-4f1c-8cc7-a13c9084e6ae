import { useState, useEffect, useCallback } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { 
  smartNotificationService, 
  SmartNotification, 
  NotificationSettings,
  ProductivityPattern 
} from '@/services/SmartNotificationService';

// Hook for managing smart notifications
export const useSmartNotifications = () => {
  const { user } = useSupabaseAuth();
  const [notifications, setNotifications] = useState<SmartNotification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load notifications and settings
  const loadData = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      
      // Initialize the service for this user
      smartNotificationService.initialize(user.id);
      
      // Load current notifications and settings
      const userNotifications = smartNotificationService.getNotifications(user.id);
      const userSettings = smartNotificationService.getSettings(user.id);
      
      setNotifications(userNotifications);
      setSettings(userSettings);
    } catch (error) {
      console.error('Error loading notification data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Run the notification analysis
  const runAnalysis = useCallback(() => {
    if (!user?.id) return;
    
    smartNotificationService.runAnalysis(user.id);
    
    // Refresh notifications after analysis
    const updatedNotifications = smartNotificationService.getNotifications(user.id);
    setNotifications(updatedNotifications);
  }, [user?.id]);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    smartNotificationService.markNotificationRead(notificationId);
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
    );
  }, []);

  // Dismiss notification
  const dismiss = useCallback((notificationId: string) => {
    smartNotificationService.dismissNotification(notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  }, []);

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    if (!user?.id) return;
    
    smartNotificationService.updateSettings(user.id, newSettings);
    const updatedSettings = smartNotificationService.getSettings(user.id);
    setSettings(updatedSettings);
  }, [user?.id]);

  // Schedule deadline reminders
  const scheduleDeadlineReminders = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.scheduleDeadlineReminders(user.id);
    runAnalysis();
  }, [user?.id, runAnalysis]);

  // Schedule break suggestions
  const scheduleBreakSuggestions = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.scheduleBreakSuggestions(user.id);
    runAnalysis();
  }, [user?.id, runAnalysis]);

  // Detect procrastination and send nudges
  const detectProcrastination = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.detectProcrastinationAndNudge(user.id);
    runAnalysis();
  }, [user?.id, runAnalysis]);

  // Track productivity streak
  const trackProductivityStreak = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.trackProductivityStreak(user.id);
    runAnalysis();
  }, [user?.id, runAnalysis]);

  // Schedule context-aware reminders
  const scheduleContextReminders = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.scheduleContextAwareReminders(user.id);
    runAnalysis();
  }, [user?.id, runAnalysis]);

  // Initialize on mount and when user changes
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Run periodic analysis (every 30 minutes)
  useEffect(() => {
    if (!user?.id) return;

    const interval = setInterval(() => {
      runAnalysis();
    }, 30 * 60 * 1000); // 30 minutes

    return () => clearInterval(interval);
  }, [user?.id, runAnalysis]);

  // Computed values
  const unreadCount = notifications.filter(n => !n.isRead).length;
  const urgentCount = notifications.filter(n => n.priority === 'urgent' && !n.isRead).length;
  const hasUrgentNotifications = urgentCount > 0;

  return {
    // Data
    notifications,
    settings,
    isLoading,
    unreadCount,
    urgentCount,
    hasUrgentNotifications,

    // Actions
    runAnalysis,
    markAsRead,
    dismiss,
    updateSettings,
    scheduleDeadlineReminders,
    scheduleBreakSuggestions,
    detectProcrastination,
    trackProductivityStreak,
    scheduleContextReminders,
    refresh: loadData,
  };
};

// Hook for notification settings management
export const useNotificationSettings = () => {
  const { user } = useSupabaseAuth();
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const loadSettings = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const userSettings = smartNotificationService.getSettings(user.id);
      setSettings(userSettings);
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    if (!user?.id || !settings) return;

    const updatedSettings = { ...settings, ...newSettings };
    smartNotificationService.updateSettings(user.id, updatedSettings);
    setSettings(updatedSettings);
  }, [user?.id, settings]);

  const resetSettings = useCallback(() => {
    if (!user?.id) return;

    smartNotificationService.updateSettings(user.id, {
      enabled: true,
      deadlineReminders: true,
      breakSuggestions: true,
      procrastinationNudges: true,
      productivityStreaks: true,
      contextReminders: true,
      quietHours: { start: 22, end: 7 },
      maxNotificationsPerHour: 3,
      reminderAdvanceTime: 24,
    });
    
    loadSettings();
  }, [user?.id, loadSettings]);

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    isLoading,
    updateSettings,
    resetSettings,
    refresh: loadSettings,
  };
};

// Hook for productivity patterns
export const useProductivityPatterns = () => {
  const { user } = useSupabaseAuth();
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzePatterns = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsAnalyzing(true);
      
      // Initialize and run analysis
      smartNotificationService.initialize(user.id);
      smartNotificationService.runAnalysis(user.id);
      
    } catch (error) {
      console.error('Error analyzing productivity patterns:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [user?.id]);

  return {
    isAnalyzing,
    analyzePatterns,
  };
};

// Hook for specific notification types
export const useNotificationTriggers = () => {
  const { user } = useSupabaseAuth();

  const triggerDeadlineReminder = useCallback((taskId?: string) => {
    if (!user?.id) return;
    smartNotificationService.scheduleDeadlineReminders(user.id);
  }, [user?.id]);

  const triggerBreakSuggestion = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.scheduleBreakSuggestions(user.id);
  }, [user?.id]);

  const triggerProcrastinationNudge = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.detectProcrastinationAndNudge(user.id);
  }, [user?.id]);

  const triggerStreakCelebration = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.trackProductivityStreak(user.id);
  }, [user?.id]);

  const triggerContextReminder = useCallback(() => {
    if (!user?.id) return;
    smartNotificationService.scheduleContextAwareReminders(user.id);
  }, [user?.id]);

  return {
    triggerDeadlineReminder,
    triggerBreakSuggestion,
    triggerProcrastinationNudge,
    triggerStreakCelebration,
    triggerContextReminder,
  };
};