import { useState, useEffect, useCallback } from 'react';
import { 
  aiTaskRecommendationEngine, 
  TaskRecommendation, 
  TimeSlot, 
  ProductivityPattern,
  LearningData
} from '../services/AITaskRecommendationEngine';
import { EnhancedTodoItem } from '../types/todo';
import { useEnhancedTodoStore } from '../stores/enhancedTodoStore';

interface UseAIRecommendationsOptions {
  userId: string;
  availableTime?: number; // in minutes
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

interface UseAIRecommendationsReturn {
  // Recommendations
  recommendations: TaskRecommendation[];
  timeSlots: TimeSlot[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  refreshRecommendations: () => Promise<void>;
  updateLearningData: (
    taskId: string,
    actualDuration: number,
    estimatedDuration: number,
    productivityRating: number,
    completed: boolean
  ) => Promise<void>;
  
  // Patterns and insights
  productivityPattern: ProductivityPattern | null;
  learningInsights: string[];
  
  // Utilities
  getTaskRecommendation: (taskId: string) => TaskRecommendation | null;
  getOptimalTimeForTask: (task: EnhancedTodoItem) => TimeSlot | null;
  shouldTakeBreak: (sessionDuration: number, currentTask?: EnhancedTodoItem) => boolean;
}

export const useAIRecommendations = (
  options: UseAIRecommendationsOptions
): UseAIRecommendationsReturn => {
  const { userId, availableTime = 60, autoRefresh = true, refreshInterval = 300000 } = options; // 5 minutes default
  
  const [recommendations, setRecommendations] = useState<TaskRecommendation[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [productivityPattern, setProductivityPattern] = useState<ProductivityPattern | null>(null);
  const [learningInsights, setLearningInsights] = useState<string[]>([]);
  
  const { board } = useEnhancedTodoStore();

  // Load recommendations
  const refreshRecommendations = useCallback(async () => {
    if (!userId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Get task recommendations
      const taskRecommendations = await aiTaskRecommendationEngine.getTaskRecommendations(
        userId,
        new Date(),
        availableTime,
        'productivity'
      );
      setRecommendations(taskRecommendations);

      // Get optimal time blocks
      const optimalTimeBlocks = await aiTaskRecommendationEngine.getOptimalTimeBlocks(
        userId,
        new Date(),
        { start: 9, end: 17 }
      );
      setTimeSlots(optimalTimeBlocks);

      // Get productivity patterns
      const patterns = await aiTaskRecommendationEngine.analyzeProductivityPatterns(userId);
      setProductivityPattern(patterns);

      // Generate learning insights
      const insights = generateLearningInsights(patterns, taskRecommendations);
      setLearningInsights(insights);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load recommendations';
      setError(errorMessage);
      console.error('Error loading AI recommendations:', err);
    } finally {
      setIsLoading(false);
    }
  }, [userId, availableTime]);

  // Update learning data when tasks are completed
  const updateLearningData = useCallback(async (
    taskId: string,
    actualDuration: number,
    estimatedDuration: number,
    productivityRating: number,
    completed: boolean
  ) => {
    try {
      await aiTaskRecommendationEngine.updateLearningData(
        userId,
        taskId,
        actualDuration,
        estimatedDuration,
        productivityRating,
        completed
      );
      
      // Refresh recommendations after learning update
      await refreshRecommendations();
    } catch (err) {
      console.error('Error updating learning data:', err);
    }
  }, [userId, refreshRecommendations]);

  // Get specific task recommendation
  const getTaskRecommendation = useCallback((taskId: string): TaskRecommendation | null => {
    return recommendations.find(rec => rec.taskId === taskId) || null;
  }, [recommendations]);

  // Get optimal time for a specific task
  const getOptimalTimeForTask = useCallback((task: EnhancedTodoItem): TimeSlot | null => {
    const recommendation = getTaskRecommendation(task.id);
    return recommendation?.optimalTimeSlot || null;
  }, [getTaskRecommendation]);

  // Determine if user should take a break
  const shouldTakeBreak = useCallback((
    sessionDuration: number, 
    currentTask?: EnhancedTodoItem
  ): boolean => {
    if (!currentTask || !productivityPattern) return false;

    // Basic break logic
    if (sessionDuration >= 90) return true; // Long session
    if (sessionDuration >= 25 && currentTask.difficultyLevel === 'hard') return true; // Hard task
    
    // Pattern-based break logic
    const optimalSessionLength = productivityPattern.optimalSessionLength;
    if (sessionDuration >= optimalSessionLength * 1.2) return true;

    return false;
  }, [productivityPattern]);

  // Initial load
  useEffect(() => {
    refreshRecommendations();
  }, [refreshRecommendations]);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshRecommendations();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshRecommendations]);

  // Refresh when tasks change
  useEffect(() => {
    const taskCount = Object.keys(board.tasks).length;
    if (taskCount > 0) {
      // Debounce the refresh to avoid too many calls
      const timeoutId = setTimeout(() => {
        refreshRecommendations();
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [board.tasks, refreshRecommendations]);

  return {
    recommendations,
    timeSlots,
    isLoading,
    error,
    refreshRecommendations,
    updateLearningData,
    productivityPattern,
    learningInsights,
    getTaskRecommendation,
    getOptimalTimeForTask,
    shouldTakeBreak
  };
};

// Helper function to generate learning insights
function generateLearningInsights(
  pattern: ProductivityPattern,
  recommendations: TaskRecommendation[]
): string[] {
  const insights: string[] = [];

  // Analyze productivity patterns
  const hourlyProductivity = Object.entries(pattern.hourlyProductivity);
  if (hourlyProductivity.length > 0) {
    const bestHour = hourlyProductivity.reduce((best, current) => 
      current[1] > best[1] ? current : best
    );
    
    const timeType = getTimeType(parseInt(bestHour[0]));
    insights.push(`You're most productive in the ${timeType} (${formatHour(parseInt(bestHour[0]))})`);
  }

  // Analyze subject preferences
  const subjectPrefs = Object.entries(pattern.subjectPreferences);
  if (subjectPrefs.length > 0) {
    const bestSubject = subjectPrefs.reduce((best, current) => 
      current[1] > best[1] ? current : best
    );
    
    if (bestSubject[1] > 0.7) {
      insights.push(`You work particularly well on ${bestSubject[0]} tasks`);
    }
  }

  // Analyze session length
  if (pattern.optimalSessionLength > 0) {
    if (pattern.optimalSessionLength < 30) {
      insights.push('You prefer shorter, focused sessions - consider the Pomodoro technique');
    } else if (pattern.optimalSessionLength > 90) {
      insights.push('You can maintain focus for extended periods - leverage this for complex tasks');
    }
  }

  // Analyze current recommendations
  const highUrgencyTasks = recommendations.filter(r => r.urgency === 'critical' || r.urgency === 'high');
  if (highUrgencyTasks.length > 0) {
    insights.push(`You have ${highUrgencyTasks.length} urgent task${highUrgencyTasks.length > 1 ? 's' : ''} - consider prioritizing these`);
  }

  // Weekly pattern insights
  const weeklyEntries = Object.entries(pattern.weeklyPatterns);
  if (weeklyEntries.length > 0) {
    const bestDay = weeklyEntries.reduce((best, current) => 
      current[1] > best[1] ? current : best
    );
    
    if (bestDay[1] > 0.7) {
      insights.push(`${bestDay[0]}s are your most productive days`);
    }
  }

  return insights.slice(0, 3); // Return top 3 insights
}

function getTimeType(hour: number): string {
  if (hour >= 6 && hour < 12) return 'morning';
  if (hour >= 12 && hour < 17) return 'afternoon';
  if (hour >= 17 && hour < 22) return 'evening';
  return 'night';
}

function formatHour(hour: number): string {
  const period = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
  return `${displayHour}:00 ${period}`;
}

export default useAIRecommendations;