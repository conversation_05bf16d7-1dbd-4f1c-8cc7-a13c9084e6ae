import { useEffect, useCallback, useRef } from 'react';
import { useEnhancedTimerStore } from '@/stores/enhancedTimerStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { dataSyncUtilities } from '@/utils/dataSyncUtilities';
import { supabase } from '@/integrations/supabase/client';

interface TimerSyncState {
  sessionId?: string;
  taskId?: string;
  userId: string;
  status: 'idle' | 'running' | 'paused';
  startTime?: number;
  pausedDuration?: number;
  lastUpdate: number;
}

/**
 * Hook for managing real-time timer synchronization across devices and pages
 */
export function useTimerSync() {
  const { user } = useSupabaseAuth();
  const syncChannelRef = useRef<any>(null);
  const lastSyncRef = useRef<number>(0);
  const syncIntervalRef = useRef<NodeJS.Timeout>();

  // Broadcast timer state to other devices/tabs
  const broadcastTimerState = useCallback(async (state: Partial<TimerSyncState>) => {
    if (!user?.id || !syncChannelRef.current) return;

    try {
      // Get current timer state without subscribing to store changes
      const currentState = useEnhancedTimerStore.getState();

      const syncState: TimerSyncState = {
        userId: user.id,
        status: currentState.status,
        sessionId: currentState.sessionId,
        taskId: currentState.linkedTask?.taskId,
        startTime: currentState.sessionStartTime.getTime(),
        pausedDuration: currentState.pausedDuration,
        lastUpdate: Date.now(),
        ...state,
      };

      await syncChannelRef.current.send({
        type: 'timer_state_update',
        payload: syncState,
      });

      lastSyncRef.current = Date.now();
      console.log('Timer state broadcasted:', syncState);
    } catch (error) {
      console.error('Error broadcasting timer state:', error);
    }
  }, [user?.id]); // Only depend on user.id, not the entire timer store

  // Handle incoming timer state updates
  const handleTimerStateUpdate = useCallback(async (payload: TimerSyncState) => {
    if (!user?.id || payload.userId !== user.id) return;

    // Prevent processing our own broadcasts
    if (payload.lastUpdate <= lastSyncRef.current) return;

    try {
      console.log('Received timer state update:', payload);

      // Get current timer state without subscribing to store changes
      const currentState = useEnhancedTimerStore.getState();

      // Update local timer state if the remote state is newer
      const localLastUpdate = currentState.sessionStartTime.getTime();
      if (payload.lastUpdate > localLastUpdate) {

        // If task changed, link new task
        if (payload.taskId && payload.taskId !== currentState.linkedTask?.taskId) {
          await currentState.linkTaskToTimer(payload.taskId, user.id);
        }

        // Update timer status and timing
        if (payload.status !== currentState.status) {
          switch (payload.status) {
            case 'running':
              if (currentState.status !== 'running') {
                await currentState.startTimer(payload.taskId, user.id);
              }
              break;
            case 'paused':
              if (currentState.status === 'running') {
                await currentState.pauseTimer();
              }
              break;
            case 'idle':
              if (currentState.status !== 'idle') {
                await currentState.stopTimer({ userId: user.id });
              }
              break;
          }
        }

        // Sync timing data
        if (payload.startTime && payload.pausedDuration !== undefined) {
          // Update internal timing state (this would require extending the store)
          console.log('Syncing timing data:', {
            startTime: payload.startTime,
            pausedDuration: payload.pausedDuration,
          });
        }
      }
    } catch (error) {
      console.error('Error handling timer state update:', error);
    }
  }, [user?.id]); // Only depend on user.id, not the entire timer store

  // Set up real-time subscription
  useEffect(() => {
    if (!user?.id) return;

    const channelName = `timer_sync_${user.id}`;

    // Prevent multiple subscriptions
    if (syncChannelRef.current) {
      syncChannelRef.current.unsubscribe();
    }

    // Create Supabase realtime channel
    syncChannelRef.current = supabase.channel(channelName)
      .on('broadcast', { event: 'timer_state_update' }, ({ payload }) => {
        handleTimerStateUpdate(payload);
      })
      .subscribe((status) => {
        console.log('Timer sync channel status:', status);
      });

    // Set up periodic sync for active timers
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
    }

    syncIntervalRef.current = setInterval(() => {
      const currentState = useEnhancedTimerStore.getState();
      if (currentState.status === 'running' && currentState.linkedTask) {
        broadcastTimerState({});
      }
    }, 10000); // Sync every 10 seconds when timer is running

    return () => {
      if (syncChannelRef.current) {
        syncChannelRef.current.unsubscribe();
        syncChannelRef.current = null;
      }
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = undefined;
      }
    };
  }, [user?.id, handleTimerStateUpdate]); // Removed broadcastTimerState dependency

  // Subscribe to timer store changes and broadcast when needed
  useEffect(() => {
    if (!user?.id) return;

    const unsubscribe = useEnhancedTimerStore.subscribe(
      (state) => ({ status: state.status, linkedTask: state.linkedTask }),
      (current, previous) => {
        // Only broadcast if status changed or task changed
        if (current.status !== previous.status ||
            current.linkedTask?.taskId !== previous.linkedTask?.taskId) {

          if (current.status !== 'idle' && current.linkedTask) {
            broadcastTimerState({});
          }
        }
      },
      { equalityFn: (a, b) => a.status === b.status && a.linkedTask?.taskId === b.linkedTask?.taskId }
    );

    return unsubscribe;
  }, [user?.id, broadcastTimerState]);

  // Sync with task data when timer state changes
  useEffect(() => {
    if (!user?.id) return;

    const unsubscribe = useEnhancedTimerStore.subscribe(
      (state) => ({
        status: state.status,
        linkedTaskId: state.linkedTask?.taskId,
        sessionId: state.sessionId,
        sessionStartTime: state.sessionStartTime,
        pausedDuration: state.pausedDuration,
      }),
      (current) => {
        if (current.linkedTaskId) {
          dataSyncUtilities.handleTimerStateChange(
            current.linkedTaskId,
            user.id,
            {
              status: current.status,
              sessionId: current.sessionId,
              sessionStartTime: current.sessionStartTime,
              pausedDuration: current.pausedDuration,
            }
          );
        }
      }
    );

    return unsubscribe;
  }, [user?.id]);

  return {
    broadcastTimerState,
    isConnected: !!syncChannelRef.current,
  };
}

/**
 * Hook for handling timer-related task operations
 */
export function useTimerTaskOperations() {
  const { user } = useSupabaseAuth();
  const { broadcastTimerState } = useTimerSync();

  // Handle task completion with active timer
  const handleTaskCompletion = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user?.id) return true;

    // Get current timer state without subscribing
    const currentState = useEnhancedTimerStore.getState();
    const hasActiveTimer = currentState.linkedTask?.taskId === taskId && currentState.status !== 'idle';

    if (hasActiveTimer) {
      // Show confirmation dialog
      const shouldStopTimer = window.confirm(
        'This task has an active timer running. Do you want to stop the timer and mark the task as complete?'
      );

      if (shouldStopTimer) {
        try {
          // Stop the timer with completion feedback
          await currentState.stopTimer({
            userId: user.id,
            productivityRating: 4, // Default good rating for completed task
            notes: 'Task completed',
            completedSubtasks: ['Task marked as complete'],
          });

          // Broadcast the state change
          await broadcastTimerState({ status: 'idle' });

          return true;
        } catch (error) {
          console.error('Error stopping timer for completed task:', error);
          return false;
        }
      } else {
        // User chose not to stop timer, don't complete task
        return false;
      }
    }

    return true; // No active timer, proceed with completion
  }, [user?.id, broadcastTimerState]);

  // Handle task deletion with active timer
  const handleTaskDeletion = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user?.id) return true;

    // Get current timer state without subscribing
    const currentState = useEnhancedTimerStore.getState();
    const hasActiveTimer = currentState.linkedTask?.taskId === taskId && currentState.status !== 'idle';

    if (hasActiveTimer) {
      // Show confirmation dialog
      const shouldStopTimer = window.confirm(
        'This task has an active timer running. Deleting the task will stop the timer. Do you want to continue?'
      );

      if (shouldStopTimer) {
        try {
          // Stop the timer
          await currentState.stopTimer({
            userId: user.id,
            notes: 'Task deleted',
          });

          // Broadcast the state change
          await broadcastTimerState({ status: 'idle' });

          return true;
        } catch (error) {
          console.error('Error stopping timer for deleted task:', error);
          return false;
        }
      } else {
        // User chose not to delete task with active timer
        return false;
      }
    }

    return true; // No active timer, proceed with deletion
  }, [user?.id, broadcastTimerState]);

  // Check if a task has an active timer
  const hasActiveTimer = useCallback((taskId: string): boolean => {
    const currentState = useEnhancedTimerStore.getState();
    return currentState.linkedTask?.taskId === taskId && currentState.status !== 'idle';
  }, []);

  // Get timer status for a specific task
  const getTaskTimerStatus = useCallback((taskId: string) => {
    const currentState = useEnhancedTimerStore.getState();
    if (currentState.linkedTask?.taskId === taskId) {
      return {
        status: currentState.status,
        sessionId: currentState.sessionId,
        startTime: currentState.sessionStartTime,
        pausedDuration: currentState.pausedDuration,
        currentTime: currentState.status === 'running'
          ? Math.floor((Date.now() - currentState.sessionStartTime.getTime() + currentState.pausedDuration) / 1000)
          : Math.floor(currentState.pausedDuration / 1000),
      };
    }
    return null;
  }, []);

  return {
    handleTaskCompletion,
    handleTaskDeletion,
    hasActiveTimer,
    getTaskTimerStatus,
  };
}