import { useEffect, useCallback, useRef } from 'react';
import { useEnhancedTimerStore } from '@/stores/enhancedTimerStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { dataSyncUtilities } from '@/utils/dataSyncUtilities';
import { supabase } from '@/integrations/supabase/client';

interface TimerSyncState {
  sessionId?: string;
  taskId?: string;
  userId: string;
  status: 'idle' | 'running' | 'paused';
  startTime?: number;
  pausedDuration?: number;
  lastUpdate: number;
}

/**
 * Hook for managing real-time timer synchronization across devices and pages
 */
export function useTimerSync() {
  const { user } = useSupabaseAuth();
  const timerStore = useEnhancedTimerStore();
  const syncChannelRef = useRef<any>(null);
  const lastSyncRef = useRef<number>(0);
  const syncIntervalRef = useRef<NodeJS.Timeout>();

  // Broadcast timer state to other devices/tabs
  const broadcastTimerState = useCallback(async (state: Partial<TimerSyncState>) => {
    if (!user?.id || !syncChannelRef.current) return;

    try {
      const syncState: TimerSyncState = {
        userId: user.id,
        status: timerStore.status,
        sessionId: timerStore.sessionId,
        taskId: timerStore.linkedTask?.taskId,
        startTime: timerStore.sessionStartTime.getTime(),
        pausedDuration: timerStore.pausedDuration,
        lastUpdate: Date.now(),
        ...state,
      };

      await syncChannelRef.current.send({
        type: 'timer_state_update',
        payload: syncState,
      });

      lastSyncRef.current = Date.now();
      console.log('Timer state broadcasted:', syncState);
    } catch (error) {
      console.error('Error broadcasting timer state:', error);
    }
  }, [user?.id, timerStore]);

  // Handle incoming timer state updates
  const handleTimerStateUpdate = useCallback(async (payload: TimerSyncState) => {
    if (!user?.id || payload.userId !== user.id) return;

    // Prevent processing our own broadcasts
    if (payload.lastUpdate <= lastSyncRef.current) return;

    try {
      console.log('Received timer state update:', payload);

      // Update local timer state if the remote state is newer
      const localLastUpdate = timerStore.sessionStartTime.getTime();
      if (payload.lastUpdate > localLastUpdate) {
        
        // If task changed, link new task
        if (payload.taskId && payload.taskId !== timerStore.linkedTask?.taskId) {
          await timerStore.linkTaskToTimer(payload.taskId, user.id);
        }

        // Update timer status and timing
        if (payload.status !== timerStore.status) {
          switch (payload.status) {
            case 'running':
              if (timerStore.status !== 'running') {
                await timerStore.startTimer(payload.taskId, user.id);
              }
              break;
            case 'paused':
              if (timerStore.status === 'running') {
                await timerStore.pauseTimer();
              }
              break;
            case 'idle':
              if (timerStore.status !== 'idle') {
                await timerStore.stopTimer({ userId: user.id });
              }
              break;
          }
        }

        // Sync timing data
        if (payload.startTime && payload.pausedDuration !== undefined) {
          // Update internal timing state (this would require extending the store)
          console.log('Syncing timing data:', {
            startTime: payload.startTime,
            pausedDuration: payload.pausedDuration,
          });
        }
      }
    } catch (error) {
      console.error('Error handling timer state update:', error);
    }
  }, [user?.id, timerStore]);

  // Set up real-time subscription
  useEffect(() => {
    if (!user?.id) return;

    const channelName = `timer_sync_${user.id}`;
    
    // Create Supabase realtime channel
    syncChannelRef.current = supabase.channel(channelName)
      .on('broadcast', { event: 'timer_state_update' }, ({ payload }) => {
        handleTimerStateUpdate(payload);
      })
      .subscribe((status) => {
        console.log('Timer sync channel status:', status);
      });

    // Set up periodic sync for active timers
    syncIntervalRef.current = setInterval(() => {
      if (timerStore.status === 'running' && timerStore.linkedTask) {
        broadcastTimerState({});
      }
    }, 10000); // Sync every 10 seconds when timer is running

    return () => {
      if (syncChannelRef.current) {
        syncChannelRef.current.unsubscribe();
        syncChannelRef.current = null;
      }
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [user?.id, handleTimerStateUpdate, broadcastTimerState]);

  // Broadcast state changes
  useEffect(() => {
    if (timerStore.status !== 'idle' && timerStore.linkedTask) {
      broadcastTimerState({});
    }
  }, [timerStore.status, timerStore.linkedTask?.taskId, broadcastTimerState]);

  // Sync with task data when timer state changes
  useEffect(() => {
    if (user?.id && timerStore.linkedTask?.taskId) {
      dataSyncUtilities.handleTimerStateChange(
        timerStore.linkedTask.taskId,
        user.id,
        {
          status: timerStore.status,
          sessionId: timerStore.sessionId,
          sessionStartTime: timerStore.sessionStartTime,
          pausedDuration: timerStore.pausedDuration,
        }
      );
    }
  }, [user?.id, timerStore.status, timerStore.linkedTask?.taskId, timerStore.sessionId]);

  return {
    broadcastTimerState,
    isConnected: !!syncChannelRef.current,
  };
}

/**
 * Hook for handling timer-related task operations
 */
export function useTimerTaskOperations() {
  const { user } = useSupabaseAuth();
  const timerStore = useEnhancedTimerStore();
  const { broadcastTimerState } = useTimerSync();

  // Handle task completion with active timer
  const handleTaskCompletion = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user?.id) return true;

    // Check if this task has an active timer
    const hasActiveTimer = timerStore.linkedTask?.taskId === taskId && timerStore.status !== 'idle';

    if (hasActiveTimer) {
      // Show confirmation dialog
      const shouldStopTimer = window.confirm(
        'This task has an active timer running. Do you want to stop the timer and mark the task as complete?'
      );

      if (shouldStopTimer) {
        try {
          // Stop the timer with completion feedback
          await timerStore.stopTimer({ 
            userId: user.id,
            productivityRating: 4, // Default good rating for completed task
            notes: 'Task completed',
            completedSubtasks: ['Task marked as complete'],
          });

          // Broadcast the state change
          await broadcastTimerState({ status: 'idle' });

          return true;
        } catch (error) {
          console.error('Error stopping timer for completed task:', error);
          return false;
        }
      } else {
        // User chose not to stop timer, don't complete task
        return false;
      }
    }

    return true; // No active timer, proceed with completion
  }, [user?.id, timerStore, broadcastTimerState]);

  // Handle task deletion with active timer
  const handleTaskDeletion = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user?.id) return true;

    // Check if this task has an active timer
    const hasActiveTimer = timerStore.linkedTask?.taskId === taskId && timerStore.status !== 'idle';

    if (hasActiveTimer) {
      // Show confirmation dialog
      const shouldStopTimer = window.confirm(
        'This task has an active timer running. Deleting the task will stop the timer. Do you want to continue?'
      );

      if (shouldStopTimer) {
        try {
          // Stop the timer
          await timerStore.stopTimer({ 
            userId: user.id,
            notes: 'Task deleted',
          });

          // Broadcast the state change
          await broadcastTimerState({ status: 'idle' });

          return true;
        } catch (error) {
          console.error('Error stopping timer for deleted task:', error);
          return false;
        }
      } else {
        // User chose not to delete task with active timer
        return false;
      }
    }

    return true; // No active timer, proceed with deletion
  }, [user?.id, timerStore, broadcastTimerState]);

  // Check if a task has an active timer
  const hasActiveTimer = useCallback((taskId: string): boolean => {
    return timerStore.linkedTask?.taskId === taskId && timerStore.status !== 'idle';
  }, [timerStore.linkedTask?.taskId, timerStore.status]);

  // Get timer status for a specific task
  const getTaskTimerStatus = useCallback((taskId: string) => {
    if (timerStore.linkedTask?.taskId === taskId) {
      return {
        status: timerStore.status,
        sessionId: timerStore.sessionId,
        startTime: timerStore.sessionStartTime,
        pausedDuration: timerStore.pausedDuration,
        currentTime: timerStore.status === 'running' 
          ? Math.floor((Date.now() - timerStore.sessionStartTime.getTime() + timerStore.pausedDuration) / 1000)
          : Math.floor(timerStore.pausedDuration / 1000),
      };
    }
    return null;
  }, [timerStore]);

  return {
    handleTaskCompletion,
    handleTaskDeletion,
    hasActiveTimer,
    getTaskTimerStatus,
  };
}