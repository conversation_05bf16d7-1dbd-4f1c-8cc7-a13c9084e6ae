import React from 'react';
import { motion } from 'framer-motion';
import { TaskAnalyticsDashboard } from '@/components/enhanced-tasks/TaskAnalyticsDashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Brain, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const ComprehensiveAnalytics: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="text-gray-400 hover:text-white"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="p-3 bg-violet-500/20 rounded-lg">
              <Brain className="h-8 w-8 text-violet-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">
                Comprehensive Analytics
              </h1>
              <p className="text-gray-400 mt-1">
                Deep insights into your productivity patterns, task completion, and time management
              </p>
            </div>
          </div>
        </motion.div>

        {/* Analytics Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <TaskAnalyticsDashboard 
            mode="comprehensive"
            className="w-full"
          />
        </motion.div>

        {/* Additional Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8"
        >
          <Card className="bg-[#030303]/80 backdrop-blur-md border border-gray-800/50">
            <CardHeader>
              <CardTitle className="text-white text-lg">
                About These Analytics
              </CardTitle>
            </CardHeader>
            <CardContent className="text-gray-300 space-y-4">
              <p>
                This comprehensive analytics dashboard provides deep insights into your productivity patterns 
                and task management effectiveness. The data is analyzed across multiple dimensions to help you 
                optimize your study habits and work efficiency.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-white mb-2">What's Analyzed:</h3>
                  <ul className="space-y-1 text-sm">
                    <li>• Task completion rates and velocity</li>
                    <li>• Time estimation accuracy</li>
                    <li>• Productivity patterns by time and day</li>
                    <li>• Subject and task type performance</li>
                    <li>• Trends and improvement opportunities</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold text-white mb-2">How to Use:</h3>
                  <ul className="space-y-1 text-sm">
                    <li>• Review patterns to optimize your schedule</li>
                    <li>• Use insights to improve time estimation</li>
                    <li>• Focus on high-performing subjects/times</li>
                    <li>• Address declining productivity trends</li>
                    <li>• Apply improvement suggestions</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default ComprehensiveAnalytics;