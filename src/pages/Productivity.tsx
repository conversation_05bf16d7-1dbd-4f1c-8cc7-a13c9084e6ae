import { useEffect, useState } from "react"
import { useNavigate, useLocation } from "react-router-dom" // Import useLocation
import { ProductivityLink } from "@/components/productivity/ProductivityLink" // Import our custom Link component
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { StudyTimer } from "@/components/productivity/StudyTimer"
import { AddStudySessionButton } from "@/components/productivity/AddStudySessionButton"

import { TasksDropdown } from "@/components/productivity/TasksDropdown"
import { EnhancedTaskSelector } from "@/components/productivity/EnhancedTaskSelector"
import { EnhancedTodoItem } from "@/types/todo"
import { ExamCountdown } from "@/components/productivity/ExamCountdown"
import { SpotifyBar } from "@/components/productivity/SpotifyBar"
import { LeaderboardButton } from "@/components/productivity/LeaderboardButton"
import { AITaskRecommendations } from "@/components/productivity/AITaskRecommendations"
import { TimeBlockingInterface } from "@/components/productivity/TimeBlockingInterface"
import { DistractionBlockingPanel } from "@/components/productivity/DistractionBlockingPanel"
import { TaskSpecificBreakSuggestions } from "@/components/productivity/TaskSpecificBreakSuggestions"
import { RecurringTaskTimerTemplates } from "@/components/productivity/RecurringTaskTimerTemplates"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext"
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import { useLocalStorage } from "@/hooks/useLocalStorage"
import { useTimerSync } from "@/hooks/useTimerSync"
import { Settings, Trophy, Flame, Brain, ChevronLeft, ChevronRight, Calendar, Shield, Timer, Repeat } from "lucide-react"

import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning';
import { toast } from '@/components/ui/use-toast';

import Header from "@/components/shared/Header"
// Removed Firebase imports - now using Supabase
import { Button } from "@/components/ui/button" // Import Button component

// Collection of motivational quotes for students
const motivationalQuotes = [
  "Every hour of focused study brings you one step closer to your dream college 🎓",
  "Consistency + Hard Work + Smart Study = Results 📊",
  "Master one concept at a time. Small victories lead to exam triumph 🏆",
  "Your rank is determined by what you do today, not tomorrow 🚀",
  "The best way to predict your future is to create it through disciplined study 📚",
  "The questions you solve today are the answers you'll know in the exam 💡",
  "Study smarter: 25 minutes of deep focus beats hours of distracted effort 🧠",
  "Your competition is studying right now. Are you? 💪",
  "Difficult formulas often lead to beautiful solutions ✨",
  "Track your progress, celebrate small wins, achieve big goals 📈",
  "When you feel like giving up, remember why you started 🌟",
  "Turn study stress into study success through planned breaks 🌿",
  "Today's productive study session is tomorrow's exam confidence 🎯",
  "The Pomodoro that seems hardest often teaches you the most 🍅",
  "Your notes today become your knowledge tomorrow 📝",
  "Study with purpose: every minute counts on the path to success ⏳",
  "The best students aren't always the smartest, but they're always the most consistent 🔄",
  "Chemistry, like success, is all about the right reactions at the right time ⚗️"
];

// Interface for productivity visibility settings
interface ProductivityVisibilitySettings {
  showQuotes: boolean;
  showTasks: boolean;
  showExamCountdown: boolean;
  showSpotifyBar: boolean;
  spotifyCollapsed: boolean;
}

// Default visibility settings
const DEFAULT_VISIBILITY_SETTINGS: ProductivityVisibilitySettings = {
  showQuotes: true,
  showTasks: true,
  showExamCountdown: true,
  showSpotifyBar: true,
  spotifyCollapsed: false
};

export default function Productivity() {
  useDocumentTitle("Productivity - IsotopeAI");
  const { user, loading } = useSupabaseAuth()
  
  // Initialize timer synchronization
  useTimerSync();
  const navigate = useNavigate()
  const location = useLocation(); // Get location object
  const [mode, setMode] = useState<"pomodoro" | "stopwatch">("stopwatch")
  // State to hold the initial selected subject from navigation state
  const [initialSubject, setInitialSubject] = useState<string | null>(null);
  // State for selected task in enhanced task selector
  const [selectedTask, setSelectedTask] = useState<EnhancedTodoItem | null>(null);
  const { getBackgroundStyle } = useBackgroundTheme() // Removed unused backgroundTheme
  const [quote, setQuote] = useState<string>("")
  const [showAIRecommendations, setShowAIRecommendations] = useState(false);
  const [aiPanelCollapsed, setAiPanelCollapsed] = useState(true);
  
  // Advanced timer features state
  const [showTimeBlocking, setShowTimeBlocking] = useState(false);
  const [showDistractionBlocking, setShowDistractionBlocking] = useState(false);
  const [showBreakSuggestions, setShowBreakSuggestions] = useState(false);
  const [showRecurringTemplates, setShowRecurringTemplates] = useState(false);
  const [isDistractionBlockingActive, setIsDistractionBlockingActive] = useState(false);
  const [sessionDuration, setSessionDuration] = useState(0);
  const [timerStartTime, setTimerStartTime] = useState<Date | null>(null);

  // Visibility settings state
  const [visibilitySettings, setVisibilitySettings] = useState<ProductivityVisibilitySettings>(DEFAULT_VISIBILITY_SETTINGS);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);

  // Set Spotify states based on visibility settings
  const [spotifyVisible, setSpotifyVisible] = useLocalStorage<boolean>('spotify-is-visible', DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar);
  const [spotifyCollapsed, setSpotifyCollapsed] = useLocalStorage<boolean>('spotify-is-collapsed', DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed);

  // Track timer status for beforeunload warning
  const [timerStatus] = useState<"idle" | "running" | "paused">("idle");

  // Load productivity settings from Supabase
  useEffect(() => {
    const fetchProductivitySettings = async () => {
      if (!user) return;

      try {
        // Import the function to get user profile
        const { getExtendedUserProfile } = await import('@/utils/supabase');
        const userData = await getExtendedUserProfile(user.id);

        if (userData?.progress?.productivitySettings) {
          const settings = userData.progress.productivitySettings;
          setVisibilitySettings({
            showQuotes: settings.showQuotes ?? DEFAULT_VISIBILITY_SETTINGS.showQuotes,
            showTasks: settings.showTasks ?? DEFAULT_VISIBILITY_SETTINGS.showTasks,
            showExamCountdown: settings.showExamCountdown ?? DEFAULT_VISIBILITY_SETTINGS.showExamCountdown,
            showSpotifyBar: settings.showSpotifyBar ?? DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar,
            spotifyCollapsed: settings.spotifyCollapsed ?? DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed
          });
          setSpotifyVisible(settings.showSpotifyBar ?? DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar);
          setSpotifyCollapsed(settings.spotifyCollapsed ?? DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed);
        } else {
          // Use default settings if none found
          setVisibilitySettings(DEFAULT_VISIBILITY_SETTINGS);
          setSpotifyVisible(DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar);
          setSpotifyCollapsed(DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed);
        }
      } catch (error) {
        console.error('Error fetching productivity settings:', error);
        // Fall back to defaults on error
        setVisibilitySettings(DEFAULT_VISIBILITY_SETTINGS);
        setSpotifyVisible(DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar);
        setSpotifyCollapsed(DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed);
      } finally {
        setIsLoadingSettings(false);
      }
    };

    fetchProductivitySettings();
  }, [user, setSpotifyVisible, setSpotifyCollapsed]);

  // Select a random quote when the component mounts
  useEffect(() => {
    if (visibilitySettings.showQuotes) {
      const randomIndex = Math.floor(Math.random() * motivationalQuotes.length);
      setQuote(motivationalQuotes[randomIndex]);
    }
  }, [visibilitySettings.showQuotes]);

  // Effect to check for selectedSubject in navigation state on mount
  useEffect(() => {
    if (location.state && (location.state as any).selectedSubject) {
      setInitialSubject((location.state as any).selectedSubject);
      // Optionally clear the state after reading it to prevent re-applying on refresh
      // navigate(location.pathname, { replace: true });
    }
  }, [location.state]); // Depend on location.state

  // Add beforeunload event listener to prevent accidental tab closing
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Only show warning if timer is active (running or paused)
      if (timerStatus === "running" || timerStatus === "paused") {
        // Standard way to show a confirmation dialog before closing the tab
        const confirmationMessage = "You have an active study session. Are you sure you want to leave? Your progress might be lost.";
        e.preventDefault();
        e.returnValue = confirmationMessage; // Required for Chrome
        return confirmationMessage; // For older browsers
      }
    };

    // Add the event listener
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [timerStatus]); // Add timerStatus as a dependency

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login')
    }
  }, [user, loading, navigate])

  if (loading || isLoadingSettings) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  return (
    <div className={cn("relative min-h-screen w-full text-foreground overflow-hidden flex flex-col", getBackgroundStyle())} style={{ position: 'relative' }}>
      <SmallDeviceWarning />

      {/* Animated gradient orbs (Dark mode only) */}
      <div className="absolute top-20 right-[10%] w-64 h-64 rounded-full bg-purple-600/10 blur-[100px] animate-float-slow dark:block hidden"></div>
      <div className="absolute bottom-20 left-[5%] w-80 h-80 rounded-full bg-blue-600/10 blur-[120px] animate-float-slower dark:block hidden"></div>
      <div className="absolute top-[40%] left-[20%] w-40 h-40 rounded-full bg-pink-600/5 blur-[80px] animate-pulse-slow dark:block hidden"></div>

      {/* Subtle particle effect (Dark mode only) */}
      <div className="absolute inset-0 bg-[url('/stars.png')] bg-repeat opacity-[0.07] dark:block hidden"></div>

      {/* Top gradient overlay (Dark mode only) */}
      <div className="absolute top-0 left-0 right-0 h-72 bg-gradient-to-b from-purple-900/30 via-indigo-900/10 to-transparent dark:block hidden"></div>

      {/* Header section - fixed height */}
      <div className="flex-none">
        <Header />
      </div>

      {/* Main content section - scrollable, now with flex and centering */}
      <div className="flex-1 flex relative pt-16 sm:pt-20">
        {/* AI Recommendations Panel */}
        <div className={cn(
          "fixed left-0 top-0 h-full bg-background/95 dark:bg-background/95 backdrop-blur-sm border-r border-border dark:border-white/10 z-50 transition-transform duration-300 ease-in-out",
          aiPanelCollapsed ? "-translate-x-full" : "translate-x-0",
          "w-80 sm:w-96"
        )}>
          <div className="h-full flex flex-col">
            {/* Panel Header */}
            <div className="flex items-center justify-between p-4 border-b border-border dark:border-white/10">
              <div className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-purple-500" />
                <h2 className="font-semibold">AI Assistant</h2>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAiPanelCollapsed(true)}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
            </div>
            
            {/* Panel Content */}
            <div className="flex-1 overflow-hidden">
              {user && (
                <AITaskRecommendations
                  userId={user.id}
                  currentTask={selectedTask || undefined}
                  sessionDuration={Math.floor((Date.now() - (timerStartTime || Date.now())) / 60000)}
                  availableTime={60}
                  onTaskSelect={setSelectedTask}
                  onStartTimer={(taskId) => {
                    // Handle timer start from AI recommendations
                    console.log('Starting timer for task:', taskId);
                  }}
                  className="p-4 h-full"
                />
              )}
            </div>
          </div>
        </div>

        {/* AI Panel Toggle Button */}
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "fixed left-4 top-1/2 -translate-y-1/2 z-40 transition-all duration-300",
            "bg-background/80 dark:bg-background/80 backdrop-blur-sm",
            "border-border dark:border-white/10",
            "hover:bg-background dark:hover:bg-background",
            "shadow-lg hover:shadow-xl",
            !aiPanelCollapsed && "translate-x-80 sm:translate-x-96"
          )}
          onClick={() => setAiPanelCollapsed(!aiPanelCollapsed)}
        >
          {aiPanelCollapsed ? (
            <>
              <Brain className="w-4 h-4 mr-2" />
              <ChevronRight className="w-4 h-4" />
            </>
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </Button>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col justify-center items-center">
          {/* Exam Countdown - Only visible on larger screens if enabled */}
          {visibilitySettings.showExamCountdown && (
            <div className="hidden sm:block absolute top-4 right-4 z-40">
              <ExamCountdown />
            </div>
          )}

        {/* Draggable Tasks Dropdown - Only if enabled */}
        {visibilitySettings.showTasks && (
          <div className="relative z-30 w-full mt-4 sm:mt-20">
            {/* Pass the initialSubject state as a prop */}
            <TasksDropdown initialSelectedSubject={initialSubject} />
          </div>
        )}

          {/* Main content with subtle animations - now centered */}
          <div className="flex-1 flex items-center justify-center px-4 animate-fadeIn w-full mt-4 sm:mt-0">
            <div className="w-full max-w-xl">
            {/* Enhanced tabs with beautiful box and subtle animation */}
            <div className="w-full mb-12 flex justify-center">
              {/* Use theme-aware background, border, and shadow */}
              <div className="bg-background/50 dark:bg-white/5 backdrop-blur-sm border border-border dark:border-white/10 rounded-full p-1.5 shadow-lg shadow-primary/10 dark:shadow-purple-500/10 inline-block hover:shadow-primary/20 dark:hover:shadow-purple-500/20 transition-all duration-500">
                <Tabs
                  value={mode}
                  onValueChange={(value) => setMode(value as "pomodoro" | "stopwatch")}
                  className="w-full"
                >
                  <TabsList className="grid grid-cols-2 bg-transparent gap-1">
                    <TabsTrigger
                      value="pomodoro"
                      className={cn(
                        "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md",
                        "text-muted-foreground rounded-full py-2 px-6 flex items-center justify-center transition-all duration-300"
                      )}
                    >
                      Pomodoro
                    </TabsTrigger>
                    <TabsTrigger
                      value="stopwatch"
                      className={cn(
                        "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md",
                        "text-muted-foreground rounded-full py-2 px-6 flex items-center justify-center transition-all duration-300"
                      )}
                    >
                      Stopwatch
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>

            {/* Enhanced Task Selector */}
            <div className="w-full mb-8 flex justify-center">
              <EnhancedTaskSelector
                onTaskSelect={setSelectedTask}
                selectedTaskId={selectedTask?.id}
                className="max-w-lg"
              />
            </div>

            <StudyTimer mode={mode} />

            {/* Exam Countdown - Only visible on small screens if enabled */}
            {visibilitySettings.showExamCountdown && (
              <div className="sm:hidden mt-8 flex justify-center">
                <ExamCountdown />
              </div>
            )}
            </div>
          </div>
        </div>

        {/* Enhanced quote section with animation - now better positioned - Only if enabled */}
        {visibilitySettings.showQuotes && quote && (
          <div className="w-full pb-8 px-4">
            {/* Styled quote with decorative elements */}
            <div className="relative max-w-xl mx-auto px-6 animate-fadeIn">
              {/* Large quotation marks with improved visibility in dark mode */}
              <div className="absolute top-0 left-0 text-primary/30 dark:text-primary/40 text-6xl font-serif leading-none">&ldquo;</div>
              <div className="absolute bottom-0 right-0 text-primary/30 dark:text-primary/40 text-6xl font-serif leading-none">&rdquo;</div>

              {/* Quote text with improved typography */}
              <p className="font-serif italic text-xl sm:text-2xl text-center mx-auto text-muted-foreground py-6 px-4">
                {quote}
              </p>

              {/* Decorative line with improved visibility in dark mode */}
              <div className="w-24 h-1 bg-primary/30 dark:bg-primary/50 mx-auto mt-2 rounded-full"></div>
            </div>
          </div>
        )}

        {/* Spotify Bar - Only if enabled */}
        {visibilitySettings.showSpotifyBar && spotifyVisible && (
          <div className="w-full max-w-xl mb-12 px-4 flex justify-center animate-fadeIn">
            <SpotifyBar
              className={cn("w-full", spotifyCollapsed && "mx-auto w-auto")}
              isCollapsed={spotifyCollapsed}
              setIsCollapsed={setSpotifyCollapsed}
              onToggleCollapse={(collapsed) => setSpotifyCollapsed(collapsed)}
            />
          </div>
        )}
      </div>

      {/* Bottom buttons row - aligned in a fixed container */}
      <div className="fixed bottom-8 right-8 z-50 flex flex-col items-end gap-3">
        {/* Advanced Timer Features Row */}
        <div className="flex items-center gap-2">
          {/* Time Blocking Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-10 w-10 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
            onClick={() => setShowTimeBlocking(true)}
            title="Time Blocking"
          >
            <Calendar className="h-4 w-4 text-primary" />
          </Button>

          {/* Distraction Blocking Button */}
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-10 w-10 rounded-full shadow-lg border dark:border-slate-800 backdrop-blur-md transition-all duration-300",
              isDistractionBlockingActive 
                ? "bg-green-100 dark:bg-green-900/20 border-green-300 dark:border-green-700 hover:bg-green-200 dark:hover:bg-green-900/30"
                : "bg-background/80 hover:bg-primary/20 dark:hover:bg-white/10"
            )}
            onClick={() => setShowDistractionBlocking(true)}
            title="Focus Environment"
          >
            <Shield className={cn(
              "h-4 w-4 transition-colors duration-300",
              isDistractionBlockingActive ? "text-green-600 dark:text-green-400" : "text-primary"
            )} />
          </Button>

          {/* Recurring Templates Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-10 w-10 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
            onClick={() => setShowRecurringTemplates(true)}
            title="Timer Templates"
          >
            <Repeat className="h-4 w-4 text-primary" />
          </Button>
        </div>

        {/* Main Action Buttons Row */}
        <div className="flex items-center gap-3">
          {/* Settings Button */}
          <ProductivityLink to="/settings">
            <Button
              variant="ghost"
              size="icon"
              className="h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
            >
              <Settings className="h-5 w-5 text-primary" />
            </Button>
          </ProductivityLink>

          {/* Study Sessions Button - Using the component but with custom styling */}
          <AddStudySessionButton
            className="!static h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
          />

          {/* Leaderboard Button - Updated to match new design */}
          <Button
            variant="ghost"
            size="icon"
            className="h-14 w-14 rounded-full shadow-xl border-2 border-yellow-400/30 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 backdrop-blur-md hover:from-yellow-100 hover:to-orange-100 dark:hover:from-yellow-800/30 dark:hover:to-orange-800/30 transition-all duration-300 hover:scale-110 group"
            onClick={() => document.querySelector<HTMLButtonElement>('.leaderboard-trigger-button')?.click()}
          >
            <Trophy className="h-6 w-6 text-yellow-600 dark:text-yellow-400 group-hover:text-yellow-700 dark:group-hover:text-yellow-300 transition-colors duration-300" />
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
              <Flame className="h-2.5 w-2.5 text-white" />
            </div>
          </Button>
        </div>
      </div>

      {/* Keep the original LeaderboardButton for its functionality, but hide it */}
      <div className="hidden">
        <LeaderboardButton />
      </div>

      {/* Advanced Timer Feature Modals */}
      {showTimeBlocking && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <TimeBlockingInterface
            onClose={() => setShowTimeBlocking(false)}
            selectedDate={new Date()}
          />
        </div>
      )}

      {showDistractionBlocking && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <DistractionBlockingPanel
            isActive={isDistractionBlockingActive}
            onToggle={setIsDistractionBlockingActive}
            currentTask={selectedTask ? {
              id: selectedTask.id,
              title: selectedTask.title,
              type: selectedTask.tags[0] || 'study',
              subject: selectedTask.subjectName
            } : undefined}
          />
          <Button
            variant="outline"
            size="sm"
            className="absolute top-4 right-4"
            onClick={() => setShowDistractionBlocking(false)}
          >
            Close
          </Button>
        </div>
      )}

      {showBreakSuggestions && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <TaskSpecificBreakSuggestions
            currentTask={selectedTask ? {
              id: selectedTask.id,
              title: selectedTask.title,
              type: selectedTask.tags[0] || 'study',
              subject: selectedTask.subjectName,
              difficulty: selectedTask.priority === 'high' ? 'hard' : 
                         selectedTask.priority === 'medium' ? 'medium' : 'easy'
            } : undefined}
            sessionDuration={sessionDuration}
            isBreakTime={showBreakSuggestions}
            onStartBreak={(suggestion) => {
              console.log('Starting break:', suggestion);
              // Handle break start logic
            }}
            onSkipBreak={() => setShowBreakSuggestions(false)}
            onCustomizePomodoro={(customization) => {
              console.log('Customizing pomodoro:', customization);
              // Handle pomodoro customization
            }}
          />
        </div>
      )}

      {showRecurringTemplates && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <RecurringTaskTimerTemplates
            onClose={() => setShowRecurringTemplates(false)}
            onApplyTemplate={(template) => {
              console.log('Applying template:', template);
              setShowRecurringTemplates(false);
              toast({
                title: "Template applied",
                description: `Applied "${template.name}" settings`
              });
            }}
            currentTask={selectedTask}
          />
        </div>
      )}
    </div>
  )
}
