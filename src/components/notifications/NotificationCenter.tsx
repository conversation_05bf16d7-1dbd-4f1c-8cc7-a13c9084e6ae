import React, { useState } from 'react';
import { <PERSON>, X, <PERSON>, <PERSON>ert<PERSON>riangle, Target, Zap, Brain, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useSmartNotifications } from '@/hooks/useSmartNotifications';
import { SmartNotification, NotificationType } from '@/services/SmartNotificationService';
import { formatDistanceToNow } from 'date-fns';

// Notification type icons and colors
const notificationConfig: Record<NotificationType, { icon: React.ComponentType<any>; color: string; bgColor: string }> = {
  deadline_reminder: { icon: Clock, color: 'text-orange-600', bgColor: 'bg-orange-50' },
  break_suggestion: { icon: Brain, color: 'text-blue-600', bgColor: 'bg-blue-50' },
  procrastination_nudge: { icon: Target, color: 'text-purple-600', bgColor: 'bg-purple-50' },
  productivity_streak: { icon: Zap, color: 'text-green-600', bgColor: 'bg-green-50' },
  context_reminder: { icon: Bell, color: 'text-indigo-600', bgColor: 'bg-indigo-50' },
  time_estimate_exceeded: { icon: AlertTriangle, color: 'text-red-600', bgColor: 'bg-red-50' },
  task_completion_prompt: { icon: CheckCircle, color: 'text-emerald-600', bgColor: 'bg-emerald-50' },
};

// Priority colors
const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
};

interface NotificationItemProps {
  notification: SmartNotification;
  onMarkRead: (id: string) => void;
  onDismiss: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onMarkRead, onDismiss }) => {
  const config = notificationConfig[notification.type];
  const Icon = config.icon;

  const handleMarkRead = () => {
    if (!notification.isRead) {
      onMarkRead(notification.id);
    }
  };

  const handleDismiss = () => {
    onDismiss(notification.id);
  };

  return (
    <Card className={`mb-3 transition-all duration-200 hover:shadow-md ${
      notification.isRead ? 'opacity-75' : 'border-l-4 border-l-blue-500'
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Icon */}
          <div className={`p-2 rounded-full ${config.bgColor} flex-shrink-0`}>
            <Icon className={`h-4 w-4 ${config.color}`} />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1">
                <h4 className={`font-medium text-sm ${
                  notification.isRead ? 'text-gray-600' : 'text-gray-900'
                }`}>
                  {notification.title}
                </h4>
                <p className={`text-sm mt-1 ${
                  notification.isRead ? 'text-gray-500' : 'text-gray-700'
                }`}>
                  {notification.description}
                </p>
              </div>

              {/* Priority badge */}
              <Badge className={`text-xs ${priorityColors[notification.priority]} flex-shrink-0`}>
                {notification.priority}
              </Badge>
            </div>

            {/* Timestamp */}
            <div className="flex items-center justify-between mt-3">
              <span className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
              </span>

              {/* Actions */}
              <div className="flex items-center gap-2">
                {notification.actions && notification.actions.length > 0 && (
                  <div className="flex gap-1">
                    {notification.actions.slice(0, 2).map((action) => (
                      <Button
                        key={action.id}
                        size="sm"
                        variant={action.type === 'primary' ? 'default' : 'outline'}
                        className="h-7 px-2 text-xs"
                        onClick={() => {
                          action.action();
                          handleMarkRead();
                        }}
                      >
                        {action.label}
                      </Button>
                    ))}
                  </div>
                )}

                {!notification.isRead && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-7 px-2 text-xs text-gray-500 hover:text-gray-700"
                    onClick={handleMarkRead}
                  >
                    Mark read
                  </Button>
                )}

                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0 text-gray-400 hover:text-gray-600"
                  onClick={handleDismiss}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface NotificationCenterProps {
  className?: string;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ className }) => {
  const {
    notifications,
    unreadCount,
    hasUrgentNotifications,
    markAsRead,
    dismiss,
    runAnalysis,
    isLoading
  } = useSmartNotifications();

  const [isOpen, setIsOpen] = useState(false);

  const handleMarkAllRead = () => {
    notifications
      .filter(n => !n.isRead)
      .forEach(n => markAsRead(n.id));
  };

  const handleDismissAll = () => {
    notifications.forEach(n => dismiss(n.id));
  };

  const handleRefresh = () => {
    runAnalysis();
  };

  // Group notifications by type
  const groupedNotifications = notifications.reduce((acc, notification) => {
    if (!acc[notification.type]) {
      acc[notification.type] = [];
    }
    acc[notification.type].push(notification);
    return acc;
  }, {} as Record<NotificationType, SmartNotification[]>);

  const sortedNotifications = notifications.sort((a, b) => {
    // Sort by priority first, then by creation time
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) return priorityDiff;
    
    return b.createdAt - a.createdAt;
  });

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`relative ${className}`}
          onClick={() => setIsOpen(true)}
        >
          <Bell className={`h-5 w-5 ${hasUrgentNotifications ? 'text-red-500' : 'text-gray-600'}`} />
          {unreadCount > 0 && (
            <Badge 
              className={`absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center ${
                hasUrgentNotifications ? 'bg-red-500' : 'bg-blue-500'
              }`}
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>

      <SheetContent className="w-full sm:w-[480px] sm:max-w-[480px]">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifications
              {unreadCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {unreadCount} new
                </Badge>
              )}
            </SheetTitle>
            
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
                className="text-xs"
              >
                Refresh
              </Button>
            </div>
          </div>
        </SheetHeader>

        <div className="mt-6">
          {notifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
              <p className="text-gray-500">
                You're all caught up! We'll notify you when there's something important.
              </p>
            </div>
          ) : (
            <>
              {/* Action buttons */}
              {unreadCount > 0 && (
                <div className="flex gap-2 mb-4">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleMarkAllRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleDismissAll}
                    className="text-xs text-red-600 hover:text-red-700"
                  >
                    Dismiss all
                  </Button>
                </div>
              )}

              {/* Notifications list */}
              <ScrollArea className="h-[calc(100vh-200px)]">
                <div className="space-y-1">
                  {sortedNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkRead={markAsRead}
                      onDismiss={dismiss}
                    />
                  ))}
                </div>
              </ScrollArea>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};

// Notification summary component for dashboard
export const NotificationSummary: React.FC = () => {
  const { notifications, unreadCount, hasUrgentNotifications } = useSmartNotifications();

  if (notifications.length === 0) return null;

  const urgentNotifications = notifications.filter(n => n.priority === 'urgent' && !n.isRead);
  const recentNotifications = notifications
    .filter(n => !n.isRead)
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(0, 3);

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Bell className="h-4 w-4" />
          Recent Notifications
          {unreadCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {urgentNotifications.length > 0 && (
          <div className="mb-3">
            <h4 className="text-xs font-medium text-red-600 mb-2 flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" />
              Urgent
            </h4>
            {urgentNotifications.slice(0, 2).map((notification) => (
              <div key={notification.id} className="text-sm text-red-700 bg-red-50 p-2 rounded mb-1">
                {notification.title}
              </div>
            ))}
          </div>
        )}

        {recentNotifications.length > 0 && (
          <div className="space-y-2">
            {recentNotifications.map((notification) => {
              const config = notificationConfig[notification.type];
              const Icon = config.icon;
              
              return (
                <div key={notification.id} className="flex items-center gap-2 text-sm">
                  <Icon className={`h-3 w-3 ${config.color} flex-shrink-0`} />
                  <span className="text-gray-700 truncate">{notification.title}</span>
                  <span className="text-xs text-gray-500 ml-auto">
                    {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                  </span>
                </div>
              );
            })}
          </div>
        )}

        {unreadCount > 3 && (
          <div className="text-xs text-gray-500 mt-2 text-center">
            +{unreadCount - 3} more notifications
          </div>
        )}
      </CardContent>
    </Card>
  );
};