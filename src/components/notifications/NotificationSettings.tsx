import React from 'react';
import { <PERSON>, <PERSON>, Brain, Target, Zap, Volume2, VolumeX } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useNotificationSettings } from '@/hooks/useSmartNotifications';
import { NotificationSettings } from '@/services/SmartNotificationService';

interface NotificationSettingsProps {
  className?: string;
}

export const NotificationSettingsComponent: React.FC<NotificationSettingsProps> = ({ className }) => {
  const { settings, isLoading, updateSettings, resetSettings } = useNotificationSettings();

  if (isLoading || !settings) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleToggle = (key: keyof NotificationSettings, value: boolean) => {
    updateSettings({ [key]: value });
  };

  const handleSliderChange = (key: keyof NotificationSettings, value: number[]) => {
    updateSettings({ [key]: value[0] });
  };

  const handleQuietHoursChange = (type: 'start' | 'end', value: number[]) => {
    updateSettings({
      quietHours: {
        ...settings.quietHours,
        [type]: value[0]
      }
    });
  };

  const formatHour = (hour: number): string => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Smart Notifications
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={resetSettings}
            className="text-xs"
          >
            Reset to Default
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Master toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-base font-medium">Enable Smart Notifications</Label>
            <p className="text-sm text-gray-500">
              Turn on intelligent notifications to help you stay productive
            </p>
          </div>
          <Switch
            checked={settings.enabled}
            onCheckedChange={(checked) => handleToggle('enabled', checked)}
          />
        </div>

        <Separator />

        {/* Notification types */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Notification Types</h3>
          
          {/* Deadline reminders */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Clock className="h-4 w-4 text-orange-600" />
              <div>
                <Label className="text-sm font-medium">Deadline Reminders</Label>
                <p className="text-xs text-gray-500">
                  Get intelligent reminders based on task complexity and available time
                </p>
              </div>
            </div>
            <Switch
              checked={settings.deadlineReminders}
              onCheckedChange={(checked) => handleToggle('deadlineReminders', checked)}
              disabled={!settings.enabled}
            />
          </div>

          {/* Break suggestions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Brain className="h-4 w-4 text-blue-600" />
              <div>
                <Label className="text-sm font-medium">Break Suggestions</Label>
                <p className="text-xs text-gray-500">
                  Receive optimal break timing notifications during study sessions
                </p>
              </div>
            </div>
            <Switch
              checked={settings.breakSuggestions}
              onCheckedChange={(checked) => handleToggle('breakSuggestions', checked)}
              disabled={!settings.enabled}
            />
          </div>

          {/* Procrastination nudges */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Target className="h-4 w-4 text-purple-600" />
              <div>
                <Label className="text-sm font-medium">Procrastination Nudges</Label>
                <p className="text-xs text-gray-500">
                  Get motivational nudges when procrastination patterns are detected
                </p>
              </div>
            </div>
            <Switch
              checked={settings.procrastinationNudges}
              onCheckedChange={(checked) => handleToggle('procrastinationNudges', checked)}
              disabled={!settings.enabled}
            />
          </div>

          {/* Productivity streaks */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Zap className="h-4 w-4 text-green-600" />
              <div>
                <Label className="text-sm font-medium">Productivity Streaks</Label>
                <p className="text-xs text-gray-500">
                  Celebrate milestones and get positive reinforcement
                </p>
              </div>
            </div>
            <Switch
              checked={settings.productivityStreaks}
              onCheckedChange={(checked) => handleToggle('productivityStreaks', checked)}
              disabled={!settings.enabled}
            />
          </div>

          {/* Context reminders */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="h-4 w-4 text-indigo-600" />
              <div>
                <Label className="text-sm font-medium">Context-Aware Reminders</Label>
                <p className="text-xs text-gray-500">
                  Get reminders that respect your current activity and productive hours
                </p>
              </div>
            </div>
            <Switch
              checked={settings.contextReminders}
              onCheckedChange={(checked) => handleToggle('contextReminders', checked)}
              disabled={!settings.enabled}
            />
          </div>
        </div>

        <Separator />

        {/* Advanced settings */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Advanced Settings</h3>

          {/* Quiet hours */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <VolumeX className="h-4 w-4" />
              Quiet Hours
            </Label>
            <p className="text-xs text-gray-500 mb-3">
              Set hours when you don't want to receive notifications
            </p>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-xs text-gray-600">Start Time</Label>
                <div className="mt-2">
                  <Slider
                    value={[settings.quietHours.start]}
                    onValueChange={(value) => handleQuietHoursChange('start', value)}
                    max={23}
                    min={0}
                    step={1}
                    disabled={!settings.enabled}
                    className="w-full"
                  />
                  <div className="text-xs text-gray-500 mt-1 text-center">
                    {formatHour(settings.quietHours.start)}
                  </div>
                </div>
              </div>
              
              <div>
                <Label className="text-xs text-gray-600">End Time</Label>
                <div className="mt-2">
                  <Slider
                    value={[settings.quietHours.end]}
                    onValueChange={(value) => handleQuietHoursChange('end', value)}
                    max={23}
                    min={0}
                    step={1}
                    disabled={!settings.enabled}
                    className="w-full"
                  />
                  <div className="text-xs text-gray-500 mt-1 text-center">
                    {formatHour(settings.quietHours.end)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Max notifications per hour */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Maximum Notifications per Hour</Label>
            <p className="text-xs text-gray-500">
              Limit the number of notifications to avoid overwhelming you
            </p>
            <div className="px-2">
              <Slider
                value={[settings.maxNotificationsPerHour]}
                onValueChange={(value) => handleSliderChange('maxNotificationsPerHour', value)}
                max={10}
                min={1}
                step={1}
                disabled={!settings.enabled}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1</span>
                <span className="font-medium">{settings.maxNotificationsPerHour}</span>
                <span>10</span>
              </div>
            </div>
          </div>

          {/* Reminder advance time */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Deadline Reminder Advance Time</Label>
            <p className="text-xs text-gray-500">
              How many hours before a deadline should we start reminding you
            </p>
            <div className="px-2">
              <Slider
                value={[settings.reminderAdvanceTime]}
                onValueChange={(value) => handleSliderChange('reminderAdvanceTime', value)}
                max={168}
                min={1}
                step={1}
                disabled={!settings.enabled}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1h</span>
                <span className="font-medium">
                  {settings.reminderAdvanceTime < 24 
                    ? `${settings.reminderAdvanceTime}h` 
                    : `${Math.round(settings.reminderAdvanceTime / 24)}d`
                  }
                </span>
                <span>7d</span>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Status indicator */}
        <div className="flex items-center gap-2 text-sm">
          {settings.enabled ? (
            <>
              <Volume2 className="h-4 w-4 text-green-600" />
              <span className="text-green-600 font-medium">Smart notifications are active</span>
            </>
          ) : (
            <>
              <VolumeX className="h-4 w-4 text-gray-400" />
              <span className="text-gray-500">Smart notifications are disabled</span>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};