import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '../ui/card';
import { PieChart, Activity, TrendingUp, Clock, BookOpen, CheckCircle } from 'lucide-react';

export const AnalyticsOverviewWidget: React.FC = () => {
  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-slate-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-slate-600 to-blue-600 rounded-xl shadow-lg">
            <PieChart className="h-6 w-6 text-white" />
          </div>
          <div>
            <span className="text-xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 dark:from-slate-300 dark:to-blue-300 bg-clip-text text-transparent">
              Analytics Overview
            </span>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive performance metrics and trends
            </p>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-6">
        {/* Enhanced Today's Study Time */}
        <div className="mb-6 p-4 bg-white/60 dark:bg-gray-700/30 rounded-xl border border-slate-200/30 dark:border-slate-700/30">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">Study Time Today</span>
            </div>
            <div className="p-1.5 bg-slate-100 dark:bg-slate-800 rounded-lg">
              <Clock className="h-4 w-4 text-slate-600 dark:text-slate-400" />
            </div>
          </div>
          <div className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-blue-900 dark:from-slate-100 dark:to-blue-100 bg-clip-text text-transparent mb-2">
            4h 32m
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <TrendingUp className="h-3 w-3 text-green-600 dark:text-green-400" />
              <span className="text-xs font-medium text-green-700 dark:text-green-400">+18%</span>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">from yesterday</span>
          </div>
        </div>

        {/* Enhanced Subject Breakdown */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4 flex items-center gap-2">
            <Activity className="h-4 w-4 text-slate-500" />
            Subject Breakdown
          </h4>
          <div className="space-y-4">
            {/* Subject 1 - Mathematics */}
            <div className="p-3 bg-white/60 dark:bg-gray-700/30 rounded-xl border border-blue-200/30 dark:border-blue-800/30">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Mathematics</span>
                </div>
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">1h 45m</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-2.5 rounded-full shadow-sm transition-all duration-500" style={{ width: '38%' }}></div>
              </div>
            </div>

            {/* Subject 2 - Physics */}
            <div className="p-3 bg-white/60 dark:bg-gray-700/30 rounded-xl border border-green-200/30 dark:border-green-800/30">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-gradient-to-br from-green-400 to-green-600 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Physics</span>
                </div>
                <span className="text-sm font-bold text-green-600 dark:text-green-400">1h 30m</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                <div className="bg-gradient-to-r from-green-400 to-green-600 h-2.5 rounded-full shadow-sm transition-all duration-500" style={{ width: '33%' }}></div>
              </div>
            </div>

            {/* Subject 3 - Chemistry */}
            <div className="p-3 bg-white/60 dark:bg-gray-700/30 rounded-xl border border-purple-200/30 dark:border-purple-800/30">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full shadow-sm"></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Chemistry</span>
                </div>
                <span className="text-sm font-bold text-purple-600 dark:text-purple-400">1h 17m</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                <div className="bg-gradient-to-r from-purple-400 to-purple-600 h-2.5 rounded-full shadow-sm transition-all duration-500" style={{ width: '29%' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Quick Stats Grid */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="p-4 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 rounded-xl border border-blue-200/50 dark:border-blue-800/30 hover:shadow-lg transition-all duration-200 hover:scale-105">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-blue-500 rounded-lg">
                <BookOpen className="h-3 w-3 text-white" />
              </div>
              <span className="text-xs font-semibold text-blue-700 dark:text-blue-400">Chapters</span>
            </div>
            <p className="text-xl font-bold text-blue-900 dark:text-blue-100 mb-1">24/30</p>
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-blue-200 dark:bg-blue-800 rounded-full h-1.5">
                <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '80%' }}></div>
              </div>
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">80%</span>
            </div>
          </div>

          <div className="p-4 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 rounded-xl border border-green-200/50 dark:border-green-800/30 hover:shadow-lg transition-all duration-200 hover:scale-105">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-green-500 rounded-lg">
                <CheckCircle className="h-3 w-3 text-white" />
              </div>
              <span className="text-xs font-semibold text-green-700 dark:text-green-400">Tasks</span>
            </div>
            <p className="text-xl font-bold text-green-900 dark:text-green-100 mb-1">8/12</p>
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-green-200 dark:bg-green-800 rounded-full h-1.5">
                <div className="bg-green-500 h-1.5 rounded-full" style={{ width: '67%' }}></div>
              </div>
              <span className="text-xs text-green-600 dark:text-green-400 font-medium">67%</span>
            </div>
          </div>
        </div>

        {/* View Details Button */}
        <button className="w-full px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
          <Activity className="h-4 w-4" />
          View Detailed Analytics
        </button>
      </CardContent>
    </Card>
  );
};
