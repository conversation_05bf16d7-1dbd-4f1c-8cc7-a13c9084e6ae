import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Calendar } from '../ui/calendar';
import { Progress } from '../ui/progress';
import {
  Timer,
  AlertCircle,
  Plus,
  Calendar as CalendarIcon,
  Target,
  Zap,
  ChevronDown,
  ChevronUp,
  Edit,
  Trash2,
  MoreH<PERSON>zontal,
  Clock,
  TrendingUp,
  Star,
  Flame,
  Eye,
  EyeOff
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { dDayStorage, dDayIntegration, DDayExam } from '@/utils/mockTestLocalStorage';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { toast } from '../ui/use-toast';

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
}

export const DDayCountdownWidget: React.FC = () => {
  const { user } = useSupabaseAuth();
  const [dDayExams, setDDayExams] = useState<DDayExam[]>([]);
  const [timeLeft, setTimeLeft] = useState<Record<string, TimeLeft>>({});
  const [closestExam, setClosestExam] = useState<DDayExam | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isAddGoalOpen, setIsAddGoalOpen] = useState(false);
  const [isCompactView, setIsCompactView] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Add goal form state
  const [goalFormData, setGoalFormData] = useState({
    name: "",
    description: "",
    date: new Date(),
    time: "09:00",
    goalType: "personal" as const,
    priority: "medium" as DDayExam['priority'],
  });

  // Load D-Day exams on component mount
  useEffect(() => {
    if (user?.id) {
      loadDDayExams();
    }
  }, [user?.id]);

  const loadDDayExams = () => {
    if (!user?.id) return;

    try {
      // Get upcoming D-Day exams including synced from upcoming tests
      const exams = dDayIntegration.getUpcomingForDDay(user.id, 365);
      setDDayExams(exams);
    } catch (error) {
      console.error('Error loading D-Day exams:', error);
    }
  };

  // Calculate time left for all exams
  const calculateTimeLeft = () => {
    const now = new Date();
    const updatedTimeLeft: Record<string, TimeLeft> = {};
    let closest: { exam: DDayExam; totalSeconds: number } | null = null;

    dDayExams.forEach(exam => {
      const examDateTime = new Date(exam.date);
      const [hours, minutes] = exam.time.split(':').map(Number);
      examDateTime.setHours(hours, minutes, 0, 0);

      const diff = examDateTime.getTime() - now.getTime();

      if (diff > 0) {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        const totalSeconds = diff / 1000;

        updatedTimeLeft[exam.id] = { days, hours, minutes, seconds, totalSeconds };

        if (!closest || totalSeconds < closest.totalSeconds) {
          closest = { exam, totalSeconds };
        }
      }
    });

    setTimeLeft(updatedTimeLeft);
    setClosestExam(closest?.exam || null);
  };

  // Update countdown every second
  useEffect(() => {
    calculateTimeLeft();
    intervalRef.current = setInterval(calculateTimeLeft, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [dDayExams]);

  // Get urgency color based on days left - simplified palette
  const getUrgencyColor = (days: number) => {
    if (days <= 1) return 'text-red-600 dark:text-red-400';
    if (days <= 7) return 'text-orange-600 dark:text-orange-400';
    if (days <= 30) return 'text-blue-600 dark:text-blue-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  // Get urgency indicator - clean styling
  const getUrgencyIndicator = (days: number) => {
    if (days <= 1) return { 
      color: 'text-red-600', 
      bgColor: 'bg-red-50 dark:bg-red-900/10',
      borderColor: 'border-red-200 dark:border-red-800/50',
      icon: AlertCircle, 
      pulse: false, 
      label: 'CRITICAL'
    };
    if (days <= 3) return { 
      color: 'text-orange-600', 
      bgColor: 'bg-orange-50 dark:bg-orange-900/10',
      borderColor: 'border-orange-200 dark:border-orange-800/50',
      icon: Zap, 
      pulse: false, 
      label: 'URGENT'
    };
    if (days <= 7) return { 
      color: 'text-blue-600', 
      bgColor: 'bg-blue-50 dark:bg-blue-900/10',
      borderColor: 'border-blue-200 dark:border-blue-800/50',
      icon: Target, 
      pulse: false, 
      label: 'SOON'
    };
    return { 
      color: 'text-gray-600', 
      bgColor: 'bg-gray-50 dark:bg-gray-900/10',
      borderColor: 'border-gray-200 dark:border-gray-800/50',
      icon: CalendarIcon, 
      pulse: false, 
      label: 'UPCOMING'
    };
  };

  // Get priority badge styling - simplified
  const getPriorityBadge = (priority: DDayExam['priority']) => {
    switch (priority) {
      case 'critical':
        return { color: 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400', icon: Flame, label: 'Critical' };
      case 'high':
        return { color: 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400', icon: Star, label: 'High' };
      case 'medium':
        return { color: 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400', icon: Target, label: 'Medium' };
      case 'low':
        return { color: 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400', icon: Clock, label: 'Low' };
      default:
        return { color: 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400', icon: Clock, label: 'Medium' };
    }
  };

  // Calculate progress percentage (0-100) based on time remaining
  const getProgressPercentage = (exam: DDayExam, timeLeft: TimeLeft) => {
    const examDate = new Date(exam.date);
    const [hours, minutes] = exam.time.split(':').map(Number);
    examDate.setHours(hours, minutes, 0, 0);
    
    const now = new Date();
    const totalTime = examDate.getTime() - now.getTime();
    const oneWeek = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    
    if (totalTime <= 0) return 100;
    if (totalTime >= oneWeek) return 0;
    
    return Math.max(0, Math.min(100, ((oneWeek - totalTime) / oneWeek) * 100));
  };

  // Handle adding new goal
  const handleAddGoal = async () => {
    if (!user?.id || !goalFormData.name.trim()) {
      toast({
        title: "Error",
        description: "Goal name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      const newGoal: DDayExam = {
        id: `goal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: goalFormData.name.trim(),
        description: goalFormData.description || undefined,
        date: format(goalFormData.date, "yyyy-MM-dd"),
        time: goalFormData.time,
        userId: user.id,
        status: 'upcoming',
        priority: goalFormData.priority,
        reminderSettings: {
          enabled: true,
          intervals: [7, 3, 1],
        },
        preparationData: {
          chapters: [],
          totalTopics: [],
        },
        syncedFromUpcoming: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dDayStorage.save(user.id, newGoal);
      loadDDayExams();
      setIsAddGoalOpen(false);

      // Reset form
      setGoalFormData({
        name: "",
        description: "",
        date: new Date(),
        time: "09:00",
        goalType: "personal",
        priority: "medium",
      });

      toast({
        title: "Success",
        description: "Goal added successfully",
      });
    } catch (error) {
      console.error('Error adding goal:', error);
      toast({
        title: "Error",
        description: "Failed to add goal",
        variant: "destructive",
      });
    }
  };

  // Handle editing goal
  const handleEditGoal = (exam: DDayExam) => {
    // For now, just show a toast - full edit functionality can be added later
    toast({
      title: "Edit Goal",
      description: "Edit functionality coming soon",
    });
  };

  // Handle deleting goal
  const handleDeleteGoal = (goalId: string) => {
    if (!user?.id) return;

    try {
      dDayStorage.delete(user.id, goalId);
      loadDDayExams();

      toast({
        title: "Success",
        description: "Goal deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting goal:', error);
      toast({
        title: "Error",
        description: "Failed to delete goal",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-blue-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl shadow-lg">
              <Timer className="h-6 w-6 text-white" />
            </div>
            <div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 dark:from-blue-300 dark:to-indigo-300 bg-clip-text text-transparent">
                D-Day Countdown
              </span>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {dDayExams.length > 0 ? `${dDayExams.length} active goals • Next in ${closestExam && timeLeft[closestExam.id] ? timeLeft[closestExam.id].days : 0} days` : 'Track your most important deadlines'}
              </p>
            </div>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCompactView(!isCompactView)}
              className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-xl transition-all duration-200"
            >
              {isCompactView ? <Eye className="h-4 w-4 text-blue-600" /> : <EyeOff className="h-4 w-4 text-blue-600" />}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pb-6 space-y-4">
        {/* No Goals State */}
        {dDayExams.length === 0 ? (
          <div className="text-center py-8">
            <div className="p-6 rounded-full bg-gray-100 dark:bg-gray-800 mb-4 mx-auto w-fit">
              <CalendarIcon className="h-8 w-8 text-gray-500 dark:text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
              No D-Day Goals Set
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Add your first goal to start tracking important deadlines
            </p>
          </div>
        ) : (
          <>
            {/* Main Countdown Display */}
            {closestExam && timeLeft[closestExam.id] && (
              <div className="space-y-4">
                {/* Priority and Urgency Badges */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {(() => {
                      const priority = getPriorityBadge(closestExam.priority);
                      const PriorityIcon = priority.icon;
                      return (
                        <Badge className={cn("text-xs font-medium", priority.color)}>
                          <PriorityIcon className="h-3 w-3 mr-1" />
                          {priority.label}
                        </Badge>
                      );
                    })()}
                    {(() => {
                      const urgency = getUrgencyIndicator(timeLeft[closestExam.id].days);
                      const UrgencyIcon = urgency.icon;
                      return (
                        <Badge variant="outline" className={cn("text-xs", urgency.color, urgency.borderColor)}>
                          <UrgencyIcon className="h-3 w-3 mr-1" />
                          {urgency.label}
                        </Badge>
                      );
                    })()}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {format(new Date(closestExam.date), 'MMM d')} • {closestExam.time}
                  </div>
                </div>

                {/* Goal Title */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 truncate">
                    {closestExam.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {format(new Date(closestExam.date), 'EEEE, MMMM d, yyyy')}
                  </p>
                </div>

                {/* Unified Countdown Display */}
                <div className="text-center py-4">
                  <div className={cn(
                    "text-4xl md:text-5xl font-bold mb-2 transition-colors duration-300",
                    getUrgencyColor(timeLeft[closestExam.id].days)
                  )}>
                    {timeLeft[closestExam.id].days}
                    <span className="text-lg font-normal text-gray-500 dark:text-gray-400 ml-1">days</span>
                    <span className="text-2xl font-semibold mx-2">:</span>
                    {timeLeft[closestExam.id].hours.toString().padStart(2, '0')}
                    <span className="text-lg font-normal text-gray-500 dark:text-gray-400 ml-1">h</span>
                    <span className="text-2xl font-semibold mx-2">:</span>
                    {timeLeft[closestExam.id].minutes.toString().padStart(2, '0')}
                    <span className="text-lg font-normal text-gray-500 dark:text-gray-400 ml-1">m</span>
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {timeLeft[closestExam.id].seconds.toString().padStart(2, '0')} seconds remaining
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Time Progress</span>
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                      {Math.round(getProgressPercentage(closestExam, timeLeft[closestExam.id]))}%
                    </span>
                  </div>
                  <Progress 
                    value={getProgressPercentage(closestExam, timeLeft[closestExam.id])} 
                    className="h-2"
                  />
                </div>

                {/* Quick Stats */}
                {!isCompactView && (
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Total Hours</div>
                      <div className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                        {Math.round(timeLeft[closestExam.id].totalSeconds / 3600)}h
                      </div>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Weekdays</div>
                      <div className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                        {Math.floor(timeLeft[closestExam.id].days * 5/7)}
                      </div>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Weeks</div>
                      <div className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                        {Math.floor(timeLeft[closestExam.id].days / 7)}w
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Additional Goals */}
            {dDayExams.length > 1 && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Other Goals ({dDayExams.length - 1})
                  </h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </div>

                <AnimatePresence>
                  {(isExpanded ? dDayExams.slice(1, 5) : dDayExams.slice(1, 2)).map((exam) => {
                    const examTimeLeft = timeLeft[exam.id];
                    if (!examTimeLeft) return null;

                    const urgency = getUrgencyIndicator(examTimeLeft.days);
                    const priority = getPriorityBadge(exam.priority);
                    const PriorityIcon = priority.icon;

                    return (
                      <motion.div
                        key={exam.id}
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className={cn(
                          "p-3 rounded-lg border transition-all duration-200 hover:shadow-sm group",
                          urgency.bgColor,
                          urgency.borderColor
                        )}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            <PriorityIcon className={cn("h-4 w-4 flex-shrink-0", urgency.color)} />
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                                {exam.name}
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                {format(new Date(exam.date), 'MMM d')} • {exam.time}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="text-right">
                              <div className={cn("text-sm font-semibold", urgency.color)}>
                                {examTimeLeft.days}d {examTimeLeft.hours}h
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {examTimeLeft.minutes}m left
                              </div>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem onClick={() => handleEditGoal(exam)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDeleteGoal(exam.id)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                        
                        {/* Mini Progress Bar */}
                        <div className="mt-2">
                          <Progress 
                            value={getProgressPercentage(exam, examTimeLeft)} 
                            className="h-1"
                          />
                        </div>
                      </motion.div>
                    );
                  })}
                </AnimatePresence>
              </div>
            )}
          </>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Dialog open={isAddGoalOpen} onOpenChange={setIsAddGoalOpen}>
            <DialogTrigger asChild>
              <Button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium transition-colors duration-200">
                <Plus className="h-4 w-4 mr-2" />
                Add Goal
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  Add New D-Day Goal
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Goal Name *
                  </label>
                  <Input
                    value={goalFormData.name}
                    onChange={(e) => setGoalFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter goal name"
                    className="mt-1"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description
                  </label>
                  <Textarea
                    value={goalFormData.description}
                    onChange={(e) => setGoalFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Optional description"
                    className="mt-1"
                    rows={2}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Target Date *
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal mt-1"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {format(goalFormData.date, "MMM d, yyyy")}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={goalFormData.date}
                          onSelect={(date) => date && setGoalFormData(prev => ({ ...prev, date }))}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Time
                    </label>
                    <Input
                      type="time"
                      value={goalFormData.time}
                      onChange={(e) => setGoalFormData(prev => ({ ...prev, time: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Priority
                  </label>
                  <Select
                    value={goalFormData.priority}
                    onValueChange={(value: DDayExam['priority']) =>
                      setGoalFormData(prev => ({ ...prev, priority: value }))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low Priority</SelectItem>
                      <SelectItem value="medium">Medium Priority</SelectItem>
                      <SelectItem value="high">High Priority</SelectItem>
                      <SelectItem value="critical">Critical Priority</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button
                    onClick={handleAddGoal}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    <Target className="h-4 w-4 mr-2" />
                    Add Goal
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsAddGoalOpen(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {dDayExams.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="px-3 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              {isExpanded ? 'Less' : 'More'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
