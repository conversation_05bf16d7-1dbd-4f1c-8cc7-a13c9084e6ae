import React from 'react';
import { TodaysTasksWidget } from './TodaysTasksWidget';
import { UpcomingExamsWidget } from './UpcomingExamsWidget';
import { SWOTAnalysisWidget } from './SWOTAnalysisWidget';
import { PrepInsightBoardWidget } from './PrepInsightBoardWidget';
import { AnalyticsOverviewWidget } from './AnalyticsOverviewWidget';
import { DDayCountdownWidget } from './DDayCountdownWidget';

interface DashboardGridProps {
  userId: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({ userId }) => {
  return (
    <div className="space-y-8">
      {/* Enhanced Welcome Section */}
      <div className="relative mb-12 p-8 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-700/30 shadow-lg">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-400/10 to-pink-500/10 rounded-full blur-2xl"></div>
        
        <div className="relative z-10">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent mb-4">
            Welcome to your Dashboard
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl">
            Track your academic journey with intelligent insights, personalized analytics, and seamless task management
          </p>
          
          {/* Quick stats bar */}
          <div className="flex items-center gap-6 mt-6 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-gray-600 dark:text-gray-400">All systems operational</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">6 widgets active</span>
            </div>
          </div>
        </div>
      </div>

      {/* 2x3 Dashboard Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Row 1: Top Row */}
        {/* Today's Tasks Widget - Top Left */}
        <div className="w-full">
          <TodaysTasksWidget />
        </div>

        {/* Upcoming Exams Widget - Top Right */}
        <div className="w-full">
          <UpcomingExamsWidget />
        </div>

        {/* Row 2: Middle Row */}
        {/* SWOT Analysis Widget - Middle Left */}
        <div className="w-full">
          <SWOTAnalysisWidget />
        </div>

        {/* Prep Insight Board Widget - Middle Right */}
        <div className="w-full">
          <PrepInsightBoardWidget />
        </div>

        {/* Row 3: Bottom Row */}
        {/* Analytics Overview Widget - Bottom Left */}
        <div className="w-full">
          <AnalyticsOverviewWidget />
        </div>

        {/* Final D-Day Countdown Timer - Bottom Right */}
        <div className="w-full">
          <DDayCountdownWidget />
        </div>
      </div>
    </div>
  );
};