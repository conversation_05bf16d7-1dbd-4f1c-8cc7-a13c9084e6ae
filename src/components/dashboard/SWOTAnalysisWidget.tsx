import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '../ui/card';
import { <PERSON>, Lightbulb, TrendingUp, Al<PERSON>Triangle, Shield } from 'lucide-react';

export const SWOTAnalysisWidget: React.FC = () => {
  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-violet-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-violet-600 to-purple-600 rounded-xl shadow-lg">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <span className="text-xl font-bold bg-gradient-to-r from-violet-700 to-purple-700 dark:from-violet-300 dark:to-purple-300 bg-clip-text text-transparent">
              SWOT Analysis
            </span>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              AI-powered self-assessment and study pattern analysis
            </p>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-6">
        {/* Enhanced SWOT Grid */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          {/* Strengths */}
          <div className="group p-4 rounded-xl bg-gradient-to-br from-green-50 to-emerald-50/50 dark:from-green-900/20 dark:to-emerald-900/10 border border-green-200/50 dark:border-green-800/30 hover:border-green-300 dark:hover:border-green-700 hover:shadow-lg transition-all duration-300 cursor-pointer hover:scale-105">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-1.5 bg-green-500 rounded-lg group-hover:scale-110 transition-transform duration-200">
                <TrendingUp className="h-3 w-3 text-white" />
              </div>
              <span className="text-sm font-semibold text-green-700 dark:text-green-400">Strengths</span>
            </div>
            <p className="text-xs text-green-600 dark:text-green-500 leading-relaxed">
              What are you good at in your studies?
            </p>
            <div className="mt-2 text-xs text-green-500 dark:text-green-400 opacity-0 group-hover:opacity-100 transition-opacity">
              Click to add insights
            </div>
          </div>

          {/* Weaknesses */}
          <div className="group p-4 rounded-xl bg-gradient-to-br from-red-50 to-rose-50/50 dark:from-red-900/20 dark:to-rose-900/10 border border-red-200/50 dark:border-red-800/30 hover:border-red-300 dark:hover:border-red-700 hover:shadow-lg transition-all duration-300 cursor-pointer hover:scale-105">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-1.5 bg-red-500 rounded-lg group-hover:scale-110 transition-transform duration-200">
                <AlertTriangle className="h-3 w-3 text-white" />
              </div>
              <span className="text-sm font-semibold text-red-700 dark:text-red-400">Weaknesses</span>
            </div>
            <p className="text-xs text-red-600 dark:text-red-500 leading-relaxed">
              What areas need improvement?
            </p>
            <div className="mt-2 text-xs text-red-500 dark:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity">
              Click to add insights
            </div>
          </div>

          {/* Opportunities */}
          <div className="group p-4 rounded-xl bg-gradient-to-br from-blue-50 to-sky-50/50 dark:from-blue-900/20 dark:to-sky-900/10 border border-blue-200/50 dark:border-blue-800/30 hover:border-blue-300 dark:hover:border-blue-700 hover:shadow-lg transition-all duration-300 cursor-pointer hover:scale-105">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-1.5 bg-blue-500 rounded-lg group-hover:scale-110 transition-transform duration-200">
                <Lightbulb className="h-3 w-3 text-white" />
              </div>
              <span className="text-sm font-semibold text-blue-700 dark:text-blue-400">Opportunities</span>
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-500 leading-relaxed">
              What opportunities can you leverage?
            </p>
            <div className="mt-2 text-xs text-blue-500 dark:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity">
              Click to add insights
            </div>
          </div>

          {/* Threats */}
          <div className="group p-4 rounded-xl bg-gradient-to-br from-amber-50 to-yellow-50/50 dark:from-amber-900/20 dark:to-yellow-900/10 border border-amber-200/50 dark:border-amber-800/30 hover:border-amber-300 dark:hover:border-amber-700 hover:shadow-lg transition-all duration-300 cursor-pointer hover:scale-105">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-1.5 bg-amber-500 rounded-lg group-hover:scale-110 transition-transform duration-200">
                <Shield className="h-3 w-3 text-white" />
              </div>
              <span className="text-sm font-semibold text-amber-700 dark:text-amber-400">Threats</span>
            </div>
            <p className="text-xs text-amber-600 dark:text-amber-500 leading-relaxed">
              What challenges might you face?
            </p>
            <div className="mt-2 text-xs text-amber-500 dark:text-amber-400 opacity-0 group-hover:opacity-100 transition-opacity">
              Click to add insights
            </div>
          </div>
        </div>

        {/* Enhanced AI Analysis Section */}
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-2">
            <div className="w-2 h-2 bg-violet-500 rounded-full animate-pulse"></div>
            Click quadrants to add your analysis
          </div>
          <button className="px-4 py-2 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white text-sm font-medium rounded-xl transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl hover:scale-105">
            <Brain className="h-4 w-4" />
            AI Analyze
          </button>
        </div>

        {/* Enhanced AI Insights Placeholder */}
        <div className="p-4 bg-gradient-to-br from-violet-50 to-purple-50/50 dark:from-violet-900/20 dark:to-purple-900/10 rounded-xl border border-violet-200/50 dark:border-violet-800/30">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-1.5 bg-gradient-to-br from-violet-500 to-purple-500 rounded-lg">
              <Lightbulb className="h-4 w-4 text-white" />
            </div>
            <span className="text-sm font-semibold text-violet-700 dark:text-violet-400">AI Insights</span>
          </div>
          <p className="text-xs text-violet-600 dark:text-violet-500 leading-relaxed mb-3">
            Complete your SWOT analysis to receive AI-powered insights about your study methods and GRIT assessment.
          </p>
          <div className="flex items-center gap-2 text-xs text-violet-500 dark:text-violet-400">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-violet-400 rounded-full animate-bounce"></div>
              <div className="w-1 h-1 bg-violet-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-1 h-1 bg-violet-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span>AI analysis ready when you complete the quadrants</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
