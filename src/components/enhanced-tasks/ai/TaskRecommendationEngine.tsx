import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseSubjectStore } from '@/stores/supabaseSubjectStore';
import { EnhancedTodoItem } from '@/types/todo';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  Clock,
  TrendingUp,
  AlertTriangle,
  Target,
  Lightbulb,
  Calendar,
  BookOpen,
  Zap,
  ChevronRight,
  X,
} from 'lucide-react';

interface Recommendation {
  id: string;
  type: 'priority' | 'schedule' | 'study_pattern' | 'deadline' | 'productivity';
  title: string;
  description: string;
  action: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  taskIds?: string[];
  icon: React.ComponentType<any>;
  color: string;
}

interface StudyPattern {
  mostProductiveHours: number[];
  averageSessionLength: number;
  preferredSubjects: string[];
  completionRate: number;
  procrastinationTendency: number;
}

export function TaskRecommendationEngine() {
  const { board, getFilteredTasks, addTask, updateTask } = useEnhancedTodoStore();
  const { subjects: userSubjects } = useSupabaseSubjectStore();
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Analyze user study patterns
  const studyPattern = useMemo((): StudyPattern => {
    const allTasks = Object.values(board.tasks);
    const completedTasks = allTasks.filter(task => task.completionPercentage === 100);
    const totalTasks = allTasks.length;

    // Analyze completion times to find productive hours
    const completionHours = completedTasks
      .filter(task => task.updatedAt)
      .map(task => new Date(task.updatedAt!).getHours());

    const hourCounts = completionHours.reduce((acc, hour) => {
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const mostProductiveHours = Object.entries(hourCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour));

    // Calculate average session length
    const averageSessionLength = completedTasks
      .filter(task => task.actualTimeSpent)
      .reduce((sum, task) => sum + (task.actualTimeSpent || 0), 0) / (completedTasks.length || 1) || 30;

    // Find preferred subjects
    const subjectCounts = completedTasks.reduce((acc, task) => {
      if (task.subjectId) {
        acc[task.subjectId] = (acc[task.subjectId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const preferredSubjects = Object.entries(subjectCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([subjectId]) => userSubjects.find(s => s.id === subjectId)?.name || subjectId);

    // Calculate completion rate and procrastination tendency
    const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
    
    const overdueTasks = allTasks.filter(task => 
      task.dueDate && task.dueDate < Date.now() && task.completionPercentage < 100
    ).length;
    
    const procrastinationTendency = totalTasks > 0 ? (overdueTasks / totalTasks) * 100 : 0;

    return {
      mostProductiveHours,
      averageSessionLength,
      preferredSubjects,
      completionRate,
      procrastinationTendency,
    };
  }, [board.tasks, userSubjects]);

  // Generate AI recommendations
  const generateRecommendations = () => {
    setIsAnalyzing(true);
    
    setTimeout(() => {
      const newRecommendations: Recommendation[] = [];
      const allTasks = Object.values(board.tasks);

      // Priority-based recommendations
      const highPriorityTasks = allTasks.filter(
        task => task.priority === 'high' && task.completionPercentage < 100
      );
      
      if (highPriorityTasks.length > 3) {
        newRecommendations.push({
          id: 'focus_high_priority',
          type: 'priority',
          title: 'Focus on High Priority Tasks',
          description: `You have ${highPriorityTasks.length} high-priority tasks pending. Consider tackling 2-3 today.`,
          action: 'Review high-priority tasks',
          confidence: 85,
          impact: 'high',
          taskIds: highPriorityTasks.slice(0, 3).map(t => t.id),
          icon: Target,
          color: 'text-red-500',
        });
      }

      // Deadline-based recommendations
      const upcomingDeadlines = allTasks.filter(task => {
        if (!task.dueDate || task.completionPercentage === 100) return false;
        const daysUntilDue = Math.ceil(
          (task.dueDate - Date.now()) / (1000 * 60 * 60 * 24)
        );
        return daysUntilDue <= 3 && daysUntilDue > 0;
      });

      if (upcomingDeadlines.length > 0) {
        newRecommendations.push({
          id: 'upcoming_deadlines',
          type: 'deadline',
          title: 'Urgent Deadlines Approaching',
          description: `${upcomingDeadlines.length} tasks due within 3 days. Plan your schedule accordingly.`,
          action: 'Schedule deadline tasks',
          confidence: 95,
          impact: 'high',
          taskIds: upcomingDeadlines.map(t => t.id),
          icon: AlertTriangle,
          color: 'text-orange-500',
        });
      }

      // Productivity pattern recommendations
      if (studyPattern.mostProductiveHours.length > 0) {
        const currentHour = new Date().getHours();
        const isProductiveTime = studyPattern.mostProductiveHours.includes(currentHour);
        
        if (isProductiveTime) {
          newRecommendations.push({
            id: 'productive_time',
            type: 'productivity',
            title: 'Peak Productivity Time',
            description: `You're most productive around ${currentHour}:00. Perfect time for challenging tasks!`,
            action: 'Start a difficult task',
            confidence: 80,
            impact: 'medium',
            icon: Zap,
            color: 'text-yellow-500',
          });
        }
      }

      // Study pattern recommendations
      if (studyPattern.procrastinationTendency > 30) {
        newRecommendations.push({
          id: 'break_procrastination',
          type: 'study_pattern',
          title: 'Break the Procrastination Cycle',
          description: 'Try the 2-minute rule: start with tasks that take less than 2 minutes.',
          action: 'Find quick wins',
          confidence: 70,
          impact: 'medium',
          icon: TrendingUp,
          color: 'text-blue-500',
        });
      }

      // Subject balance recommendations
      const subjectTaskCounts = userSubjects.map(subject => ({
        subject,
        count: allTasks.filter(task => task.subjectId === subject.id && task.completionPercentage < 100).length,
      }));

      const imbalancedSubject = subjectTaskCounts.find(s => s.count > 10);
      if (imbalancedSubject) {
        newRecommendations.push({
          id: 'subject_balance',
          type: 'study_pattern',
          title: 'Subject Workload Imbalance',
          description: `${imbalancedSubject.subject.name} has many pending tasks. Consider breaking them into smaller chunks.`,
          action: 'Organize subject tasks',
          confidence: 75,
          impact: 'medium',
          icon: BookOpen,
          color: 'text-purple-500',
        });
      }

      // Schedule optimization recommendations
      const tasksWithoutDueDate = allTasks.filter(
        task => !task.dueDate && task.completionPercentage < 100
      );

      if (tasksWithoutDueDate.length > 5) {
        newRecommendations.push({
          id: 'schedule_tasks',
          type: 'schedule',
          title: 'Schedule Unplanned Tasks',
          description: `${tasksWithoutDueDate.length} tasks don't have due dates. Adding deadlines improves completion rates by 40%.`,
          action: 'Add due dates',
          confidence: 85,
          impact: 'high',
          taskIds: tasksWithoutDueDate.slice(0, 5).map(t => t.id),
          icon: Calendar,
          color: 'text-green-500',
        });
      }

      setRecommendations(newRecommendations);
      setIsAnalyzing(false);
    }, 1500);
  };

  // Auto-generate recommendations on component mount and data changes
  useEffect(() => {
    if (Object.values(board.tasks).length > 0) {
      generateRecommendations();
    }
  }, [Object.values(board.tasks).length]);

  // Handle recommendation actions
  const handleRecommendationAction = (recommendation: Recommendation) => {
    switch (recommendation.type) {
      case 'priority':
        // Focus on high priority tasks - could open a filtered view
        break;
      case 'deadline':
        // Show upcoming deadline tasks
        break;
      case 'schedule':
        // Bulk add due dates to tasks
        if (recommendation.taskIds) {
          recommendation.taskIds.forEach(taskId => {
            const suggestedDate = new Date();
            suggestedDate.setDate(suggestedDate.getDate() + 7); // Default to 1 week
            updateTask(taskId, { dueDate: suggestedDate.getTime() });
          });
        }
        break;
      default:
        break;
    }

    // Remove the recommendation after action
    setRecommendations(prev => prev.filter(r => r.id !== recommendation.id));
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'medium': return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'low': return 'bg-green-500/10 text-green-500 border-green-500/20';
      default: return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  if (!showRecommendations || recommendations.length === 0) {
    return (
      <Button
        variant="outline"
        onClick={() => {
          setShowRecommendations(true);
          generateRecommendations();
        }}
        className="bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800"
      >
        <Brain className="h-4 w-4 mr-2" />
        Get AI Recommendations
      </Button>
    );
  }

  return (
    <Card className="bg-gray-900/50 backdrop-blur-md border-gray-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Brain className="h-5 w-5 text-violet-500" />
            AI Recommendations
            {isAnalyzing && (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Lightbulb className="h-4 w-4 text-yellow-500" />
              </motion.div>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRecommendations(false)}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <AnimatePresence>
          {recommendations.map((recommendation, index) => (
            <motion.div
              key={recommendation.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, x: -300 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 rounded-lg bg-gray-800/50 border border-gray-700 hover:border-gray-600 transition-all"
            >
              <div className="flex items-start gap-3">
                <div className={`p-2 rounded-full bg-gray-800 ${recommendation.color}`}>
                  <recommendation.icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="text-white font-medium">{recommendation.title}</h4>
                    <Badge className={`text-xs ${getImpactColor(recommendation.impact)}`}>
                      {recommendation.impact} impact
                    </Badge>
                  </div>
                  <p className="text-gray-300 text-sm mb-3">{recommendation.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-gray-400">
                      <TrendingUp className="h-3 w-3" />
                      {recommendation.confidence}% confidence
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleRecommendationAction(recommendation)}
                      className="bg-violet-600 hover:bg-violet-700 text-white"
                    >
                      {recommendation.action}
                      <ChevronRight className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {isAnalyzing && (
          <div className="text-center py-4">
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="text-gray-400 text-sm"
            >
              Analyzing your study patterns...
            </motion.div>
          </div>
        )}

        <Button
          variant="outline"
          onClick={generateRecommendations}
          disabled={isAnalyzing}
          className="w-full bg-gray-800/50 border-gray-700 text-white hover:bg-gray-700"
        >
          <Brain className="h-4 w-4 mr-2" />
          Refresh Recommendations
        </Button>
      </CardContent>
    </Card>
  );
}
