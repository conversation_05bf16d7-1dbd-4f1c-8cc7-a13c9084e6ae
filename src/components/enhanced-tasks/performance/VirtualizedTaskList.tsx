import React, { useMemo, useRef, useEffect, useState, useCallback } from 'react';
import { FixedSizeList as List, VariableSizeList, ListChildComponentProps } from 'react-window';
import { motion } from 'framer-motion';
import { EnhancedTodoItem } from '@/types/todo';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseSubjectStore } from '@/stores/supabaseSubjectStore';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  CheckCircle2, 
  Clock, 
  AlertTriangle, 
  Calendar,
  User,
  BookOpen,
  MoreVertical,
} from 'lucide-react';

interface VirtualizedTaskListProps {
  tasks: EnhancedTodoItem[];
  height: number;
  itemHeight?: number;
  overscan?: number;
  onTaskClick?: (task: EnhancedTodoItem) => void;
  onTaskSelect?: (taskId: string, selected: boolean) => void;
  selectedTasks?: Set<string>;
  className?: string;
}

interface TaskItemProps extends ListChildComponentProps {
  data: {
    tasks: EnhancedTodoItem[];
    onTaskClick?: (task: EnhancedTodoItem) => void;
    onTaskSelect?: (taskId: string, selected: boolean) => void;
    selectedTasks?: Set<string>;
  };
}

// Memoized task item component for performance
const TaskItem = React.memo<TaskItemProps>(({ index, style, data }) => {
  const { tasks, onTaskClick, onTaskSelect, selectedTasks } = data;
  const task = tasks[index];
  const { updateTask } = useEnhancedTodoStore();
  const { subjects: userSubjects } = useSupabaseSubjectStore();
  const [isHovered, setIsHovered] = useState(false);

  if (!task) {
    return (
      <div style={style} className="p-2">
        <Skeleton className="h-20 w-full" />
      </div>
    );
  }

  const subject = task.subjectId ? userSubjects.find(s => s.id === task.subjectId) : undefined;
  const isSelected = selectedTasks?.has(task.id) || false;
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.completionPercentage < 100;
  const isCompleted = task.completionPercentage === 100;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-500/5';
      case 'medium': return 'border-l-yellow-500 bg-yellow-500/5';
      case 'low': return 'border-l-green-500 bg-green-500/5';
      default: return 'border-l-gray-500 bg-gray-500/5';
    }
  };

  const handleToggleComplete = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newCompletion = isCompleted ? 0 : 100;
    updateTask(task.id, { completionPercentage: newCompletion });
  };

  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    onTaskSelect?.(task.id, !isSelected);
  };

  return (
    <div style={style} className="p-1">
      <Card
        className={`
          border-l-4 transition-all duration-200 cursor-pointer
          ${getPriorityColor(task.priority)}
          ${isSelected ? 'ring-2 ring-violet-500 bg-violet-500/10' : ''}
          ${isOverdue ? 'border-red-500/50 bg-red-500/5' : ''}
          ${isCompleted ? 'opacity-60' : ''}
          ${isHovered ? 'shadow-md scale-[1.02]' : ''}
          bg-gray-900/50 backdrop-blur-sm border-gray-700
        `}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onTaskClick?.(task)}
      >
        <div className="p-3">
          <div className="flex items-start gap-3">
            {/* Completion checkbox */}
            <button
              onClick={handleToggleComplete}
              className={`
                mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors
                ${isCompleted 
                  ? 'bg-green-500 border-green-500 text-white' 
                  : 'border-gray-400 hover:border-green-500'
                }
              `}
            >
              {isCompleted && <CheckCircle2 className="w-3 h-3" />}
            </button>

            {/* Task content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <h3 className={`font-medium text-sm ${isCompleted ? 'line-through text-gray-500' : 'text-white'}`}>
                  {task.title}
                </h3>
                <div className="flex items-center gap-1">
                  {/* Priority indicator */}
                  {task.priority && (
                    <span className={`
                      px-1.5 py-0.5 text-xs rounded-full font-medium
                      ${task.priority === 'high' ? 'bg-red-500/20 text-red-400' : ''}
                      ${task.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400' : ''}
                      ${task.priority === 'low' ? 'bg-green-500/20 text-green-400' : ''}
                    `}>
                      {task.priority}
                    </span>
                  )}
                  
                  {/* More actions */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle more actions
                    }}
                    className="p-1 rounded hover:bg-gray-700 text-gray-400 hover:text-white"
                  >
                    <MoreVertical className="w-3 h-3" />
                  </button>
                </div>
              </div>

              {/* Description */}
              {task.description && (
                <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                  {task.description}
                </p>
              )}

              {/* Metadata */}
              <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                {/* Subject */}
                {subject && (
                  <div className="flex items-center gap-1">
                    <BookOpen className="w-3 h-3" />
                    <span>{subject.name}</span>
                  </div>
                )}

                {/* Due date */}
                {task.dueDate && (
                  <div className={`flex items-center gap-1 ${isOverdue ? 'text-red-400' : ''}`}>
                    <Calendar className="w-3 h-3" />
                    <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                    {isOverdue && <AlertTriangle className="w-3 h-3" />}
                  </div>
                )}

                {/* Time estimate */}
                {task.timeEstimate && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{task.timeEstimate}m</span>
                  </div>
                )}

                {/* Progress */}
                {task.completionPercentage > 0 && task.completionPercentage < 100 && (
                  <div className="flex items-center gap-1">
                    <div className="w-12 h-1 bg-gray-700 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-violet-500 transition-all duration-300"
                        style={{ width: `${task.completionPercentage}%` }}
                      />
                    </div>
                    <span>{task.completionPercentage}%</span>
                  </div>
                )}
              </div>
            </div>

            {/* Selection checkbox */}
            <button
              onClick={handleSelect}
              className={`
                mt-1 w-4 h-4 rounded border flex items-center justify-center transition-colors
                ${isSelected 
                  ? 'bg-violet-500 border-violet-500' 
                  : 'border-gray-500 hover:border-violet-500'
                }
              `}
            >
              {isSelected && <span className="text-white text-xs">✓</span>}
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
});

TaskItem.displayName = 'TaskItem';

export function VirtualizedTaskList({
  tasks,
  height,
  itemHeight = 100,
  overscan = 5,
  onTaskClick,
  onTaskSelect,
  selectedTasks = new Set(),
  className = '',
}: VirtualizedTaskListProps) {
  const listRef = useRef<List>(null);
  const [isScrolling, setIsScrolling] = useState(false);

  // Memoize the data object to prevent unnecessary re-renders
  const itemData = useMemo(() => ({
    tasks,
    onTaskClick,
    onTaskSelect,
    selectedTasks,
  }), [tasks, onTaskClick, onTaskSelect, selectedTasks]);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    setIsScrolling(true);
    const timeoutId = setTimeout(() => setIsScrolling(false), 150);
    return () => clearTimeout(timeoutId);
  }, []);

  // Auto-scroll to selected task
  const scrollToTask = useCallback((taskId: string) => {
    const index = tasks.findIndex(task => task.id === taskId);
    if (index !== -1 && listRef.current) {
      listRef.current.scrollToItem(index, 'center');
    }
  }, [tasks]);

  // Performance monitoring
  useEffect(() => {
    if (tasks.length > 1000) {
      console.log(`VirtualizedTaskList: Rendering ${tasks.length} tasks with virtualization`);
    }
  }, [tasks.length]);

  return (
    <div className={`relative ${className}`}>
      {/* Scroll indicator */}
      {isScrolling && tasks.length > 20 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute top-2 right-2 z-10 bg-gray-900/90 backdrop-blur-sm border border-gray-700 rounded px-2 py-1 text-xs text-white"
        >
          Scrolling...
        </motion.div>
      )}

      {/* Virtual list */}
      <List
        ref={listRef}
        height={height}
        width="100%"
        itemCount={tasks.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={overscan}
        onScroll={handleScroll}
        className="scrollbar-thin scrollbar-track-gray-800 scrollbar-thumb-gray-600"
      >
        {TaskItem}
      </List>

      {/* Empty state */}
      {tasks.length === 0 && (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <CheckCircle2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No tasks found</p>
          </div>
        </div>
      )}

      {/* Performance info (development only) */}
      {process.env.NODE_ENV === 'development' && tasks.length > 100 && (
        <div className="absolute bottom-2 left-2 text-xs text-gray-500 bg-gray-900/50 px-2 py-1 rounded">
          {tasks.length} tasks (virtualized)
        </div>
      )}
    </div>
  );
}

// Hook for managing virtualized list state
export function useVirtualizedTaskList(tasks: EnhancedTodoItem[]) {
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
  const [scrollToTaskId, setScrollToTaskId] = useState<string | null>(null);

  const handleTaskSelect = useCallback((taskId: string, selected: boolean) => {
    setSelectedTasks(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(taskId);
      } else {
        newSet.delete(taskId);
      }
      return newSet;
    });
  }, []);

  const selectAllTasks = useCallback(() => {
    setSelectedTasks(new Set(tasks.map(task => task.id)));
  }, [tasks]);

  const clearSelection = useCallback(() => {
    setSelectedTasks(new Set());
  }, []);

  const scrollToTask = useCallback((taskId: string) => {
    setScrollToTaskId(taskId);
  }, []);

  return {
    selectedTasks,
    handleTaskSelect,
    selectAllTasks,
    clearSelection,
    scrollToTask,
    scrollToTaskId,
  };
}

// Performance optimized task list with automatic virtualization
interface SmartTaskListProps {
  tasks: EnhancedTodoItem[];
  height: number;
  onTaskClick?: (task: EnhancedTodoItem) => void;
  className?: string;
  virtualizationThreshold?: number;
}

export function SmartTaskList({
  tasks,
  height,
  onTaskClick,
  className = '',
  virtualizationThreshold = 50,
}: SmartTaskListProps) {
  const { selectedTasks, handleTaskSelect } = useVirtualizedTaskList(tasks);

  // Automatically use virtualization for large lists
  const shouldVirtualize = tasks.length > virtualizationThreshold;

  if (shouldVirtualize) {
    return (
      <VirtualizedTaskList
        tasks={tasks}
        height={height}
        onTaskClick={onTaskClick}
        onTaskSelect={handleTaskSelect}
        selectedTasks={selectedTasks}
        className={className}
      />
    );
  }

  // Render normally for small lists
  return (
    <div className={`space-y-2 overflow-y-auto ${className}`} style={{ height }}>
      {tasks.map((task, index) => (
        <TaskItem
          key={task.id}
          index={index}
          style={{}}
          data={{
            tasks,
            onTaskClick,
            onTaskSelect: handleTaskSelect,
            selectedTasks,
          }}
        />
      ))}
    </div>
  );
}
