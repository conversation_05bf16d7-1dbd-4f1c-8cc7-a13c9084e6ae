import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Clock,
  Timer,
  TrendingUp,
  Calendar,
  Target,
  BarChart3,
  Edit3,
  Save,
  X,
  Play,
  Pause,
  Square,
  Star,
  AlertCircle,
  CheckCircle2,
} from 'lucide-react';
import { EnhancedTodoItem } from '@/types/todo';
import { taskTimerIntegration, TaskTimeData } from '@/services/TaskTimerIntegrationService';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { format, formatDistanceToNow } from 'date-fns';

interface TaskTimeTrackingProps {
  task: EnhancedTodoItem;
  compact?: boolean;
  showAnalytics?: boolean;
}

export const TaskTimeTracking: React.FC<TaskTimeTrackingProps> = ({
  task,
  compact = false,
  showAnalytics = false,
}) => {
  const [timeData, setTimeData] = useState<TaskTimeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditingEstimate, setIsEditingEstimate] = useState(false);
  const [newEstimate, setNewEstimate] = useState(task.timeEstimate?.toString() || '');
  const [showSessionHistory, setShowSessionHistory] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { updateTask } = useEnhancedTodoStore();
  const { user } = useSupabaseAuth();

  // Load time tracking data
  useEffect(() => {
    if (user) {
      loadTimeTrackingData();
    }
  }, [task.id, user]);

  const loadTimeTrackingData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);
      const data = await taskTimerIntegration.getTaskTimeTracking(task.id, user.id);
      setTimeData(data);
    } catch (error) {
      console.error('Error loading time tracking data:', error);
      setError('Failed to load time tracking data');
    } finally {
      setIsLoading(false);
    }
  };

  // Format time duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  // Format time estimate
  const formatEstimate = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  // Handle estimate update
  const handleUpdateEstimate = async () => {
    if (!user || !newEstimate) return;

    try {
      const estimateMinutes = parseInt(newEstimate);
      if (isNaN(estimateMinutes) || estimateMinutes <= 0) {
        setError('Please enter a valid time estimate');
        return;
      }

      await taskTimerIntegration.updateTaskTimeEstimate(task.id, user.id, estimateMinutes);
      await updateTask(task.id, { timeEstimate: estimateMinutes });
      setIsEditingEstimate(false);
      await loadTimeTrackingData();
    } catch (error) {
      console.error('Error updating time estimate:', error);
      setError('Failed to update time estimate');
    }
  };

  // Get progress color based on completion
  const getProgressColor = (progress: number): string => {
    if (progress >= 100) return 'bg-emerald-500';
    if (progress >= 75) return 'bg-amber-500';
    if (progress >= 50) return 'bg-blue-500';
    return 'bg-violet-500';
  };

  // Get productivity rating color
  const getProductivityColor = (rating: number): string => {
    if (rating >= 4) return 'text-emerald-500';
    if (rating >= 3) return 'text-blue-500';
    if (rating >= 2) return 'text-amber-500';
    return 'text-red-500';
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-gray-400">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-violet-400" />
        <span className="text-xs">Loading time data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 text-red-400">
        <AlertCircle className="h-4 w-4" />
        <span className="text-xs">{error}</span>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="flex items-center gap-3 text-xs text-gray-400">
        {/* Total time spent */}
        {timeData && timeData.totalTimeSpent > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatDuration(timeData.totalTimeSpent)}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Total time spent on this task</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Time estimate */}
        {task.timeEstimate && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  <span>{formatEstimate(task.timeEstimate)}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Estimated time to complete</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Progress indicator */}
        {timeData && task.timeEstimate && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  <span>{Math.round(timeData.timeProgress)}%</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Progress toward time estimate</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Session history button */}
        {timeData && timeData.sessions.length > 0 && (
          <Dialog open={showSessionHistory} onOpenChange={setShowSessionHistory}>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 px-1 text-xs text-violet-400 hover:text-violet-300"
              >
                <BarChart3 className="h-3 w-3 mr-1" />
                {timeData.sessions.length} sessions
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl bg-[#030303]/95 backdrop-blur-md border-gray-800">
              <DialogHeader>
                <DialogTitle className="text-white">Session History - {task.title}</DialogTitle>
              </DialogHeader>
              <SessionHistoryView task={task} timeData={timeData} />
            </DialogContent>
          </Dialog>
        )}
      </div>
    );
  }

  return (
    <Card className="bg-[#030303]/80 backdrop-blur-md border border-gray-800/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-sm flex items-center gap-2">
          <Timer className="h-4 w-4 text-violet-400" />
          Time Tracking
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Time estimate section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-gray-300 text-xs">Time Estimate</Label>
            {!isEditingEstimate && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-gray-400 hover:text-white"
                onClick={() => {
                  setIsEditingEstimate(true);
                  setNewEstimate(task.timeEstimate?.toString() || '');
                }}
              >
                <Edit3 className="h-3 w-3" />
              </Button>
            )}
          </div>
          
          {isEditingEstimate ? (
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={newEstimate}
                onChange={(e) => setNewEstimate(e.target.value)}
                placeholder="Minutes"
                className="h-8 text-xs bg-gray-800 border-gray-700 text-white"
              />
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-emerald-400 hover:text-emerald-300"
                onClick={handleUpdateEstimate}
              >
                <Save className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-red-400 hover:text-red-300"
                onClick={() => setIsEditingEstimate(false)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <div className="text-sm text-gray-300">
              {task.timeEstimate ? formatEstimate(task.timeEstimate) : 'Not set'}
            </div>
          )}
        </div>

        {/* Time tracking summary */}
        {timeData && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label className="text-gray-400 text-xs">Total Time</Label>
                <div className="text-sm text-white font-medium">
                  {formatDuration(timeData.totalTimeSpent)}
                </div>
              </div>
              <div className="space-y-1">
                <Label className="text-gray-400 text-xs">Sessions</Label>
                <div className="text-sm text-white font-medium">
                  {timeData.sessions.length}
                </div>
              </div>
            </div>

            {/* Progress bar */}
            {task.timeEstimate && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-gray-400 text-xs">Progress</Label>
                  <span className="text-xs text-gray-300">
                    {Math.round(timeData.timeProgress)}%
                  </span>
                </div>
                <Progress
                  value={timeData.timeProgress}
                  className="h-2 bg-gray-800"
                />
                <div className="flex justify-between text-xs text-gray-400">
                  <span>{formatDuration(timeData.totalTimeSpent)}</span>
                  <span>{formatEstimate(task.timeEstimate)}</span>
                </div>
              </div>
            )}

            {/* Analytics summary */}
            {showAnalytics && (
              <div className="grid grid-cols-2 gap-4 pt-2 border-t border-gray-800">
                <div className="space-y-1">
                  <Label className="text-gray-400 text-xs">Avg Session</Label>
                  <div className="text-sm text-white">
                    {formatDuration(timeData.averageSessionLength)}
                  </div>
                </div>
                <div className="space-y-1">
                  <Label className="text-gray-400 text-xs">Productivity</Label>
                  <div className={`text-sm font-medium ${getProductivityColor(timeData.productivityRating)}`}>
                    {timeData.productivityRating > 0 ? (
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        {timeData.productivityRating.toFixed(1)}/5
                      </div>
                    ) : (
                      'Not rated'
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Last worked on */}
            {timeData.lastWorkedOn && (
              <div className="text-xs text-gray-400">
                Last worked on {formatDistanceToNow(timeData.lastWorkedOn, { addSuffix: true })}
              </div>
            )}

            {/* Session history button */}
            {timeData.sessions.length > 0 && (
              <Dialog open={showSessionHistory} onOpenChange={setShowSessionHistory}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Session History ({timeData.sessions.length})
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl bg-[#030303]/95 backdrop-blur-md border-gray-800">
                  <DialogHeader>
                    <DialogTitle className="text-white">Session History - {task.title}</DialogTitle>
                  </DialogHeader>
                  <SessionHistoryView task={task} timeData={timeData} />
                </DialogContent>
              </Dialog>
            )}
          </>
        )}

        {/* No time data message */}
        {!timeData || timeData.totalTimeSpent === 0 && (
          <div className="text-center py-4 text-gray-400">
            <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No time tracked yet</p>
            <p className="text-xs">Start a timer to begin tracking time for this task</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Session History Component
interface SessionHistoryViewProps {
  task: EnhancedTodoItem;
  timeData: TaskTimeData;
}

const SessionHistoryView: React.FC<SessionHistoryViewProps> = ({ task, timeData }) => {
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  const getProductivityColor = (rating: number): string => {
    if (rating >= 4) return 'text-emerald-500';
    if (rating >= 3) return 'text-blue-500';
    if (rating >= 2) return 'text-amber-500';
    return 'text-red-500';
  };

  const sortedSessions = [...timeData.sessions].sort((a, b) => 
    new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
  );

  return (
    <div className="space-y-4 max-h-96 overflow-y-auto">
      {/* Summary stats */}
      <div className="grid grid-cols-3 gap-4 p-4 bg-gray-800/50 rounded-lg">
        <div className="text-center">
          <div className="text-lg font-semibold text-white">
            {formatDuration(timeData.totalTimeSpent)}
          </div>
          <div className="text-xs text-gray-400">Total Time</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-white">
            {formatDuration(timeData.averageSessionLength)}
          </div>
          <div className="text-xs text-gray-400">Avg Session</div>
        </div>
        <div className="text-center">
          <div className={`text-lg font-semibold ${getProductivityColor(timeData.productivityRating)}`}>
            {timeData.productivityRating > 0 ? timeData.productivityRating.toFixed(1) : 'N/A'}
          </div>
          <div className="text-xs text-gray-400">Avg Rating</div>
        </div>
      </div>

      {/* Session list */}
      <div className="space-y-2">
        {sortedSessions.map((session, index) => (
          <motion.div
            key={session.id || index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className="p-3 bg-gray-800/30 rounded-lg border border-gray-700/50"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-violet-500 rounded-full" />
                <span className="text-sm text-white font-medium">
                  {format(new Date(session.start_time), 'MMM d, yyyy')}
                </span>
                <span className="text-xs text-gray-400">
                  {format(new Date(session.start_time), 'h:mm a')}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="text-xs border-violet-500/50 text-violet-400">
                  {formatDuration(session.duration || 0)}
                </Badge>
                {session.productivity_rating > 0 && (
                  <div className={`flex items-center gap-1 text-xs ${getProductivityColor(session.productivity_rating)}`}>
                    <Star className="h-3 w-3" />
                    {session.productivity_rating}
                  </div>
                )}
              </div>
            </div>
            
            {session.notes && (
              <p className="text-sm text-gray-300 mt-2 p-2 bg-gray-900/50 rounded">
                {session.notes}
              </p>
            )}
          </motion.div>
        ))}
      </div>

      {sortedSessions.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>No study sessions recorded yet</p>
        </div>
      )}
    </div>
  );
};