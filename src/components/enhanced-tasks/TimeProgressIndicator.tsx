import React from 'react';
import { motion } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Clock,
  Target,
  TrendingUp,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle2,
  Timer,
} from 'lucide-react';

interface TimeProgressIndicatorProps {
  totalTimeSpent: number; // in seconds
  estimatedTime?: number; // in seconds
  variant?: 'compact' | 'detailed' | 'minimal';
  showLabels?: boolean;
  showPercentage?: boolean;
  animated?: boolean;
  className?: string;
}

export const TimeProgressIndicator: React.FC<TimeProgressIndicatorProps> = ({
  totalTimeSpent,
  estimatedTime,
  variant = 'detailed',
  showLabels = true,
  showPercentage = true,
  animated = true,
  className = '',
}) => {
  // Calculate progress percentage
  const progress = estimatedTime && estimatedTime > 0 
    ? Math.min((totalTimeSpent / estimatedTime) * 100, 100)
    : 0;

  // Format time duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  // Get progress status
  const getProgressStatus = () => {
    if (!estimatedTime) return 'no-estimate';
    if (progress >= 100) return 'completed';
    if (progress >= 90) return 'almost-done';
    if (progress >= 75) return 'on-track';
    if (progress >= 50) return 'halfway';
    return 'started';
  };

  // Get progress color based on status
  const getProgressColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-emerald-500';
      case 'almost-done': return 'bg-emerald-400';
      case 'on-track': return 'bg-blue-500';
      case 'halfway': return 'bg-amber-500';
      case 'started': return 'bg-violet-500';
      default: return 'bg-gray-500';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="h-3 w-3 text-emerald-500" />;
      case 'almost-done': return <TrendingUp className="h-3 w-3 text-emerald-400" />;
      case 'on-track': return <Target className="h-3 w-3 text-blue-500" />;
      case 'halfway': return <Timer className="h-3 w-3 text-amber-500" />;
      case 'started': return <Clock className="h-3 w-3 text-violet-500" />;
      default: return <Clock className="h-3 w-3 text-gray-500" />;
    }
  };

  // Get status message
  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'completed': return 'Time estimate completed!';
      case 'almost-done': return 'Almost done with estimated time';
      case 'on-track': return 'Good progress on time estimate';
      case 'halfway': return 'Halfway through estimated time';
      case 'started': return 'Started working on this task';
      case 'no-estimate': return 'No time estimate set';
      default: return 'Time tracking in progress';
    }
  };

  const status = getProgressStatus();
  const progressColor = getProgressColor(status);
  const statusIcon = getStatusIcon(status);
  const statusMessage = getStatusMessage(status);

  // Minimal variant - just a small indicator
  if (variant === 'minimal') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={`flex items-center gap-1 ${className}`}>
              {statusIcon}
              {totalTimeSpent > 0 && (
                <span className="text-xs text-gray-400">
                  {formatDuration(totalTimeSpent)}
                </span>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p className="font-medium">{statusMessage}</p>
              <p className="text-xs">
                Time spent: {formatDuration(totalTimeSpent)}
                {estimatedTime && ` / ${formatDuration(estimatedTime)}`}
              </p>
              {estimatedTime && (
                <p className="text-xs">Progress: {Math.round(progress)}%</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Compact variant - single line with progress
  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {statusIcon}
        <div className="flex-1 min-w-0">
          {estimatedTime ? (
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-400">
                  {formatDuration(totalTimeSpent)} / {formatDuration(estimatedTime)}
                </span>
                {showPercentage && (
                  <span className="text-gray-300">{Math.round(progress)}%</span>
                )}
              </div>
              <Progress
                value={progress}
                className="h-1.5 bg-gray-800"
                style={{
                  background: 'linear-gradient(90deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.2) 100%)'
                }}
              />
            </div>
          ) : (
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <Clock className="h-3 w-3" />
              <span>{formatDuration(totalTimeSpent)}</span>
              {totalTimeSpent === 0 && <span>No time tracked</span>}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Detailed variant - full display with labels and status
  return (
    <div className={`space-y-3 ${className}`}>
      {/* Status header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {statusIcon}
          {showLabels && (
            <span className="text-sm text-gray-300 font-medium">
              Time Progress
            </span>
          )}
        </div>
        {estimatedTime && showPercentage && (
          <Badge 
            variant="outline" 
            className={`text-xs border-gray-600 ${
              progress >= 100 ? 'text-emerald-400 border-emerald-500/50' :
              progress >= 75 ? 'text-blue-400 border-blue-500/50' :
              progress >= 50 ? 'text-amber-400 border-amber-500/50' :
              'text-violet-400 border-violet-500/50'
            }`}
          >
            {Math.round(progress)}%
          </Badge>
        )}
      </div>

      {/* Progress visualization */}
      {estimatedTime ? (
        <div className="space-y-2">
          <motion.div
            initial={animated ? { width: 0 } : undefined}
            animate={animated ? { width: '100%' } : undefined}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          >
            <Progress
              value={progress}
              className="h-2 bg-gray-800"
            />
          </motion.div>
          
          <div className="flex justify-between items-center text-xs">
            <div className="flex items-center gap-1 text-gray-400">
              <Clock className="h-3 w-3" />
              <span>{formatDuration(totalTimeSpent)}</span>
            </div>
            <div className="flex items-center gap-1 text-gray-400">
              <Target className="h-3 w-3" />
              <span>{formatDuration(estimatedTime)}</span>
            </div>
          </div>

          {/* Status message */}
          <div className="text-xs text-gray-400 text-center">
            {statusMessage}
          </div>

          {/* Overtime warning */}
          {progress > 100 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-2 p-2 bg-amber-500/10 border border-amber-500/30 rounded-md"
            >
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              <div className="text-xs text-amber-400">
                <span className="font-medium">Over estimate by </span>
                {formatDuration(totalTimeSpent - estimatedTime)}
              </div>
            </motion.div>
          )}
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Clock className="h-4 w-4" />
            <span>
              {totalTimeSpent > 0 
                ? `${formatDuration(totalTimeSpent)} tracked`
                : 'No time tracked yet'
              }
            </span>
          </div>
          <div className="text-xs text-gray-500 text-center">
            Set a time estimate to track progress
          </div>
        </div>
      )}
    </div>
  );
};

// Quick time display component for inline use
interface QuickTimeDisplayProps {
  totalTimeSpent: number;
  estimatedTime?: number;
  className?: string;
}

export const QuickTimeDisplay: React.FC<QuickTimeDisplayProps> = ({
  totalTimeSpent,
  estimatedTime,
  className = '',
}) => {
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  const progress = estimatedTime && estimatedTime > 0 
    ? Math.min((totalTimeSpent / estimatedTime) * 100, 100)
    : 0;

  return (
    <div className={`flex items-center gap-2 text-xs ${className}`}>
      {totalTimeSpent > 0 && (
        <div className="flex items-center gap-1 text-gray-400">
          <Clock className="h-3 w-3" />
          <span>{formatDuration(totalTimeSpent)}</span>
        </div>
      )}
      
      {estimatedTime && (
        <div className="flex items-center gap-1 text-gray-400">
          <Target className="h-3 w-3" />
          <span>{formatDuration(estimatedTime)}</span>
          {progress > 0 && (
            <span className="text-violet-400">({Math.round(progress)}%)</span>
          )}
        </div>
      )}
    </div>
  );
};