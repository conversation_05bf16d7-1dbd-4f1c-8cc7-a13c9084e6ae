import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Draggable } from '@hello-pangea/dnd';
import {
  Card,
  CardContent,
  CardHeader,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Calendar,
  ChevronDown,
  ChevronRight,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle2,
  Circle,
  BookOpen,
  Target,
  Timer,
  Eye,
  Play,
  X,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedTodoItem } from '@/types/todo';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useEnhancedTimerStore } from '@/stores/enhancedTimerStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useTimerTaskOperations } from '@/hooks/useTimerSync';
import { MobileSwipeActions } from './MobileSwipeActions';
import { TaskCreationModal } from './TaskCreationModal';
import { TimeProgressIndicator, QuickTimeDisplay } from './TimeProgressIndicator';
import { TaskTimeTracking } from './TaskTimeTracking';
import { TaskAnalyticsDashboard } from './TaskAnalyticsDashboard';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

interface EnhancedTaskCardProps {
  task: EnhancedTodoItem;
  index: number;
  showSubtasks?: boolean;
  depth?: number;
  showTimeTracking?: boolean;
  showAnalytics?: boolean;
}

export const EnhancedTaskCard = React.memo(function EnhancedTaskCard({
  task,
  index,
  showSubtasks = true,
  depth = 0,
  showTimeTracking = true,
  showAnalytics = false
}: EnhancedTaskCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isStartingTimer, setIsStartingTimer] = useState(false);
  const [showTimeDetails, setShowTimeDetails] = useState(false);
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false);

  const {
    updateTask,
    deleteTask,
    selectTask,
    selectedTasks,
    getTaskHierarchy,
    calculateTaskProgress,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const { startTimer, status: timerStatus, linkedTask } = useEnhancedTimerStore();
  const { handleTaskCompletion, handleTaskDeletion, hasActiveTimer, getTaskTimerStatus } = useTimerTaskOperations();

  const isSelected = selectedTasks.includes(task.id);
  const subtasks = showSubtasks ? getTaskHierarchy(task.id) : [];
  const hasSubtasks = subtasks.length > 0;
  const taskProgress = hasSubtasks ? calculateTaskProgress(task.id) : task.completionPercentage;

  // Get subject and exam information - memoize expensive lookups
  const subject = React.useMemo(() =>
    task.subjectId ? subjects[task.subjectId] : undefined,
    [task.subjectId, subjects]
  );

  const exam = React.useMemo(() =>
    task.examId ? presetExams[task.examId] : undefined,
    [task.examId, presetExams]
  );

  // Calculate due date status - memoize expensive date calculations
  const dueDateStatus = React.useMemo(() => {
    if (!task.dueDate) return null;

    const now = new Date();
    const dueDate = new Date(task.dueDate);
    const daysDiff = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff < 0) return 'overdue';
    if (daysDiff === 0) return 'today';
    if (daysDiff <= 3) return 'soon';
    return 'normal';
  }, [task.dueDate]);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/90 text-white';
      case 'medium': return 'bg-amber-500/90 text-white';
      case 'low': return 'bg-green-500/90 text-white';
      default: return 'bg-gray-500/90 text-white';
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'hard': return 'bg-purple-500/20 text-purple-700 border-purple-500/30';
      case 'medium': return 'bg-blue-500/20 text-blue-700 border-blue-500/30';
      case 'easy': return 'bg-emerald-500/20 text-emerald-700 border-emerald-500/30';
      default: return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
    }
  };

  // Get due date color
  const getDueDateColor = () => {
    switch (dueDateStatus) {
      case 'overdue': return 'text-red-600 bg-red-50 border-red-200';
      case 'today': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'soon': return 'text-amber-600 bg-amber-50 border-amber-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Handle task completion toggle
  const handleToggleComplete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const newCompletion = task.completionPercentage === 100 ? 0 : 100;
      console.log('Toggling task completion:', task.id, 'from', task.completionPercentage, 'to', newCompletion);

      // Check if task has active timer and handle accordingly
      if (newCompletion === 100) {
        const canComplete = await handleTaskCompletion(task.id);
        if (!canComplete) {
          return; // User chose not to stop timer, don't complete task
        }
      }

      // Update task immediately for better UX
      await updateTask(task.id, {
        completionPercentage: newCompletion,
        updatedAt: Date.now(),
      });

      console.log('Task completion toggled successfully');
    } catch (error) {
      console.error('Failed to toggle task completion:', error);
      // You could add a toast notification here
    }
  };

  // Handle task selection
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    selectTask(task.id);
  };

  // Handle view count increment
  const handleCardClick = async () => {
    if (!isEditing) {
      await updateTask(task.id, {
        viewCount: (task.viewCount || 0) + 1,
        lastViewed: Date.now(),
      });
      setIsEditing(true);
    }
  };

  // Handle starting timer for this task
  const handleStartTimer = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!user) {
      console.error('User not authenticated');
      return;
    }

    try {
      setIsStartingTimer(true);

      // Check if another timer is already running
      if (timerStatus === 'running' && linkedTask && linkedTask.taskId !== task.id) {
        const confirmStop = window.confirm(
          `A timer is already running for "${linkedTask.taskTitle}". Do you want to stop it and start a new timer for this task?`
        );
        if (!confirmStop) {
          setIsStartingTimer(false);
          return;
        }
      }

      // Start timer for this task
      await startTimer(task.id, user.id);

      // Update task status to "In Progress" (column-2)
      if (task.columnId !== 'column-2') {
        await updateTask(task.id, {
          columnId: 'column-2',
          updatedAt: Date.now(),
        });
      }

      // Navigate to productivity page
      navigate('/productivity', {
        state: {
          taskId: task.id,
          taskTitle: task.title
        }
      });

    } catch (error) {
      console.error('Failed to start timer:', error);
      // You could add a toast notification here
    } finally {
      setIsStartingTimer(false);
    }
  };

  // Animation variants - simplified for better performance
  const cardVariants = React.useMemo(() => ({
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    dragging: {
      scale: 1.02, // Reduced scale for smoother animation
      transition: { duration: 0.1 }
    },
  }), []);

  const subtaskVariants = React.useMemo(() => ({
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: { duration: 0.2, ease: "easeOut" }
    },
  }), []);

  return (
    <>
      <Draggable draggableId={task.id} index={index}>
        {(provided, snapshot) => (
          <motion.div
            ref={provided.innerRef}
            {...provided.draggableProps}
            variants={cardVariants}
            initial="hidden"
            animate={snapshot.isDragging ? "dragging" : "visible"}
            className={`mb-3 ${depth > 0 ? `ml-${Math.min(depth * 4, 16)}` : ''}`}
          >
            <MobileSwipeActions
              onComplete={handleToggleComplete}
              onDelete={async () => {
                const canDelete = await handleTaskDeletion(task.id);
                if (canDelete) {
                  deleteTask(task.id);
                }
              }}
              onEdit={() => setIsEditing(true)}
              onTogglePriority={() => {
                const newPriority = task.priority === 'high' ? 'medium' : 'high';
                updateTask(task.id, { priority: newPriority });
              }}
              isCompleted={task.completionPercentage === 100}
            >
              <Card
                className={`
              relative overflow-hidden transition-all duration-300 cursor-pointer
              bg-[#030303]/80 backdrop-blur-md border border-gray-800/50
              hover:border-violet-500/30 hover:bg-[#030303]/90
              ${isSelected ? 'ring-2 ring-violet-500/50 border-violet-500/50' : ''}
              ${snapshot.isDragging ? 'shadow-2xl shadow-violet-500/20' : 'shadow-lg shadow-black/20'}
              ${dueDateStatus === 'overdue' ? 'border-red-500/50 bg-red-950/20' : ''}
              ${task.completionPercentage === 100 ? 'opacity-75' : ''}
            `}
                onClick={handleCardClick}
              >
                {/* Subject color indicator */}
                {subject && (
                  <div
                    className="absolute top-0 left-0 w-1 h-full"
                    style={{ backgroundColor: subject.color }}
                  />
                )}

                {/* Selection checkbox - Larger touch target for mobile */}
                <div className="absolute top-2 left-2 z-10">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 md:h-6 md:w-6 p-0 hover:bg-violet-500/20 touch-manipulation"
                    onClick={handleSelect}
                  >
                    {isSelected ? (
                      <CheckCircle2 className="h-5 w-5 md:h-4 md:w-4 text-violet-400" />
                    ) : (
                      <Circle className="h-5 w-5 md:h-4 md:w-4 text-gray-400" />
                    )}
                  </Button>
                </div>

                <CardHeader className="pb-3 pt-3 pl-12 pr-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {/* Drag handle */}
                      <div
                        {...provided.dragHandleProps}
                        className="absolute top-2 right-2 p-1 opacity-50 hover:opacity-100 cursor-grab active:cursor-grabbing"
                      >
                        <div className="w-4 h-4 flex flex-col justify-center space-y-0.5">
                          <div className="w-full h-0.5 bg-gray-400 rounded"></div>
                          <div className="w-full h-0.5 bg-gray-400 rounded"></div>
                          <div className="w-full h-0.5 bg-gray-400 rounded"></div>
                        </div>
                      </div>
                      {/* Task title */}
                      <h3 className="font-onest font-semibold text-white text-sm leading-tight mb-2 truncate">
                        {task.title}
                      </h3>

                      {/* Tags and metadata */}
                      <div className="flex flex-wrap gap-1 mb-2">
                        {/* Priority badge */}
                        <Badge className={`text-xs px-2 py-0.5 ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </Badge>

                        {/* Difficulty badge */}
                        <Badge
                          variant="outline"
                          className={`text-xs px-2 py-0.5 border ${getDifficultyColor(task.difficultyLevel)}`}
                        >
                          {task.difficultyLevel}
                        </Badge>

                        {/* Subject badge */}
                        {subject && (
                          <Badge
                            variant="outline"
                            className="text-xs px-2 py-0.5 border-gray-600 text-gray-300"
                            style={{ borderColor: subject.color + '50', color: subject.color }}
                          >
                            <BookOpen className="h-3 w-3 mr-1" />
                            {subject.name}
                          </Badge>
                        )}

                        {/* Exam badge */}
                        {exam && (
                          <Badge
                            variant="outline"
                            className="text-xs px-2 py-0.5 border-rose-500/50 text-rose-400"
                          >
                            <Target className="h-3 w-3 mr-1" />
                            {exam.name}
                          </Badge>
                        )}
                      </div>

                      {/* Tags */}
                      {(task.tags.length > 0 || task.chapterTags.length > 0) && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {task.tags.map((tag, idx) => (
                            <span
                              key={idx}
                              className="text-xs px-2 py-0.5 bg-violet-500/20 text-violet-300 rounded-full"
                            >
                              #{tag}
                            </span>
                          ))}
                          {task.chapterTags.map((tag, idx) => (
                            <span
                              key={idx}
                              className="text-xs px-2 py-0.5 bg-emerald-500/20 text-emerald-300 rounded-full"
                            >
                              📖 {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Actions dropdown */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-violet-500/20"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className="bg-[#030303]/95 backdrop-blur-md border-gray-800"
                      >
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsEditing(true);
                          }}
                          className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                        >
                          Edit Task
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={handleToggleComplete}
                          className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                        >
                          {task.completionPercentage === 100 ? 'Mark Incomplete' : 'Mark Complete'}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowTimeDetails(true);
                          }}
                          className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                        >
                          Time Tracking
                        </DropdownMenuItem>
                        {showAnalytics && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowAnalyticsModal(true);
                            }}
                            className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                          >
                            View Analytics
                          </DropdownMenuItem>
                        )}
                        {hasSubtasks && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              setIsExpanded(!isExpanded);
                            }}
                            className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                          >
                            {isExpanded ? 'Collapse' : 'Expand'} Subtasks
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator className="bg-gray-800" />
                        <DropdownMenuItem
                          onClick={async (e) => {
                            e.stopPropagation();
                            const canDelete = await handleTaskDeletion(task.id);
                            if (canDelete) {
                              deleteTask(task.id);
                            }
                          }}
                          className="text-red-400 hover:text-red-300 hover:bg-red-500/20"
                        >
                          Delete Task
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>

                <CardContent className="pt-0 pb-4 px-4">
                  {/* Description */}
                  {task.description && (
                    <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                      {task.description}
                    </p>
                  )}

                  {/* Progress bar */}
                  <div className="mb-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-gray-400">Progress</span>
                      <span className="text-xs text-gray-300">{taskProgress}%</span>
                    </div>
                    <Progress
                      value={taskProgress}
                      className="h-2 bg-gray-800"
                      style={{
                        background: 'linear-gradient(90deg, rgba(139, 92, 246, 0.3) 0%, rgba(168, 85, 247, 0.3) 100%)'
                      }}
                    />
                  </div>

                  {/* Time tracking display */}
                  {showTimeTracking && (task.actualTimeSpent || task.timeEstimate) && (
                    <div className="mb-3">
                      <TimeProgressIndicator
                        totalTimeSpent={(task.actualTimeSpent || 0) * 60} // Convert minutes to seconds
                        estimatedTime={task.timeEstimate ? task.timeEstimate * 60 : undefined}
                        variant="compact"
                        className="text-xs"
                      />
                    </div>
                  )}

                  {/* Metadata row */}
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <div className="flex items-center gap-3">
                      {/* Due date */}
                      {task.dueDate && (
                        <div className={`flex items-center gap-1 px-2 py-1 rounded-md border ${getDueDateColor()}`}>
                          <Calendar className="h-3 w-3" />
                          <span>
                            {dueDateStatus === 'overdue' ? 'Overdue' :
                              dueDateStatus === 'today' ? 'Today' :
                                format(new Date(task.dueDate), 'MMM d')}
                          </span>
                        </div>
                      )}

                      {/* Time estimate */}
                      {task.timeEstimate && (
                        <div className="flex items-center gap-1">
                          <Timer className="h-3 w-3" />
                          <span>{task.timeEstimate}m</span>
                        </div>
                      )}

                      {/* View count */}
                      {task.viewCount > 0 && (
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          <span>{task.viewCount}</span>
                        </div>
                      )}
                    </div>

                    {/* Start Timer Button with Enhanced Status */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`
                    h-7 px-3 text-xs transition-all duration-300 touch-manipulation
                    ${hasActiveTimer(task.id)
                          ? timerStatus === 'running'
                            ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/50'
                            : 'bg-amber-500/20 text-amber-400 border border-amber-500/50'
                          : 'border border-violet-500/50 text-violet-400 hover:bg-violet-500/20 hover:text-violet-300'
                        }
                    ${isStartingTimer ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                      onClick={handleStartTimer}
                      disabled={isStartingTimer || task.completionPercentage === 100}
                      title={
                        task.completionPercentage === 100
                          ? 'Task is completed'
                          : hasActiveTimer(task.id)
                            ? `Timer is ${timerStatus} for this task`
                            : 'Start timer for this task'
                      }
                    >
                      {isStartingTimer ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-violet-400" />
                      ) : hasActiveTimer(task.id) ? (
                        <>
                          <div className={`w-2 h-2 rounded-full mr-1 ${timerStatus === 'running'
                              ? 'bg-emerald-400 animate-pulse'
                              : 'bg-amber-400'
                            }`} />
                          <span className="hidden sm:inline">
                            {timerStatus === 'running' ? 'Running' : 'Paused'}
                          </span>
                          <span className="sm:hidden">
                            {timerStatus === 'running' ? '●' : '⏸'}
                          </span>
                        </>
                      ) : (
                        <>
                          <Play className="h-3 w-3 mr-1" />
                          <span className="hidden sm:inline">Start Timer</span>
                          <span className="sm:hidden">Timer</span>
                        </>
                      )}
                    </Button>

                    {/* Subtasks indicator */}
                    {hasSubtasks && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-gray-400 hover:text-white hover:bg-violet-500/20"
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsExpanded(!isExpanded);
                        }}
                      >
                        {isExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                        <span className="ml-1">{subtasks.length} subtask{subtasks.length !== 1 ? 's' : ''}</span>
                      </Button>
                    )}
                  </div>

                  {/* Completion button */}
                  <div className="mt-3 flex justify-end">
                    <Button
                      variant={task.completionPercentage === 100 ? "default" : "outline"}
                      size="sm"
                      className={`
                    h-9 md:h-8 px-4 md:px-3 text-xs transition-all duration-300 touch-manipulation
                    ${task.completionPercentage === 100
                          ? 'bg-emerald-500 hover:bg-emerald-600 text-white'
                          : 'border-violet-500/50 text-violet-400 hover:bg-violet-500/20 hover:text-violet-300'
                        }
                  `}
                      onClick={handleToggleComplete}
                    >
                      {task.completionPercentage === 100 ? (
                        <>
                          <CheckCircle2 className="h-4 w-4 md:h-3 md:w-3 mr-1" />
                          <span className="hidden sm:inline">Completed</span>
                          <span className="sm:hidden">Done</span>
                        </>
                      ) : (
                        <>
                          <Circle className="h-4 w-4 md:h-3 md:w-3 mr-1" />
                          <span className="hidden sm:inline">Mark Done</span>
                          <span className="sm:hidden">Done</span>
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>

                {/* Overdue warning */}
                {dueDateStatus === 'overdue' && (
                  <div className="absolute top-2 right-2">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                  </div>
                )}
              </Card>

              {/* Subtasks */}
              <AnimatePresence>
                {isExpanded && hasSubtasks && (
                  <motion.div
                    variants={subtaskVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    className="mt-2"
                  >
                    {subtasks.map((subtask, idx) => (
                      <EnhancedTaskCard
                        key={subtask.id}
                        task={subtask}
                        index={idx}
                        depth={depth + 1}
                        showSubtasks={false}
                      />
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </MobileSwipeActions>
          </motion.div>
        )}
      </Draggable>

      {/* Edit Task Modal */}
      <TaskCreationModal
        isOpen={isEditing}
        onClose={() => setIsEditing(false)}
        editingTask={task}
      />

      {/* Time Tracking Modal */}
      {showTimeDetails && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-[#030303]/95 backdrop-blur-md border border-gray-800 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Time Tracking - {task.title}</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowTimeDetails(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <TaskTimeTracking task={task} showAnalytics={true} />
            </div>
          </div>
        </div>
      )}

      {/* Analytics Modal */}
      {showAnalyticsModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-[#030303]/95 backdrop-blur-md border border-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Task Analytics</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAnalyticsModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <TaskAnalyticsDashboard task={task} />
            </div>
          </div>
        </div>
      )}
    </>
  );
});
