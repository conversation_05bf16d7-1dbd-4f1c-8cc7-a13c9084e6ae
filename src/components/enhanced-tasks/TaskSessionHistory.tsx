import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Clock, 
  Calendar, 
  Star, 
  TrendingUp, 
  Brain, 
  Target,
  ChevronDown,
  ChevronUp,
  BarChart3
} from 'lucide-react';
import { format } from 'date-fns';
import { taskTimerIntegration } from '@/services/TaskTimerIntegrationService';
import { TaskTimeData } from '@/services/TaskTimerIntegrationService';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

interface TaskSessionHistoryProps {
  taskId: string;
  taskTitle: string;
  className?: string;
}

const TaskSessionHistory: React.FC<TaskSessionHistoryProps> = ({
  taskId,
  taskTitle,
  className = ''
}) => {
  const { user } = useSupabaseAuth();
  const [timeData, setTimeData] = useState<TaskTimeData | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTimeData = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        const data = await taskTimerIntegration.getTaskTimeTracking(taskId, user.id);
        setTimeData(data);
      } catch (error) {
        console.error('Error loading task time data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTimeData();
  }, [taskId, user]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatSessionDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return minutes > 0 ? `${minutes}m ${secs}s` : `${secs}s`;
  };

  const getProductivityColor = (rating: number): string => {
    if (rating >= 4) return 'text-green-600 bg-green-100';
    if (rating >= 3) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getProgressColor = (progress: number): string => {
    if (progress >= 100) return 'bg-green-500';
    if (progress >= 75) return 'bg-blue-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!timeData || timeData.sessions.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-4 text-center text-gray-500">
          <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No time tracked for this task yet</p>
          <p className="text-xs text-gray-400 mt-1">Start a timer to begin tracking</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-500" />
              Time Tracking
            </CardTitle>
            <CardDescription className="text-sm">
              {timeData.sessions.length} session{timeData.sessions.length !== 1 ? 's' : ''} recorded
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-8 w-8 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">Total Time</span>
            </div>
            <p className="text-lg font-bold text-blue-600">
              {formatDuration(timeData.totalTimeSpent)}
            </p>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">Avg. Productivity</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold">
                {timeData.productivityRating.toFixed(1)}
              </span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-3 h-3 ${
                      star <= Math.round(timeData.productivityRating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        {timeData.estimatedTime && timeData.estimatedTime > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-gray-600">
                {Math.round(timeData.timeProgress)}% complete
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(timeData.timeProgress)}`}
                style={{ width: `${Math.min(timeData.timeProgress, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{formatDuration(timeData.totalTimeSpent)}</span>
              <span>{formatDuration(timeData.estimatedTime)}</span>
            </div>
          </div>
        )}

        {/* Session History */}
        {isExpanded && (
          <>
            <Separator className="my-4" />
            <div className="space-y-3">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Session History
              </h4>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {timeData.sessions.map((session, index) => {
                  let feedback;
                  try {
                    feedback = session.feedback ? JSON.parse(session.feedback) : null;
                  } catch {
                    feedback = null;
                  }

                  return (
                    <div
                      key={session.id || index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">
                            {format(new Date(session.start_time), 'MMM d, yyyy')}
                          </span>
                          <span className="text-xs text-gray-500">
                            {format(new Date(session.start_time), 'h:mm a')}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-3 text-xs text-gray-600">
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatSessionDuration(session.duration)}
                          </span>
                          
                          {session.productivity_rating && (
                            <Badge
                              variant="secondary"
                              className={`text-xs ${getProductivityColor(session.productivity_rating)}`}
                            >
                              <Star className="w-3 h-3 mr-1" />
                              {session.productivity_rating}/5
                            </Badge>
                          )}
                          
                          {feedback?.focus_rating && (
                            <Badge variant="outline" className="text-xs">
                              <Brain className="w-3 h-3 mr-1" />
                              Focus: {feedback.focus_rating}/5
                            </Badge>
                          )}
                        </div>

                        {session.notes && (
                          <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                            {session.notes}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}

        {/* Quick Stats */}
        {!isExpanded && timeData.sessions.length > 0 && (
          <div className="flex items-center justify-between text-xs text-gray-500 mt-3 pt-3 border-t">
            <span>Avg. session: {formatDuration(timeData.averageSessionLength)}</span>
            {timeData.lastWorkedOn && (
              <span>Last: {format(timeData.lastWorkedOn, 'MMM d')}</span>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TaskSessionHistory;