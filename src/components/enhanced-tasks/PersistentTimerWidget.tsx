import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEnhancedTimerStore, useTimerStatus, useTimerLinkedTask, useTimerActions } from '@/stores/enhancedTimerStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Play,
  Pause,
  Square,
  Clock,
  Target,
  ChevronUp,
  ChevronDown,
  ExternalLink,
} from 'lucide-react';
import { formatTime } from '@/utils/timeFormatting';
import { useNavigate } from 'react-router-dom';

interface PersistentTimerWidgetProps {
  className?: string;
}

export const PersistentTimerWidget = React.memo(function PersistentTimerWidget({ className = '' }: PersistentTimerWidgetProps) {
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  
  const timerStatus = useTimerStatus();
  const linkedTask = useTimerLinkedTask();
  const { startTimer, pauseTimer, stopTimer } = useTimerActions();
  
  // Get only the specific store values we need to avoid unnecessary re-renders
  const sessionStartTime = useEnhancedTimerStore(state => state.sessionStartTime);
  const pausedDuration = useEnhancedTimerStore(state => state.pausedDuration);
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Show widget only when timer is active and task is linked
  useEffect(() => {
    const shouldShow = !!(timerStatus !== 'idle' && linkedTask);
    setIsVisible(shouldShow);
  }, [timerStatus, linkedTask]);

  // Memoize the start time to prevent re-running the effect unnecessarily
  const startTime = useMemo(() => sessionStartTime.getTime(), [sessionStartTime]);

  // Update current time display
  useEffect(() => {
    if (timerStatus === 'running') {
      const interval = setInterval(() => {
        const now = Date.now();
        const elapsed = Math.floor((now - startTime + pausedDuration) / 1000);
        setCurrentTime(elapsed);
      }, 1000);

      return () => clearInterval(interval);
    } else if (timerStatus === 'paused') {
      // Show paused time - only update if different
      const elapsed = Math.floor(pausedDuration / 1000);
      setCurrentTime(prev => prev !== elapsed ? elapsed : prev);
    } else if (timerStatus === 'idle') {
      // Reset time when idle
      setCurrentTime(0);
    }
  }, [timerStatus, startTime, pausedDuration]);

  const handlePause = useCallback(async () => {
    try {
      await pauseTimer();
    } catch (error) {
      console.error('Error pausing timer:', error);
    }
  }, [pauseTimer]);

  const handleStop = useCallback(async () => {
    try {
      if (user?.id) {
        await stopTimer({ userId: user.id });
      }
    } catch (error) {
      console.error('Error stopping timer:', error);
    }
  }, [stopTimer, user?.id]);

  const handleNavigateToProductivity = useCallback(() => {
    navigate('/productivity');
  }, [navigate]);

  const handleResume = useCallback(async () => {
    try {
      if (linkedTask?.taskId && user?.id) {
        await startTimer(linkedTask.taskId, user.id, sessionStartTime);
      }
    } catch (error) {
      console.error('Error resuming timer:', error);
    }
  }, [startTimer, linkedTask?.taskId, user?.id, sessionStartTime]);

  const progress = useMemo(() => {
    if (!linkedTask?.estimatedTime) return 0;
    const estimatedSeconds = linkedTask.estimatedTime * 60;
    return Math.min((currentTime / estimatedSeconds) * 100, 100);
  }, [linkedTask?.estimatedTime, currentTime]);

  const statusColor = useMemo(() => {
    switch (timerStatus) {
      case 'running':
        return 'bg-emerald-500';
      case 'paused':
        return 'bg-amber-500';
      default:
        return 'bg-gray-500';
    }
  }, [timerStatus]);

  const statusText = useMemo(() => {
    switch (timerStatus) {
      case 'running':
        return 'Running';
      case 'paused':
        return 'Paused';
      default:
        return 'Idle';
    }
  }, [timerStatus]);

  if (!isVisible || !linkedTask) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 100, scale: 0.9 }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 30,
          opacity: { duration: 0.2 }
        }}
        className={`fixed bottom-6 right-6 z-50 ${className}`}
      >
        <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border shadow-lg hover:shadow-xl transition-all duration-300 max-w-sm">
          {/* Compact Header */}
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {/* Status Indicator */}
                <div className="relative">
                  <div className={`w-3 h-3 rounded-full ${statusColor}`}>
                    {timerStatus === 'running' && (
                      <div className={`absolute inset-0 rounded-full ${statusColor} animate-ping opacity-75`} />
                    )}
                  </div>
                </div>

                {/* Timer Display */}
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                  <span className="font-mono text-lg font-semibold text-gray-900 dark:text-white">
                    {formatTime(currentTime)}
                  </span>
                </div>

                {/* Status Badge */}
                <Badge 
                  variant="secondary" 
                  className={`text-xs ${
                    timerStatus === 'running' 
                      ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400' 
                      : 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400'
                  }`}
                >
                  {statusText}
                </Badge>
              </div>

              {/* Expand/Collapse Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-8 w-8 p-0"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Progress Bar */}
            {linkedTask.estimatedTime && (
              <div className="mt-3">
                <Progress 
                  value={progress} 
                  className="h-2"
                />
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(linkedTask.estimatedTime * 60)}</span>
                </div>
              </div>
            )}
          </div>

          {/* Expanded Content */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="px-4 pb-4 border-t border-gray-200 dark:border-gray-700">
                  {/* Task Info */}
                  <div className="pt-4 space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white text-sm line-clamp-2">
                        {linkedTask.taskTitle}
                      </h4>
                      {linkedTask.subjectName && (
                        <div className="flex items-center gap-2 mt-1">
                          <div 
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: linkedTask.subjectColor || '#6b7280' }}
                          />
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            {linkedTask.subjectName}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Task Stats */}
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="flex items-center gap-1">
                        <Target className="h-3 w-3 text-gray-500" />
                        <span className="text-gray-600 dark:text-gray-400">
                          Priority: {linkedTask.priority}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3 text-gray-500" />
                        <span className="text-gray-600 dark:text-gray-400">
                          Total: {formatTime((linkedTask.actualTimeSpent || 0) * 60)}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      {timerStatus === 'running' ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handlePause}
                          className="flex-1"
                        >
                          <Pause className="h-3 w-3 mr-1" />
                          Pause
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleResume}
                          className="flex-1"
                        >
                          <Play className="h-3 w-3 mr-1" />
                          Resume
                        </Button>
                      )}
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleStop}
                        className="flex-1"
                      >
                        <Square className="h-3 w-3 mr-1" />
                        Stop
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleNavigateToProductivity}
                        className="px-2"
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
});
