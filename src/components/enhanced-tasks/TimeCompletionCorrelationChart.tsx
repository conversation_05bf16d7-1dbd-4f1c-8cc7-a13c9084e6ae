import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  AlertCircle,
  CheckCircle2,
  Clock,
} from 'lucide-react';
import { taskAnalyticsService } from '@/services/TaskAnalyticsService';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

interface TimeCompletionCorrelationChartProps {
  className?: string;
}

export const TimeCompletionCorrelationChart: React.FC<TimeCompletionCorrelationChartProps> = ({
  className = '',
}) => {
  const [correlationData, setCorrelationData] = useState<{
    correlation: number;
    analysis: string;
    dataPoints: { timeSpent: number; completed: boolean; taskType: string; subject: string }[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useSupabaseAuth();

  useEffect(() => {
    if (user) {
      loadCorrelationData();
    }
  }, [user]);

  const loadCorrelationData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);
      const data = await taskAnalyticsService.getTimeCompletionCorrelation(user.id);
      setCorrelationData(data);
    } catch (error) {
      console.error('Error loading correlation data:', error);
      setError('Failed to load correlation analysis');
    } finally {
      setIsLoading(false);
    }
  };

  const getCorrelationIcon = (correlation: number) => {
    if (correlation > 0.1) return <TrendingUp className="h-4 w-4 text-emerald-400" />;
    if (correlation < -0.1) return <TrendingDown className="h-4 w-4 text-red-400" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getCorrelationColor = (correlation: number) => {
    if (correlation > 0.3) return 'text-emerald-400';
    if (correlation > 0.1) return 'text-blue-400';
    if (correlation < -0.1) return 'text-red-400';
    return 'text-gray-400';
  };

  const getCorrelationStrength = (correlation: number) => {
    const abs = Math.abs(correlation);
    if (abs > 0.7) return 'Very Strong';
    if (abs > 0.5) return 'Strong';
    if (abs > 0.3) return 'Moderate';
    if (abs > 0.1) return 'Weak';
    return 'Very Weak';
  };

  if (isLoading) {
    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-400">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-violet-400" />
            <span>Analyzing correlation...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-red-400">
            <AlertCircle className="h-6 w-6" />
            <span>{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!correlationData || correlationData.dataPoints.length === 0) {
    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-violet-400" />
            Time vs Completion Correlation
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <Clock className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No Data Available</h3>
          <p className="text-gray-400">
            Complete some tasks with time tracking to see correlation analysis.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Prepare chart data with colors based on completion status
  const chartData = correlationData.dataPoints.map((point, index) => ({
    ...point,
    id: index,
    color: point.completed ? '#10b981' : '#ef4444',
  }));

  const completedTasks = chartData.filter(d => d.completed);
  const incompleteTasks = chartData.filter(d => !d.completed);

  return (
    <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
      <CardHeader>
        <CardTitle className="text-white text-lg flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-violet-400" />
          Time vs Completion Correlation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Correlation summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          <div className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
            <div className="flex items-center gap-2 mb-2">
              {getCorrelationIcon(correlationData.correlation)}
              <span className="text-xs text-gray-400">Correlation</span>
            </div>
            <div className={`text-2xl font-bold ${getCorrelationColor(correlationData.correlation)}`}>
              {correlationData.correlation >= 0 ? '+' : ''}{correlationData.correlation}
            </div>
            <div className="text-xs text-gray-400">
              {getCorrelationStrength(correlationData.correlation)}
            </div>
          </div>

          <div className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle2 className="h-4 w-4 text-emerald-400" />
              <span className="text-xs text-gray-400">Completed</span>
            </div>
            <div className="text-2xl font-bold text-emerald-400">
              {completedTasks.length}
            </div>
            <div className="text-xs text-gray-400">
              Avg: {completedTasks.length > 0 
                ? Math.round(completedTasks.reduce((sum, t) => sum + t.timeSpent, 0) / completedTasks.length)
                : 0}m
            </div>
          </div>

          <div className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-red-400" />
              <span className="text-xs text-gray-400">Incomplete</span>
            </div>
            <div className="text-2xl font-bold text-red-400">
              {incompleteTasks.length}
            </div>
            <div className="text-xs text-gray-400">
              Avg: {incompleteTasks.length > 0 
                ? Math.round(incompleteTasks.reduce((sum, t) => sum + t.timeSpent, 0) / incompleteTasks.length)
                : 0}m
            </div>
          </div>
        </motion.div>

        {/* Analysis text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="p-4 bg-violet-500/10 rounded-lg border border-violet-500/30"
        >
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-4 w-4 text-violet-400" />
            <span className="text-sm font-medium text-violet-300">Analysis</span>
          </div>
          <p className="text-sm text-gray-300">{correlationData.analysis}</p>
        </motion.div>

        {/* Scatter plot */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-300">Time Spent vs Completion Status</h3>
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-emerald-400 rounded-full"></div>
                <span className="text-gray-400">Completed</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                <span className="text-gray-400">Incomplete</span>
              </div>
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={300}>
            <ScatterChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="timeSpent" 
                stroke="#9ca3af"
                fontSize={12}
                name="Time Spent (minutes)"
                label={{ value: 'Time Spent (minutes)', position: 'insideBottom', offset: -5, style: { textAnchor: 'middle', fill: '#9ca3af' } }}
              />
              <YAxis 
                stroke="#9ca3af" 
                fontSize={12}
                domain={[-0.5, 1.5]}
                tickFormatter={(value) => value === 1 ? 'Complete' : value === 0 ? 'Incomplete' : ''}
                label={{ value: 'Status', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#9ca3af' } }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1f2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#f3f4f6',
                }}
                formatter={(value: any, name: string, props: any) => {
                  const point = props.payload;
                  return [
                    [
                      `${point.timeSpent} minutes`,
                      `Status: ${point.completed ? 'Completed' : 'Incomplete'}`,
                      `Type: ${point.taskType}`,
                      `Subject: ${point.subject}`
                    ],
                    ''
                  ];
                }}
                labelFormatter={() => 'Task Details'}
              />
              <Scatter 
                dataKey="completed" 
                fill={(entry: any) => entry.color}
              />
              {/* Add reference lines for average time spent */}
              {completedTasks.length > 0 && (
                <ReferenceLine 
                  x={completedTasks.reduce((sum, t) => sum + t.timeSpent, 0) / completedTasks.length}
                  stroke="#10b981"
                  strokeDasharray="5 5"
                  strokeOpacity={0.5}
                />
              )}
              {incompleteTasks.length > 0 && (
                <ReferenceLine 
                  x={incompleteTasks.reduce((sum, t) => sum + t.timeSpent, 0) / incompleteTasks.length}
                  stroke="#ef4444"
                  strokeDasharray="5 5"
                  strokeOpacity={0.5}
                />
              )}
            </ScatterChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Task type breakdown */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
        >
          <h3 className="text-sm font-medium text-gray-300 mb-3">Completion by Task Type</h3>
          <div className="space-y-2">
            {Object.entries(
              chartData.reduce((acc, point) => {
                if (!acc[point.taskType]) {
                  acc[point.taskType] = { completed: 0, total: 0, avgTime: 0, totalTime: 0 };
                }
                acc[point.taskType].total += 1;
                acc[point.taskType].totalTime += point.timeSpent;
                if (point.completed) {
                  acc[point.taskType].completed += 1;
                }
                acc[point.taskType].avgTime = acc[point.taskType].totalTime / acc[point.taskType].total;
                return acc;
              }, {} as Record<string, { completed: number; total: number; avgTime: number; totalTime: number }>)
            ).map(([taskType, stats]) => (
              <div key={taskType} className="flex items-center justify-between p-2 bg-gray-900/50 rounded">
                <div className="flex-1">
                  <span className="text-sm font-medium text-white">{taskType}</span>
                  <div className="text-xs text-gray-400">
                    {stats.completed}/{stats.total} completed • {Math.round(stats.avgTime)}m avg
                  </div>
                </div>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${
                    (stats.completed / stats.total) >= 0.7 
                      ? 'border-emerald-500/50 text-emerald-400'
                      : (stats.completed / stats.total) >= 0.4
                      ? 'border-amber-500/50 text-amber-400'
                      : 'border-red-500/50 text-red-400'
                  }`}
                >
                  {Math.round((stats.completed / stats.total) * 100)}%
                </Badge>
              </div>
            ))}
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );
};