import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useEnhancedTimerStore } from '@/stores/enhancedTimerStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { EnhancedTodoItem } from '@/types/todo';
import { TaskCreationModal } from './TaskCreationModal';
import { TimeProgressIndicator } from './TimeProgressIndicator';
import { useNavigate } from 'react-router-dom';
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  addMonths,
  subMonths,
  isToday,
  getDay,
  startOfWeek,
  endOfWeek,
  addWeeks,
  subWeeks,
  isAfter,
  isBefore,
  addDays,
  parseISO,
} from 'date-fns';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Plus,
  Filter,
  Grid3X3,
  List,
  Check,
  X,
  Edit,
  Play,
} from 'lucide-react';
import { cn } from '@/lib/utils';

type ViewMode = 'month' | 'week';

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  tasks: EnhancedTodoItem[];
  isToday: boolean;
  isOverdue: boolean;
}

export function EnhancedCalendarView() {
  const {
    getFilteredTasks,
    updateTask,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const { startTimer, status: timerStatus, linkedTask } = useEnhancedTimerStore();

  const [viewMode, setViewMode] = useState<ViewMode>('month');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [createTaskDate, setCreateTaskDate] = useState<Date | undefined>(undefined);
  const [editingTask, setEditingTask] = useState<EnhancedTodoItem | null>(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);
  const [selectedTask, setSelectedTask] = useState<EnhancedTodoItem | null>(null);
  const [startingTimers, setStartingTimers] = useState<Set<string>>(new Set());

  const tasks = getFilteredTasks();

  // Group tasks by date
  const tasksByDate = useMemo(() => {
    const grouped: Record<string, EnhancedTodoItem[]> = {};
    
    tasks.forEach(task => {
      if (task.dueDate) {
        const dateKey = format(new Date(task.dueDate), 'yyyy-MM-dd');
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(task);
      }
    });

    return grouped;
  }, [tasks]);

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const start = viewMode === 'month' 
      ? startOfWeek(startOfMonth(currentDate))
      : startOfWeek(currentDate);
    
    const end = viewMode === 'month'
      ? endOfWeek(endOfMonth(currentDate))
      : endOfWeek(currentDate);

    const days = eachDayOfInterval({ start, end });

    return days.map(date => {
      const dateKey = format(date, 'yyyy-MM-dd');
      const dayTasks = tasksByDate[dateKey] || [];
      const isCurrentMonth = viewMode === 'week' || isSameMonth(date, currentDate);
      const today = new Date();
      const isOverdue = dayTasks.some(task => 
        task.dueDate && isBefore(new Date(task.dueDate), today) && task.completionPercentage < 100
      );

      return {
        date,
        isCurrentMonth,
        tasks: dayTasks,
        isToday: isToday(date),
        isOverdue,
      } as CalendarDay;
    });
  }, [currentDate, viewMode, tasksByDate]);

  // Navigation handlers
  const navigatePrevious = useCallback(() => {
    setCurrentDate(prev => 
      viewMode === 'month' ? subMonths(prev, 1) : subWeeks(prev, 1)
    );
  }, [viewMode]);

  const navigateNext = useCallback(() => {
    setCurrentDate(prev =>
      viewMode === 'month' ? addMonths(prev, 1) : addWeeks(prev, 1)
    );
  }, [viewMode]);

  // Handle creating task for specific date
  const handleCreateTaskForDate = useCallback((date: Date) => {
    setCreateTaskDate(date);
    setEditingTask(null);
    setIsCreateModalOpen(true);
  }, []);

  // Handle task editing
  const handleEditTask = useCallback((task: EnhancedTodoItem) => {
    setEditingTask(task);
    setCreateTaskDate(undefined);
    setIsCreateModalOpen(true);
  }, []);

  // Handle task viewing
  const handleViewTask = useCallback((task: EnhancedTodoItem) => {
    setSelectedTask(task);
    setShowTaskDetails(true);
  }, []);

  // Handle task completion toggle
  const handleToggleComplete = useCallback(async (task: EnhancedTodoItem) => {
    try {
      const newCompletionPercentage = task.completionPercentage >= 100 ? 0 : 100;
      await updateTask(task.id, { completionPercentage: newCompletionPercentage });
    } catch (error) {
      console.error('Failed to toggle task completion:', error);
    }
  }, [updateTask]);

  // Handle starting timer for a task
  const handleStartTimer = useCallback(async (task: EnhancedTodoItem) => {
    if (!user) {
      console.error('User not authenticated');
      return;
    }

    try {
      setStartingTimers(prev => new Set(prev).add(task.id));

      // Check if another timer is already running
      if (timerStatus === 'running' && linkedTask && linkedTask.taskId !== task.id) {
        const confirmStop = window.confirm(
          `A timer is already running for "${linkedTask.taskTitle}". Do you want to stop it and start a new timer for this task?`
        );
        if (!confirmStop) {
          setStartingTimers(prev => {
            const newSet = new Set(prev);
            newSet.delete(task.id);
            return newSet;
          });
          return;
        }
      }

      // Start timer for this task
      await startTimer(task.id, user.id);

      // Update task status to "In Progress" (column-2)
      if (task.columnId !== 'column-2') {
        await updateTask(task.id, {
          columnId: 'column-2',
          updatedAt: Date.now(),
        });
      }

      // Navigate to productivity page
      navigate('/productivity', {
        state: { 
          taskId: task.id,
          taskTitle: task.title 
        }
      });

    } catch (error) {
      console.error('Failed to start timer:', error);
      // You could add a toast notification here
    } finally {
      setStartingTimers(prev => {
        const newSet = new Set(prev);
        newSet.delete(task.id);
        return newSet;
      });
    }
  }, [user, timerStatus, linkedTask, startTimer, updateTask, navigate]);

  const goToToday = useCallback(() => {
    setCurrentDate(new Date());
  }, []);

  // Drag and drop handler
  const handleDragEnd = useCallback(async (result: DropResult) => {
    if (!result.destination) return;

    const taskId = result.draggableId;
    const newDateKey = result.destination.droppableId;
    
    try {
      const newDate = parseISO(newDateKey);
      await updateTask(taskId, { dueDate: newDate.getTime() });
    } catch (error) {
      console.error('Failed to update task date:', error);
    }
  }, [updateTask]);

  // Get priority color
  const getPriorityColor = useCallback((priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  }, []);

  // Get subject color
  const getSubjectColor = useCallback((subjectId?: string) => {
    if (!subjectId || !subjects[subjectId]) return 'bg-gray-500';
    return subjects[subjectId].color;
  }, [subjects]);

  // Render task item
  const renderTaskItem = useCallback((task: EnhancedTodoItem, index: number) => (
    <Draggable key={task.id} draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <motion.div
          ref={provided.innerRef}
          {...(provided.draggableProps as any)}
          {...provided.dragHandleProps}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05 }}
          className={cn(
            "p-2 mb-1 rounded-md text-xs cursor-pointer transition-all duration-200",
            "border border-border/50 hover:border-border group",
            snapshot.isDragging && "shadow-lg rotate-2 scale-105",
            task.completionPercentage === 100 && "opacity-60 line-through"
          )}
          onClick={(e) => {
            e.stopPropagation();
            handleViewTask(task);
          }}
          onDoubleClick={(e) => {
            e.stopPropagation();
            handleEditTask(task);
          }}
          style={{
            backgroundColor: getSubjectColor(task.subjectId) + '20',
            borderLeftColor: getSubjectColor(task.subjectId),
            borderLeftWidth: '3px',
          }}
        >
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="font-medium truncate flex-1">{task.title}</span>
              <div className="flex items-center gap-1 ml-2">
              <div
                className={cn("w-2 h-2 rounded-full", getPriorityColor(task.priority))}
              />
              {task.completionPercentage === 100 && (
                <CheckCircle2 className="w-3 h-3 text-green-500" />
              )}

              {/* Quick actions - visible on hover */}
              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1 ml-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleStartTimer(task);
                  }}
                  className={cn(
                    "p-0.5 rounded transition-colors",
                    startingTimers.has(task.id) || task.completionPercentage === 100
                      ? "opacity-50 cursor-not-allowed"
                      : timerStatus === 'running' && linkedTask?.taskId === task.id
                        ? "bg-emerald-200 dark:bg-emerald-800 hover:bg-emerald-300 dark:hover:bg-emerald-700"
                        : "hover:bg-violet-200 dark:hover:bg-violet-800"
                  )}
                  disabled={startingTimers.has(task.id) || task.completionPercentage === 100}
                  title={
                    task.completionPercentage === 100 
                      ? 'Task is completed' 
                      : timerStatus === 'running' && linkedTask?.taskId === task.id
                        ? 'Timer is running for this task'
                        : 'Start timer for this task'
                  }
                >
                  {startingTimers.has(task.id) ? (
                    <div className="animate-spin rounded-full w-3 h-3 border border-violet-500 border-t-transparent" />
                  ) : timerStatus === 'running' && linkedTask?.taskId === task.id ? (
                    <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse" />
                  ) : (
                    <Play className="w-3 h-3 text-violet-500" />
                  )}
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleComplete(task);
                  }}
                  className="p-0.5 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                  title={task.completionPercentage >= 100 ? "Mark incomplete" : "Mark complete"}
                >
                  {task.completionPercentage >= 100 ? (
                    <X className="w-3 h-3 text-gray-500" />
                  ) : (
                    <Check className="w-3 h-3 text-green-500" />
                  )}
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditTask(task);
                  }}
                  className="p-0.5 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                  title="Edit task"
                >
                  <Edit className="w-3 h-3 text-blue-500" />
                </button>
              </div>
            </div>
          </div>
            
            {/* Time tracking display */}
            {(task.actualTimeSpent || task.timeEstimate) && (
              <div className="mt-1">
                <TimeProgressIndicator
                  totalTimeSpent={(task.actualTimeSpent || 0) * 60} // Convert minutes to seconds
                  estimatedTime={task.timeEstimate ? task.timeEstimate * 60 : undefined}
                  variant="minimal"
                  className="text-xs"
                />
              </div>
            )}
          </div>
          
          {task.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {task.tags.slice(0, 2).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
              {task.tags.length > 2 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{task.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </motion.div>
      )}
    </Draggable>
  ), [getPriorityColor, getSubjectColor]);

  // Render calendar day
  const renderCalendarDay = useCallback((day: CalendarDay) => {
    const dateKey = format(day.date, 'yyyy-MM-dd');
    const dayNumber = format(day.date, 'd');
    const isSelected = selectedDate && isSameDay(day.date, selectedDate);

    return (
      <Droppable key={dateKey} droppableId={dateKey}>
        {(provided, snapshot) => (
          <motion.div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={cn(
              "min-h-[120px] p-2 border border-border/30 transition-all duration-200",
              "hover:border-border/60 hover:bg-muted/20",
              !day.isCurrentMonth && "opacity-40 bg-muted/10",
              day.isToday && "bg-primary/10 border-primary/30",
              isSelected && "bg-accent/20 border-accent",
              day.isOverdue && "bg-destructive/10 border-destructive/30",
              snapshot.isDraggingOver && "bg-accent/30 border-accent scale-[1.02]"
            )}
            onClick={() => setSelectedDate(day.date)}
            onDoubleClick={() => handleCreateTaskForDate(day.date)}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            {/* Day header */}
            <div className="flex items-center justify-between mb-2">
              <span className={cn(
                "text-sm font-medium",
                day.isToday && "text-primary font-bold",
                !day.isCurrentMonth && "text-muted-foreground"
              )}>
                {dayNumber}
              </span>
              
              {day.tasks.length > 0 && (
                <div className="flex items-center gap-1">
                  {day.isOverdue && (
                    <AlertTriangle className="w-3 h-3 text-destructive" />
                  )}
                  <Badge variant="secondary" className="text-xs px-1 py-0">
                    {day.tasks.length}
                  </Badge>
                </div>
              )}
            </div>

            {/* Tasks */}
            <div className="space-y-1 max-h-[80px] overflow-y-auto">
              {day.tasks.map((task, index) => renderTaskItem(task, index))}
              {provided.placeholder}
            </div>

            {/* Add task indicator */}
            {snapshot.isDraggingOver && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-2 p-2 border-2 border-dashed border-accent rounded-md text-center text-xs text-accent"
              >
                Drop task here
              </motion.div>
            )}
          </motion.div>
        )}
      </Droppable>
    );
  }, [selectedDate, renderTaskItem]);

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="space-y-4">
        {/* Calendar Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-bold">
              {format(currentDate, viewMode === 'month' ? 'MMMM yyyy' : 'MMM dd, yyyy')}
            </h2>
            
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm" onClick={navigatePrevious}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={goToToday}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={navigateNext}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                setCreateTaskDate(selectedDate || new Date());
                setIsCreateModalOpen(true);
              }}
              className="border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>

            <div className="flex items-center border rounded-md">
              <Button
                variant={viewMode === 'month' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('month')}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4 mr-1" />
                Month
              </Button>
              <Button
                variant={viewMode === 'week' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('week')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4 mr-1" />
                Week
              </Button>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <Card>
          <CardContent className="p-0">
            {/* Day headers */}
            <div className="grid grid-cols-7 border-b">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="p-3 text-center text-sm font-medium text-muted-foreground border-r last:border-r-0">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar days */}
            <div className="grid grid-cols-7">
              {calendarDays.map(day => renderCalendarDay(day))}
            </div>
          </CardContent>
        </Card>

        {/* Legend */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Legend</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>High Priority</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>Medium Priority</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Low Priority</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-3 h-3 text-destructive" />
                <span>Overdue</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-3 h-3 text-green-500" />
                <span>Completed</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Creation Modal */}
      <TaskCreationModal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setCreateTaskDate(undefined);
          setEditingTask(null);
        }}
        initialDueDate={createTaskDate}
        editingTask={editingTask}
      />

      {/* Task Details Modal */}
      {selectedTask && (
        <Dialog open={showTaskDetails} onOpenChange={setShowTaskDetails}>
          <DialogContent className="max-w-md bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800">
            <DialogHeader>
              <DialogTitle className="font-onest text-xl text-gray-900 dark:text-white flex items-center gap-2">
                <CalendarIcon className="h-5 w-5 text-violet-400" />
                Task Details
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">{selectedTask.title}</h3>
                {selectedTask.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{selectedTask.description}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Priority:</span>
                  <div className="flex items-center gap-1 mt-1">
                    <div className={cn("w-2 h-2 rounded-full", getPriorityColor(selectedTask.priority))} />
                    <span className="capitalize">{selectedTask.priority}</span>
                  </div>
                </div>

                <div>
                  <span className="text-gray-500 dark:text-gray-400">Status:</span>
                  <div className="flex items-center gap-1 mt-1">
                    {selectedTask.completionPercentage >= 100 ? (
                      <>
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                        <span>Completed</span>
                      </>
                    ) : (
                      <>
                        <Clock className="w-4 h-4 text-yellow-500" />
                        <span>In Progress</span>
                      </>
                    )}
                  </div>
                </div>

                {selectedTask.dueDate && (
                  <div className="col-span-2">
                    <span className="text-gray-500 dark:text-gray-400">Due Date:</span>
                    <p className="mt-1">{format(new Date(selectedTask.dueDate), 'PPP')}</p>
                  </div>
                )}

                {selectedTask.timeEstimate && (
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Estimated Time:</span>
                    <p className="mt-1">{Math.floor(selectedTask.timeEstimate / 60)}h {selectedTask.timeEstimate % 60}m</p>
                  </div>
                )}

                <div>
                  <span className="text-gray-500 dark:text-gray-400">Progress:</span>
                  <p className="mt-1">{selectedTask.completionPercentage}%</p>
                </div>
              </div>

              {selectedTask.tags.length > 0 && (
                <div>
                  <span className="text-gray-500 dark:text-gray-400 text-sm">Tags:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedTask.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={() => {
                    setShowTaskDetails(false);
                    handleStartTimer(selectedTask);
                  }}
                  className={cn(
                    "flex-1",
                    timerStatus === 'running' && linkedTask?.taskId === selectedTask.id
                      ? "bg-emerald-500 hover:bg-emerald-600"
                      : "bg-violet-500 hover:bg-violet-600"
                  )}
                  disabled={startingTimers.has(selectedTask.id) || selectedTask.completionPercentage === 100}
                >
                  {startingTimers.has(selectedTask.id) ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  ) : timerStatus === 'running' && linkedTask?.taskId === selectedTask.id ? (
                    <>
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse mr-2" />
                      Timer Running
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Start Timer
                    </>
                  )}
                </Button>

                <Button
                  onClick={() => {
                    setShowTaskDetails(false);
                    handleEditTask(selectedTask);
                  }}
                  variant="outline"
                  className="flex-1"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>

                <Button
                  onClick={() => {
                    handleToggleComplete(selectedTask);
                    setShowTaskDetails(false);
                  }}
                  variant="outline"
                  className="flex-1"
                >
                  {selectedTask.completionPercentage >= 100 ? (
                    <>
                      <X className="h-4 w-4 mr-2" />
                      Mark Incomplete
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Mark Complete
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </DragDropContext>
  );
}
