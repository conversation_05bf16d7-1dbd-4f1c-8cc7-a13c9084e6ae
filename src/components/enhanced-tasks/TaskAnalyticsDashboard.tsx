import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  ComposedChart,
  Area,
  AreaChart,
} from 'recharts';
import {
  Clock,
  TrendingUp,
  Target,
  Star,
  Calendar,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  Activity,
  Award,
  AlertCircle,
  CheckCircle2,
  Timer,
  Brain,
  Zap,
  BookO<PERSON>,
  <PERSON>,
  Lightbulb,
  TrendingDown,
  ArrowUp,
  ArrowDown,
  Minus,
} from 'lucide-react';
import { EnhancedTodoItem } from '@/types/todo';
import { taskTimerIntegration, TaskTimeData } from '@/services/TaskTimerIntegrationService';
import { taskAnalyticsService, ComprehensiveAnalytics } from '@/services/TaskAnalyticsService';
import { TimeCompletionCorrelationChart } from './TimeCompletionCorrelationChart';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, parseISO } from 'date-fns';

interface TaskAnalyticsDashboardProps {
  task?: EnhancedTodoItem; // Made optional for comprehensive analytics view
  className?: string;
  mode?: 'single-task' | 'comprehensive'; // New mode prop
}

export const TaskAnalyticsDashboard: React.FC<TaskAnalyticsDashboardProps> = ({
  task,
  className = '',
  mode = 'single-task',
}) => {
  const [timeData, setTimeData] = useState<TaskTimeData | null>(null);
  const [comprehensiveData, setComprehensiveData] = useState<ComprehensiveAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'all'>('month');

  const { user } = useSupabaseAuth();

  // Load analytics data based on mode
  useEffect(() => {
    if (user) {
      if (mode === 'single-task' && task) {
        loadTimeTrackingData();
      } else if (mode === 'comprehensive') {
        loadComprehensiveAnalytics();
      }
    }
  }, [task?.id, user, mode, timeRange]);

  const loadTimeTrackingData = async () => {
    if (!user || !task) return;

    try {
      setIsLoading(true);
      setError(null);
      const data = await taskTimerIntegration.getTaskTimeTracking(task.id, user.id);
      setTimeData(data);
    } catch (error) {
      console.error('Error loading time tracking data:', error);
      setError('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadComprehensiveAnalytics = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);
      const data = await taskAnalyticsService.getComprehensiveAnalytics(user.id, timeRange);
      setComprehensiveData(data);
    } catch (error) {
      console.error('Error loading comprehensive analytics:', error);
      setError('Failed to load comprehensive analytics');
    } finally {
      setIsLoading(false);
    }
  };

  // Format time duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  // Prepare chart data
  const prepareChartData = () => {
    if (!timeData || !timeData.sessions.length) return null;

    // Daily activity data
    const dailyData = timeData.sessions.reduce((acc, session) => {
      const date = format(parseISO(session.start_time), 'yyyy-MM-dd');
      if (!acc[date]) {
        acc[date] = {
          date,
          duration: 0,
          sessions: 0,
          productivity: 0,
          productivityCount: 0,
        };
      }
      acc[date].duration += session.duration || 0;
      acc[date].sessions += 1;
      if (session.productivity_rating > 0) {
        acc[date].productivity += session.productivity_rating;
        acc[date].productivityCount += 1;
      }
      return acc;
    }, {} as Record<string, any>);

    // Calculate average productivity per day
    Object.values(dailyData).forEach((day: any) => {
      day.avgProductivity = day.productivityCount > 0 
        ? day.productivity / day.productivityCount 
        : 0;
    });

    // Weekly summary
    const weeklyData = Object.values(dailyData)
      .sort((a: any, b: any) => a.date.localeCompare(b.date))
      .slice(-7); // Last 7 days

    // Productivity distribution
    const productivityDistribution = [1, 2, 3, 4, 5].map(rating => ({
      rating: `${rating} Star${rating !== 1 ? 's' : ''}`,
      count: timeData.sessions.filter(s => s.productivity_rating === rating).length,
      color: rating >= 4 ? '#10b981' : rating >= 3 ? '#3b82f6' : rating >= 2 ? '#f59e0b' : '#ef4444',
    })).filter(item => item.count > 0);

    return {
      daily: weeklyData,
      productivity: productivityDistribution,
    };
  };

  const chartData = prepareChartData();

  // Calculate insights
  const calculateInsights = () => {
    if (!timeData || !timeData.sessions.length) return null;

    const sessions = timeData.sessions;
    const totalSessions = sessions.length;
    const totalTime = timeData.totalTimeSpent;
    const avgSessionLength = timeData.averageSessionLength;
    const avgProductivity = timeData.productivityRating;

    // Time efficiency
    const estimatedTime = task.timeEstimate ? task.timeEstimate * 60 : null; // Convert to seconds
    const timeEfficiency = estimatedTime ? (estimatedTime / totalTime) * 100 : null;

    // Consistency score (based on session frequency)
    const firstSession = new Date(sessions[sessions.length - 1].start_time);
    const lastSession = new Date(sessions[0].start_time);
    const daysBetween = Math.ceil((lastSession.getTime() - firstSession.getTime()) / (1000 * 60 * 60 * 24)) || 1;
    const consistencyScore = (totalSessions / daysBetween) * 100;

    // Best performing day
    const dayPerformance = sessions.reduce((acc, session) => {
      const day = format(parseISO(session.start_time), 'EEEE');
      if (!acc[day]) acc[day] = { duration: 0, sessions: 0, productivity: 0, count: 0 };
      acc[day].duration += session.duration || 0;
      acc[day].sessions += 1;
      if (session.productivity_rating > 0) {
        acc[day].productivity += session.productivity_rating;
        acc[day].count += 1;
      }
      return acc;
    }, {} as Record<string, any>);

    const bestDay = Object.entries(dayPerformance)
      .map(([day, data]: [string, any]) => ({
        day,
        avgProductivity: data.count > 0 ? data.productivity / data.count : 0,
        totalTime: data.duration,
        sessions: data.sessions,
      }))
      .sort((a, b) => b.avgProductivity - a.avgProductivity)[0];

    return {
      totalSessions,
      totalTime,
      avgSessionLength,
      avgProductivity,
      timeEfficiency,
      consistencyScore: Math.min(consistencyScore, 100),
      bestDay,
    };
  };

  const insights = calculateInsights();

  // Render comprehensive analytics dashboard
  const renderComprehensiveAnalytics = () => {
    if (!comprehensiveData) {
      return (
        <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
          <CardContent className="text-center py-8">
            <Brain className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">No Analytics Data</h3>
            <p className="text-gray-400 mb-4">
              Complete some tasks and track time to see comprehensive analytics.
            </p>
          </CardContent>
        </Card>
      );
    }

    const { overview, taskMetrics, productivityPatterns, estimationAccuracy, taskTypeComparisons, subjectComparisons, trends, insights: analyticsInsights } = comprehensiveData;

    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Brain className="h-6 w-6 text-violet-400" />
              Comprehensive Task Analytics
            </CardTitle>
            <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
              <SelectTrigger className="w-32 bg-gray-800/50 border-gray-700">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="week">Week</SelectItem>
                <SelectItem value="month">Month</SelectItem>
                <SelectItem value="quarter">Quarter</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6 bg-gray-800/50">
              <TabsTrigger value="overview" className="text-gray-300 data-[state=active]:text-white text-xs">
                Overview
              </TabsTrigger>
              <TabsTrigger value="productivity" className="text-gray-300 data-[state=active]:text-white text-xs">
                Productivity
              </TabsTrigger>
              <TabsTrigger value="estimation" className="text-gray-300 data-[state=active]:text-white text-xs">
                Estimation
              </TabsTrigger>
              <TabsTrigger value="comparison" className="text-gray-300 data-[state=active]:text-white text-xs">
                Comparison
              </TabsTrigger>
              <TabsTrigger value="trends" className="text-gray-300 data-[state=active]:text-white text-xs">
                Trends
              </TabsTrigger>
              <TabsTrigger value="insights" className="text-gray-300 data-[state=active]:text-white text-xs">
                Insights
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6 mt-6">
              {/* Overview metrics */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle2 className="h-4 w-4 text-emerald-400" />
                    <span className="text-xs text-gray-400">Tasks</span>
                  </div>
                  <div className="text-lg font-semibold text-white">
                    {overview.completedTasks}/{overview.totalTasks}
                  </div>
                  <div className="text-xs text-gray-400">
                    {Math.round((overview.completedTasks / Math.max(overview.totalTasks, 1)) * 100)}% complete
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-blue-400" />
                    <span className="text-xs text-gray-400">Time Spent</span>
                  </div>
                  <div className="text-lg font-semibold text-white">
                    {Math.round(overview.totalTimeSpent / 60)}h
                  </div>
                  <div className="text-xs text-gray-400">
                    {overview.totalTimeSpent}m total
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Star className="h-4 w-4 text-amber-400" />
                    <span className="text-xs text-gray-400">Productivity</span>
                  </div>
                  <div className="text-lg font-semibold text-white">
                    {overview.averageProductivity.toFixed(1)}/5
                  </div>
                  <div className="text-xs text-gray-400">Average rating</div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Zap className="h-4 w-4 text-violet-400" />
                    <span className="text-xs text-gray-400">Efficiency</span>
                  </div>
                  <div className="text-lg font-semibold text-white">
                    {overview.overallEfficiency.toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-400">Tasks/hour</div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4 text-emerald-400" />
                    <span className="text-xs text-gray-400">Best Subject</span>
                  </div>
                  <div className="text-sm font-semibold text-white truncate">
                    {analyticsInsights.bestPerformingSubject}
                  </div>
                  <div className="text-xs text-gray-400">Top performer</div>
                </motion.div>
              </div>

              {/* Task completion velocity chart */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Task Completion Velocity
                </h3>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={trends.completionVelocity}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9ca3af"
                      fontSize={12}
                      tickFormatter={(value) => format(new Date(value), 'MMM d')}
                    />
                    <YAxis stroke="#9ca3af" fontSize={12} />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#f3f4f6',
                      }}
                      formatter={(value: any) => [`${value} tasks/day`, 'Velocity']}
                      labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="velocity" 
                      stroke="#8b5cf6" 
                      strokeWidth={2}
                      dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </motion.div>
            </TabsContent>

            <TabsContent value="productivity" className="space-y-6 mt-6">
              {/* Productivity patterns heatmap */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Productivity Patterns
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-xs text-gray-400 mb-2">Best Time of Day</h4>
                    <div className="text-lg font-semibold text-violet-400">
                      {analyticsInsights.mostProductiveTimeOfDay}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-xs text-gray-400 mb-2">Best Day of Week</h4>
                    <div className="text-lg font-semibold text-emerald-400">
                      {analyticsInsights.mostProductiveDayOfWeek}
                    </div>
                  </div>
                </div>
                {productivityPatterns.length > 0 && (
                  <div className="mt-4">
                    <ResponsiveContainer width="100%" height={200}>
                      <BarChart data={productivityPatterns.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis 
                          dataKey="timeOfDay" 
                          stroke="#9ca3af"
                          fontSize={12}
                        />
                        <YAxis stroke="#9ca3af" fontSize={12} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: '#1f2937',
                            border: '1px solid #374151',
                            borderRadius: '8px',
                            color: '#f3f4f6',
                          }}
                          formatter={(value: any) => [`${value.toFixed(1)}/5`, 'Productivity']}
                        />
                        <Bar 
                          dataKey="averageProductivity" 
                          fill="#8b5cf6" 
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </motion.div>

              {/* Productivity trend */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Productivity Trend
                </h3>
                <ResponsiveContainer width="100%" height={200}>
                  <AreaChart data={trends.productivityTrend}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9ca3af"
                      fontSize={12}
                      tickFormatter={(value) => format(new Date(value), 'MMM d')}
                    />
                    <YAxis stroke="#9ca3af" fontSize={12} domain={[0, 5]} />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#f3f4f6',
                      }}
                      formatter={(value: any) => [`${value.toFixed(1)}/5`, 'Productivity']}
                      labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="productivity" 
                      stroke="#10b981" 
                      fill="#10b981"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </motion.div>
            </TabsContent>

            <TabsContent value="estimation" className="space-y-6 mt-6">
              {/* Time estimation accuracy */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Time Estimation Accuracy
                </h3>
                {estimationAccuracy.length > 0 ? (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-400">
                          {estimationAccuracy.filter(ea => Math.abs(ea.accuracyPercentage - 100) <= 20).length}
                        </div>
                        <div className="text-xs text-gray-400">Accurate estimates</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-amber-400">
                          {estimationAccuracy.filter(ea => ea.estimationError > 0).length}
                        </div>
                        <div className="text-xs text-gray-400">Overestimated</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-400">
                          {estimationAccuracy.filter(ea => ea.estimationError < 0).length}
                        </div>
                        <div className="text-xs text-gray-400">Underestimated</div>
                      </div>
                    </div>
                    <ResponsiveContainer width="100%" height={200}>
                      <ScatterChart data={estimationAccuracy}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis 
                          dataKey="estimatedTime" 
                          stroke="#9ca3af"
                          fontSize={12}
                          name="Estimated Time (min)"
                        />
                        <YAxis 
                          dataKey="actualTime" 
                          stroke="#9ca3af" 
                          fontSize={12}
                          name="Actual Time (min)"
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: '#1f2937',
                            border: '1px solid #374151',
                            borderRadius: '8px',
                            color: '#f3f4f6',
                          }}
                          formatter={(value: any, name: string) => [
                            `${value} min`,
                            name === 'actualTime' ? 'Actual Time' : 'Estimated Time'
                          ]}
                        />
                        <Scatter 
                          dataKey="actualTime" 
                          fill="#8b5cf6"
                        />
                      </ScatterChart>
                    </ResponsiveContainer>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
                    <p className="text-gray-400">No time estimates available for analysis</p>
                  </div>
                )}
              </motion.div>
            </TabsContent>

            <TabsContent value="comparison" className="space-y-6 mt-6">
              {/* Task type comparison */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Task Type Performance
                </h3>
                {taskTypeComparisons.length > 0 ? (
                  <div className="space-y-3">
                    {taskTypeComparisons.slice(0, 5).map((taskType, index) => (
                      <div key={taskType.taskType} className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium text-white">{taskType.taskType}</div>
                          <div className="text-xs text-gray-400">
                            {taskType.completedTasks}/{taskType.totalTasks} tasks • {taskType.averageTimeSpent}m avg
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 text-amber-400">
                            <Star className="h-3 w-3" />
                            <span className="text-sm font-medium">{taskType.averageProductivity}</span>
                          </div>
                          <div className="text-xs text-gray-400">{taskType.completionRate}% complete</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
                    <p className="text-gray-400">No task type data available</p>
                  </div>
                )}
              </motion.div>

              {/* Subject comparison */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Subject Performance
                </h3>
                {subjectComparisons.length > 0 ? (
                  <div className="space-y-3">
                    {subjectComparisons.slice(0, 5).map((subject, index) => (
                      <div key={subject.subject} className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-white">{subject.subject}</span>
                            {subject.trendDirection === 'improving' && <ArrowUp className="h-3 w-3 text-emerald-400" />}
                            {subject.trendDirection === 'declining' && <ArrowDown className="h-3 w-3 text-red-400" />}
                            {subject.trendDirection === 'stable' && <Minus className="h-3 w-3 text-gray-400" />}
                          </div>
                          <div className="text-xs text-gray-400">
                            {subject.completedTasks}/{subject.totalTasks} tasks • {Math.round(subject.totalTimeSpent / 60)}h total
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 text-amber-400">
                            <Star className="h-3 w-3" />
                            <span className="text-sm font-medium">{subject.averageProductivity}</span>
                          </div>
                          <div className="text-xs text-gray-400">{subject.completionRate}% complete</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
                    <p className="text-gray-400">No subject data available</p>
                  </div>
                )}
              </motion.div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-6 mt-6">
              {/* Time spent trend */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Time Spent Trend
                </h3>
                <ResponsiveContainer width="100%" height={200}>
                  <ComposedChart data={trends.timeSpentTrend}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9ca3af"
                      fontSize={12}
                      tickFormatter={(value) => format(new Date(value), 'MMM d')}
                    />
                    <YAxis stroke="#9ca3af" fontSize={12} />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#f3f4f6',
                      }}
                      formatter={(value: any) => [`${value} min`, 'Time Spent']}
                      labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                    />
                    <Bar 
                      dataKey="timeSpent" 
                      fill="#3b82f6" 
                      radius={[4, 4, 0, 0]}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </motion.div>

              {/* Time vs Completion Correlation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <TimeCompletionCorrelationChart />
              </motion.div>
            </TabsContent>

            <TabsContent value="insights" className="space-y-6 mt-6">
              {/* Key insights */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-violet-500/10 rounded-lg border border-violet-500/30"
              >
                <div className="flex items-center gap-2 mb-4">
                  <Lightbulb className="h-5 w-5 text-violet-400" />
                  <span className="text-lg font-medium text-violet-300">Key Insights</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-sm text-gray-400 mb-1">Best Performing Subject</div>
                    <div className="text-lg font-semibold text-emerald-400">
                      {analyticsInsights.bestPerformingSubject}
                    </div>
                  </div>
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-sm text-gray-400 mb-1">Most Accurate Task Type</div>
                    <div className="text-lg font-semibold text-blue-400">
                      {analyticsInsights.mostAccurateEstimationTaskType}
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-violet-300">Improvement Suggestions:</h4>
                  <ul className="space-y-1 text-sm text-gray-300">
                    {analyticsInsights.improvementSuggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-violet-400 mt-1">•</span>
                        <span>{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-400">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-violet-400" />
            <span>Loading analytics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-red-400">
            <AlertCircle className="h-6 w-6" />
            <span>{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render comprehensive analytics view
  if (mode === 'comprehensive') {
    return renderComprehensiveAnalytics();
  }

  // Render single task analytics view
  if (!timeData || timeData.sessions.length === 0) {
    return (
      <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-violet-400" />
            Task Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <Activity className="h-12 w-12 mx-auto mb-4 text-gray-500 opacity-50" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No Data Yet</h3>
          <p className="text-gray-400 mb-4">
            Start tracking time on this task to see detailed analytics and insights.
          </p>
          <Button
            variant="outline"
            className="border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
            onClick={loadTimeTrackingData}
          >
            <Timer className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-[#030303]/80 backdrop-blur-md border border-gray-800/50 ${className}`}>
      <CardHeader>
        <CardTitle className="text-white text-lg flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-violet-400" />
          Task Analytics - {task?.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-gray-800/50">
            <TabsTrigger value="overview" className="text-gray-300 data-[state=active]:text-white">
              Overview
            </TabsTrigger>
            <TabsTrigger value="trends" className="text-gray-300 data-[state=active]:text-white">
              Trends
            </TabsTrigger>
            <TabsTrigger value="insights" className="text-gray-300 data-[state=active]:text-white">
              Insights
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Key metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-violet-400" />
                  <span className="text-xs text-gray-400">Total Time</span>
                </div>
                <div className="text-lg font-semibold text-white">
                  {formatDuration(timeData.totalTimeSpent)}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="h-4 w-4 text-blue-400" />
                  <span className="text-xs text-gray-400">Sessions</span>
                </div>
                <div className="text-lg font-semibold text-white">
                  {timeData.sessions.length}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Timer className="h-4 w-4 text-amber-400" />
                  <span className="text-xs text-gray-400">Avg Session</span>
                </div>
                <div className="text-lg font-semibold text-white">
                  {formatDuration(timeData.averageSessionLength)}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Star className="h-4 w-4 text-emerald-400" />
                  <span className="text-xs text-gray-400">Avg Rating</span>
                </div>
                <div className="text-lg font-semibold text-white">
                  {timeData.productivityRating > 0 
                    ? `${timeData.productivityRating.toFixed(1)}/5`
                    : 'N/A'
                  }
                </div>
              </motion.div>
            </div>

            {/* Progress toward estimate */}
            {task.timeEstimate && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-violet-400" />
                    <span className="text-sm text-gray-300">Progress vs Estimate</span>
                  </div>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${
                      timeData.timeProgress >= 100 
                        ? 'border-emerald-500/50 text-emerald-400'
                        : 'border-violet-500/50 text-violet-400'
                    }`}
                  >
                    {Math.round(timeData.timeProgress)}%
                  </Badge>
                </div>
                <Progress
                  value={Math.min(timeData.timeProgress, 100)}
                  className="h-3 bg-gray-800 mb-2"
                />
                <div className="flex justify-between text-xs text-gray-400">
                  <span>{formatDuration(timeData.totalTimeSpent)}</span>
                  <span>{formatDuration(task.timeEstimate * 60)}</span>
                </div>
                {timeData.timeProgress > 100 && (
                  <div className="mt-2 text-xs text-amber-400">
                    Over estimate by {formatDuration(timeData.totalTimeSpent - (task.timeEstimate * 60))}
                  </div>
                )}
              </motion.div>
            )}
          </TabsContent>

          <TabsContent value="trends" className="space-y-6 mt-6">
            {chartData && (
              <>
                {/* Daily activity chart */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Daily Activity (Last 7 Days)
                  </h3>
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={chartData.daily}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis 
                        dataKey="date" 
                        stroke="#9ca3af"
                        fontSize={12}
                        tickFormatter={(value) => format(new Date(value), 'MMM d')}
                      />
                      <YAxis stroke="#9ca3af" fontSize={12} />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1f2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#f3f4f6',
                        }}
                        formatter={(value: any, name: string) => [
                          name === 'duration' ? formatDuration(value) : value,
                          name === 'duration' ? 'Time Spent' : 'Sessions'
                        ]}
                        labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                      />
                      <Bar dataKey="duration" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </motion.div>

                {/* Productivity distribution */}
                {chartData.productivity.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                  >
                    <h3 className="text-sm font-medium text-gray-300 mb-4 flex items-center gap-2">
                      <PieChartIcon className="h-4 w-4" />
                      Productivity Rating Distribution
                    </h3>
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={chartData.productivity}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="count"
                          label={({ rating, count }) => `${rating}: ${count}`}
                        >
                          {chartData.productivity.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip
                          contentStyle={{
                            backgroundColor: '#1f2937',
                            border: '1px solid #374151',
                            borderRadius: '8px',
                            color: '#f3f4f6',
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </motion.div>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="insights" className="space-y-6 mt-6">
            {insights && (
              <>
                {/* Performance insights */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                >
                  {/* Time efficiency */}
                  {insights.timeEfficiency && (
                    <div className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-4 w-4 text-blue-400" />
                        <span className="text-sm font-medium text-gray-300">Time Efficiency</span>
                      </div>
                      <div className="text-2xl font-bold text-white mb-1">
                        {Math.round(insights.timeEfficiency)}%
                      </div>
                      <p className="text-xs text-gray-400">
                        {insights.timeEfficiency > 100 
                          ? 'Taking longer than estimated'
                          : insights.timeEfficiency > 80
                          ? 'Very close to estimate'
                          : 'Ahead of schedule'
                        }
                      </p>
                    </div>
                  )}

                  {/* Consistency score */}
                  <div className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-emerald-400" />
                      <span className="text-sm font-medium text-gray-300">Consistency</span>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">
                      {Math.round(insights.consistencyScore)}%
                    </div>
                    <p className="text-xs text-gray-400">
                      {insights.consistencyScore > 80 
                        ? 'Very consistent work pattern'
                        : insights.consistencyScore > 50
                        ? 'Moderately consistent'
                        : 'Could be more consistent'
                      }
                    </p>
                  </div>
                </motion.div>

                {/* Best performing day */}
                {insights.bestDay && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                  >
                    <div className="flex items-center gap-2 mb-3">
                      <Award className="h-4 w-4 text-amber-400" />
                      <span className="text-sm font-medium text-gray-300">Best Performing Day</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-lg font-semibold text-white">
                          {insights.bestDay.day}
                        </div>
                        <div className="text-xs text-gray-400">
                          {insights.bestDay.sessions} sessions, {formatDuration(insights.bestDay.totalTime)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1 text-amber-400">
                          <Star className="h-4 w-4" />
                          <span className="font-medium">
                            {insights.bestDay.avgProductivity.toFixed(1)}/5
                          </span>
                        </div>
                        <div className="text-xs text-gray-400">Avg productivity</div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Recommendations */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="p-4 bg-violet-500/10 rounded-lg border border-violet-500/30"
                >
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircle2 className="h-4 w-4 text-violet-400" />
                    <span className="text-sm font-medium text-violet-300">Recommendations</span>
                  </div>
                  <ul className="space-y-2 text-sm text-gray-300">
                    {insights.avgSessionLength < 1800 && (
                      <li>• Consider longer focus sessions (current avg: {formatDuration(insights.avgSessionLength)})</li>
                    )}
                    {insights.avgProductivity < 3 && (
                      <li>• Try different environments or times to improve productivity</li>
                    )}
                    {insights.consistencyScore < 50 && (
                      <li>• Establish a more regular work schedule for this task</li>
                    )}
                    {insights.timeEfficiency && insights.timeEfficiency > 120 && (
                      <li>• Consider breaking this task into smaller subtasks</li>
                    )}
                    {insights.bestDay && (
                      <li>• Schedule more work on {insights.bestDay.day}s when you're most productive</li>
                    )}
                  </ul>
                </motion.div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};