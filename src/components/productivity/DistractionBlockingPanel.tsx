import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { <PERSON>lider } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { 
  Shield, 
  Volume2, 
  VolumeX, 
  Play, 
  Pause, 
  SkipForward,
  Settings,
  Eye,
  EyeOff,
  Waves,
  TreePine,
  Coffee,
  Zap,
  Brain,
  Moon,
  Sun,
  Cloud,
  Flame
} from 'lucide-react';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { toast } from '../ui/use-toast';
import { cn } from '../../lib/utils';

// Ambient sound interface
interface AmbientSound {
  id: string;
  name: string;
  icon: React.ReactNode;
  url: string;
  category: 'nature' | 'urban' | 'white-noise' | 'instrumental';
  description: string;
  duration?: number; // in seconds, undefined for looping sounds
}

// Distraction blocking rule interface
interface DistractionRule {
  id: string;
  name: string;
  type: 'website' | 'app' | 'notification';
  pattern: string; // URL pattern or app name
  isActive: boolean;
  severity: 'block' | 'warn' | 'limit';
  timeLimit?: number; // minutes per session
  customMessage?: string;
}

// Focus mode preset interface
interface FocusPreset {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  ambientSound?: string;
  volume: number;
  distractionRules: string[];
  pomodoroSettings?: {
    workDuration: number;
    breakDuration: number;
    longBreakDuration: number;
    sessionsUntilLongBreak: number;
  };
}

interface DistractionBlockingPanelProps {
  isActive: boolean;
  onToggle: (active: boolean) => void;
  currentTask?: {
    id: string;
    title: string;
    type: string;
    subject?: string;
  };
}

export const DistractionBlockingPanel: React.FC<DistractionBlockingPanelProps> = ({
  isActive,
  onToggle,
  currentTask
}) => {
  const { user } = useSupabaseAuth();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const [currentSound, setCurrentSound] = useState<string | null>(null);
  const [volume, setVolume] = useState(50);
  const [isPlaying, setIsPlaying] = useState(false);
  const [distractionRules, setDistractionRules] = useState<DistractionRule[]>([]);
  const [activePreset, setActivePreset] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [blockedAttempts, setBlockedAttempts] = useState<number>(0);

  // Predefined ambient sounds
  const ambientSounds: AmbientSound[] = [
    {
      id: 'rain',
      name: 'Rain',
      icon: <Cloud className="h-4 w-4" />,
      url: '/sounds/rain.mp3',
      category: 'nature',
      description: 'Gentle rainfall for deep focus'
    },
    {
      id: 'forest',
      name: 'Forest',
      icon: <TreePine className="h-4 w-4" />,
      url: '/sounds/forest.mp3',
      category: 'nature',
      description: 'Birds chirping in a peaceful forest'
    },
    {
      id: 'cafe',
      name: 'Coffee Shop',
      icon: <Coffee className="h-4 w-4" />,
      url: '/sounds/cafe.mp3',
      category: 'urban',
      description: 'Ambient coffee shop chatter'
    },
    {
      id: 'white-noise',
      name: 'White Noise',
      icon: <Waves className="h-4 w-4" />,
      url: '/sounds/white-noise.mp3',
      category: 'white-noise',
      description: 'Pure white noise for concentration'
    },
    {
      id: 'fireplace',
      name: 'Fireplace',
      icon: <Flame className="h-4 w-4" />,
      url: '/sounds/fireplace.mp3',
      category: 'nature',
      description: 'Crackling fireplace sounds'
    },
    {
      id: 'ocean',
      name: 'Ocean Waves',
      icon: <Waves className="h-4 w-4" />,
      url: '/sounds/ocean.mp3',
      category: 'nature',
      description: 'Calming ocean waves'
    }
  ];

  // Predefined focus presets
  const focusPresets: FocusPreset[] = [
    {
      id: 'deep-focus',
      name: 'Deep Focus',
      description: 'Maximum concentration with minimal distractions',
      icon: <Brain className="h-4 w-4" />,
      ambientSound: 'white-noise',
      volume: 30,
      distractionRules: ['social-media', 'entertainment', 'news'],
      pomodoroSettings: {
        workDuration: 50,
        breakDuration: 10,
        longBreakDuration: 30,
        sessionsUntilLongBreak: 3
      }
    },
    {
      id: 'creative-flow',
      name: 'Creative Flow',
      description: 'Balanced environment for creative work',
      icon: <Zap className="h-4 w-4" />,
      ambientSound: 'cafe',
      volume: 40,
      distractionRules: ['social-media'],
      pomodoroSettings: {
        workDuration: 45,
        breakDuration: 15,
        longBreakDuration: 30,
        sessionsUntilLongBreak: 4
      }
    },
    {
      id: 'study-session',
      name: 'Study Session',
      description: 'Optimized for reading and memorization',
      icon: <Moon className="h-4 w-4" />,
      ambientSound: 'rain',
      volume: 25,
      distractionRules: ['social-media', 'entertainment'],
      pomodoroSettings: {
        workDuration: 25,
        breakDuration: 5,
        longBreakDuration: 15,
        sessionsUntilLongBreak: 4
      }
    },
    {
      id: 'morning-energy',
      name: 'Morning Energy',
      description: 'Energizing preset for morning productivity',
      icon: <Sun className="h-4 w-4" />,
      ambientSound: 'forest',
      volume: 35,
      distractionRules: ['social-media', 'news'],
      pomodoroSettings: {
        workDuration: 30,
        breakDuration: 10,
        longBreakDuration: 20,
        sessionsUntilLongBreak: 3
      }
    }
  ];

  // Default distraction rules
  const defaultDistractionRules: DistractionRule[] = [
    {
      id: 'social-media',
      name: 'Social Media',
      type: 'website',
      pattern: '*.facebook.com,*.twitter.com,*.instagram.com,*.tiktok.com,*.snapchat.com',
      isActive: true,
      severity: 'block',
      customMessage: 'Stay focused on your studies! Social media can wait.'
    },
    {
      id: 'entertainment',
      name: 'Entertainment',
      type: 'website',
      pattern: '*.youtube.com,*.netflix.com,*.twitch.tv,*.reddit.com',
      isActive: true,
      severity: 'warn',
      timeLimit: 10,
      customMessage: 'Taking a break? Remember your study goals!'
    },
    {
      id: 'news',
      name: 'News Sites',
      type: 'website',
      pattern: '*.cnn.com,*.bbc.com,*.news.google.com',
      isActive: false,
      severity: 'limit',
      timeLimit: 5,
      customMessage: 'Stay informed, but stay focused!'
    },
    {
      id: 'gaming',
      name: 'Gaming',
      type: 'app',
      pattern: 'steam,discord,league of legends,valorant',
      isActive: true,
      severity: 'block',
      customMessage: 'Games will be more fun after you finish studying!'
    }
  ];

  // Load settings on component mount
  useEffect(() => {
    if (!user?.id) return;
    
    loadDistractionSettings();
  }, [user?.id]);

  // Update audio volume when volume state changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume / 100;
    }
  }, [volume]);

  const loadDistractionSettings = () => {
    if (!user?.id) return;
    
    const stored = localStorage.getItem(`distractionSettings_${user.id}`);
    if (stored) {
      const settings = JSON.parse(stored);
      setDistractionRules(settings.rules || defaultDistractionRules);
      setVolume(settings.volume || 50);
      setCurrentSound(settings.currentSound || null);
      setActivePreset(settings.activePreset || null);
    } else {
      setDistractionRules(defaultDistractionRules);
    }
  };

  const saveDistractionSettings = () => {
    if (!user?.id) return;
    
    const settings = {
      rules: distractionRules,
      volume,
      currentSound,
      activePreset,
      lastUpdated: Date.now()
    };
    
    localStorage.setItem(`distractionSettings_${user.id}`, JSON.stringify(settings));
  };

  // Save settings whenever they change
  useEffect(() => {
    saveDistractionSettings();
  }, [distractionRules, volume, currentSound, activePreset]);

  const playAmbientSound = (soundId: string) => {
    const sound = ambientSounds.find(s => s.id === soundId);
    if (!sound) return;

    if (audioRef.current) {
      audioRef.current.pause();
    }

    // Create new audio element
    const audio = new Audio(sound.url);
    audio.loop = true;
    audio.volume = volume / 100;
    
    audio.play().then(() => {
      setCurrentSound(soundId);
      setIsPlaying(true);
      audioRef.current = audio;
      
      toast({
        title: "Ambient sound started",
        description: `Now playing: ${sound.name}`
      });
    }).catch((error) => {
      console.error('Error playing sound:', error);
      toast({
        title: "Audio error",
        description: "Could not play the selected sound. Check your audio settings.",
        variant: "destructive"
      });
    });
  };

  const stopAmbientSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    setCurrentSound(null);
    setIsPlaying(false);
    
    toast({
      title: "Ambient sound stopped",
      description: "Audio playback has been stopped"
    });
  };

  const togglePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const applyFocusPreset = (presetId: string) => {
    const preset = focusPresets.find(p => p.id === presetId);
    if (!preset) return;

    // Apply ambient sound
    if (preset.ambientSound) {
      playAmbientSound(preset.ambientSound);
    }
    
    // Apply volume
    setVolume(preset.volume);
    
    // Apply distraction rules
    const updatedRules = distractionRules.map(rule => ({
      ...rule,
      isActive: preset.distractionRules.includes(rule.id)
    }));
    setDistractionRules(updatedRules);
    
    setActivePreset(presetId);
    
    toast({
      title: "Focus preset applied",
      description: `Applied "${preset.name}" settings`
    });
  };

  const toggleDistractionRule = (ruleId: string) => {
    const updatedRules = distractionRules.map(rule =>
      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
    );
    setDistractionRules(updatedRules);
  };

  const getActiveRulesCount = (): number => {
    return distractionRules.filter(rule => rule.isActive).length;
  };

  const getCurrentSoundInfo = () => {
    if (!currentSound) return null;
    return ambientSounds.find(s => s.id === currentSound);
  };

  const simulateBlockAttempt = () => {
    setBlockedAttempts(prev => prev + 1);
    toast({
      title: "Distraction blocked!",
      description: "Stay focused on your current task",
      variant: "destructive"
    });
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Focus Environment</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant={isActive ? "default" : "secondary"}>
              {isActive ? "Active" : "Inactive"}
            </Badge>
            <Switch checked={isActive} onCheckedChange={onToggle} />
          </div>
        </div>
        
        {currentTask && (
          <div className="text-sm text-muted-foreground">
            Current task: <span className="font-medium">{currentTask.title}</span>
            {currentTask.subject && ` (${currentTask.subject})`}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Focus Presets */}
        <div className="space-y-3">
          <h3 className="font-semibold">Focus Presets</h3>
          <div className="grid grid-cols-2 gap-2">
            {focusPresets.map((preset) => (
              <Button
                key={preset.id}
                variant={activePreset === preset.id ? "default" : "outline"}
                className="h-auto p-3 flex flex-col items-start space-y-1"
                onClick={() => applyFocusPreset(preset.id)}
              >
                <div className="flex items-center space-x-2">
                  {preset.icon}
                  <span className="font-medium">{preset.name}</span>
                </div>
                <span className="text-xs text-left opacity-80">
                  {preset.description}
                </span>
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Ambient Sounds */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Ambient Sounds</h3>
            <div className="flex items-center space-x-2">
              {currentSound && (
                <Button size="sm" variant="outline" onClick={togglePlayPause}>
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
              )}
              {currentSound && (
                <Button size="sm" variant="outline" onClick={stopAmbientSound}>
                  <VolumeX className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          
          {currentSound && (
            <div className="flex items-center space-x-4 p-3 bg-muted rounded-lg">
              <div className="flex items-center space-x-2">
                {getCurrentSoundInfo()?.icon}
                <span className="font-medium">{getCurrentSoundInfo()?.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {isPlaying ? "Playing" : "Paused"}
                </Badge>
              </div>
              <div className="flex-1 flex items-center space-x-2">
                <Volume2 className="h-4 w-4" />
                <Slider
                  value={[volume]}
                  onValueChange={(value) => setVolume(value[0])}
                  max={100}
                  step={5}
                  className="flex-1"
                />
                <span className="text-sm w-8">{volume}%</span>
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-3 gap-2">
            {ambientSounds.map((sound) => (
              <Button
                key={sound.id}
                variant={currentSound === sound.id ? "default" : "outline"}
                size="sm"
                className="flex flex-col items-center space-y-1 h-auto p-3"
                onClick={() => playAmbientSound(sound.id)}
              >
                {sound.icon}
                <span className="text-xs">{sound.name}</span>
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Distraction Blocking */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Distraction Blocking</h3>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {getActiveRulesCount()} rules active
              </Badge>
              {blockedAttempts > 0 && (
                <Badge variant="destructive">
                  {blockedAttempts} blocked
                </Badge>
              )}
              <Button size="sm" variant="outline" onClick={() => setShowSettings(!showSettings)}>
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {showSettings && (
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {distractionRules.map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Switch
                        checked={rule.isActive}
                        onCheckedChange={() => toggleDistractionRule(rule.id)}
                      />
                      <div>
                        <p className="font-medium">{rule.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {rule.severity === 'block' ? '🚫 Block' : 
                           rule.severity === 'warn' ? '⚠️ Warn' : '⏱️ Limit'}
                          {rule.timeLimit && ` (${rule.timeLimit}m)`}
                        </p>
                      </div>
                    </div>
                    <Badge variant={rule.isActive ? "default" : "secondary"}>
                      {rule.type}
                    </Badge>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
          
          {/* Demo button for testing */}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={simulateBlockAttempt}
            className="w-full"
          >
            Test Distraction Block
          </Button>
        </div>

        {/* Status Summary */}
        {isActive && (
          <Card className="p-3 bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Focus mode is active
              </span>
            </div>
            <div className="text-xs text-green-600 dark:text-green-400 mt-1">
              {getActiveRulesCount()} distraction rules enabled
              {currentSound && ` • ${getCurrentSoundInfo()?.name} playing`}
              {activePreset && ` • ${focusPresets.find(p => p.id === activePreset)?.name} preset`}
            </div>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};