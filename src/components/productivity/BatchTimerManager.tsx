import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Separator } from '../ui/separator';
import { 
  Play, 
  Pause, 
  Square, 
  SkipForward, 
  Clock, 
  CheckCircle2,
  AlertCircle,
  Timer,
  List
} from 'lucide-react';
import { useEnhancedTimerStore } from '../../stores/enhancedTimerStore';
import { taskStorage } from '../../utils/taskLocalStorage';
import { EnhancedTodoItem } from '../../types/todo';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { toast } from '../ui/use-toast';

interface BatchTimerSession {
  id: string;
  tasks: EnhancedTodoItem[];
  currentTaskIndex: number;
  totalEstimatedTime: number;
  elapsedTime: number;
  status: 'idle' | 'running' | 'paused' | 'completed';
  startTime?: Date;
  completedTasks: string[];
  settings: {
    autoAdvance: boolean;
    breakBetweenTasks: number; // minutes
    notifyOnTaskComplete: boolean;
  };
}

interface BatchTimerManagerProps {
  selectedTasks: string[];
  onClose: () => void;
}

export const BatchTimerManager: React.FC<BatchTimerManagerProps> = ({
  selectedTasks,
  onClose
}) => {
  const { user } = useSupabaseAuth();
  const { startTimer, pauseTimer, stopTimer, linkTaskToTimer } = useEnhancedTimerStore();
  const [batchSession, setBatchSession] = useState<BatchTimerSession | null>(null);
  const [tasks, setTasks] = useState<EnhancedTodoItem[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize batch session
  useEffect(() => {
    if (!user?.id || selectedTasks.length === 0 || isInitialized) return;

    const loadedTasks = selectedTasks
      .map(taskId => taskStorage.getTask(user.id, taskId))
      .filter(Boolean) as EnhancedTodoItem[];

    if (loadedTasks.length === 0) {
      toast({
        title: "No tasks found",
        description: "The selected tasks could not be loaded.",
        variant: "destructive"
      });
      onClose();
      return;
    }

    const totalEstimatedTime = loadedTasks.reduce((total, task) => 
      total + (task.timeEstimate || 30), 0
    );

    const newBatchSession: BatchTimerSession = {
      id: `batch_${Date.now()}`,
      tasks: loadedTasks,
      currentTaskIndex: 0,
      totalEstimatedTime,
      elapsedTime: 0,
      status: 'idle',
      completedTasks: [],
      settings: {
        autoAdvance: true,
        breakBetweenTasks: 5,
        notifyOnTaskComplete: true
      }
    };

    setBatchSession(newBatchSession);
    setTasks(loadedTasks);
    setIsInitialized(true);
  }, [user?.id, selectedTasks, isInitialized, onClose]);

  const startBatchTimer = async () => {
    if (!batchSession || !user?.id) return;

    try {
      const currentTask = batchSession.tasks[batchSession.currentTaskIndex];
      
      // Link current task to timer
      await linkTaskToTimer(currentTask.id, user.id);
      
      // Start the timer
      await startTimer(currentTask.id, user.id);

      setBatchSession(prev => prev ? {
        ...prev,
        status: 'running',
        startTime: new Date()
      } : null);

      toast({
        title: "Batch timer started",
        description: `Working on: ${currentTask.title}`
      });
    } catch (error) {
      console.error('Error starting batch timer:', error);
      toast({
        title: "Error",
        description: "Failed to start batch timer",
        variant: "destructive"
      });
    }
  };

  const pauseBatchTimer = async () => {
    if (!batchSession) return;

    try {
      await pauseTimer();
      setBatchSession(prev => prev ? { ...prev, status: 'paused' } : null);
      
      toast({
        title: "Batch timer paused",
        description: "Timer has been paused"
      });
    } catch (error) {
      console.error('Error pausing batch timer:', error);
      toast({
        title: "Error",
        description: "Failed to pause batch timer",
        variant: "destructive"
      });
    }
  };

  const stopBatchTimer = async () => {
    if (!batchSession || !user?.id) return;

    try {
      await stopTimer({ userId: user.id });
      setBatchSession(prev => prev ? { ...prev, status: 'idle' } : null);
      
      toast({
        title: "Batch timer stopped",
        description: "Timer session has been ended"
      });
    } catch (error) {
      console.error('Error stopping batch timer:', error);
      toast({
        title: "Error",
        description: "Failed to stop batch timer",
        variant: "destructive"
      });
    }
  };

  const advanceToNextTask = async () => {
    if (!batchSession || !user?.id) return;

    const currentTask = batchSession.tasks[batchSession.currentTaskIndex];
    const nextIndex = batchSession.currentTaskIndex + 1;

    // Mark current task as completed
    const updatedCompletedTasks = [...batchSession.completedTasks, currentTask.id];

    if (nextIndex >= batchSession.tasks.length) {
      // All tasks completed
      await stopBatchTimer();
      setBatchSession(prev => prev ? {
        ...prev,
        status: 'completed',
        completedTasks: updatedCompletedTasks
      } : null);

      toast({
        title: "Batch session completed!",
        description: `Completed ${batchSession.tasks.length} tasks`,
      });
      return;
    }

    // Move to next task
    const nextTask = batchSession.tasks[nextIndex];
    
    try {
      // Stop current timer
      await stopTimer({ userId: user.id });
      
      // Start timer for next task
      await linkTaskToTimer(nextTask.id, user.id);
      await startTimer(nextTask.id, user.id);

      setBatchSession(prev => prev ? {
        ...prev,
        currentTaskIndex: nextIndex,
        completedTasks: updatedCompletedTasks
      } : null);

      toast({
        title: "Advanced to next task",
        description: `Now working on: ${nextTask.title}`
      });

      // Optional break between tasks
      if (batchSession.settings.breakBetweenTasks > 0) {
        toast({
          title: "Take a break!",
          description: `${batchSession.settings.breakBetweenTasks} minute break before next task`,
        });
      }
    } catch (error) {
      console.error('Error advancing to next task:', error);
      toast({
        title: "Error",
        description: "Failed to advance to next task",
        variant: "destructive"
      });
    }
  };

  const skipCurrentTask = async () => {
    if (!batchSession) return;
    await advanceToNextTask();
  };

  if (!batchSession) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Timer className="h-5 w-5 animate-spin" />
            <span>Loading batch timer...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentTask = batchSession.tasks[batchSession.currentTaskIndex];
  const progress = (batchSession.completedTasks.length / batchSession.tasks.length) * 100;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <List className="h-5 w-5" />
            <span>Batch Timer Session</span>
          </CardTitle>
          <Button variant="outline" size="sm" onClick={onClose}>
            Close
          </Button>
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Progress: {batchSession.completedTasks.length} of {batchSession.tasks.length} tasks</span>
            <span>Total estimated: {batchSession.totalEstimatedTime} minutes</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Task */}
        {batchSession.status !== 'completed' && (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">Current Task</Badge>
              <span className="text-sm text-muted-foreground">
                {batchSession.currentTaskIndex + 1} of {batchSession.tasks.length}
              </span>
            </div>
            
            <Card className="border-2 border-primary/20">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <h3 className="font-semibold">{currentTask.title}</h3>
                    {currentTask.description && (
                      <p className="text-sm text-muted-foreground">{currentTask.description}</p>
                    )}
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{currentTask.timeEstimate || 30} min</span>
                      </div>
                      {currentTask.subjectName && (
                        <Badge variant="outline" style={{ backgroundColor: currentTask.subjectColor + '20' }}>
                          {currentTask.subjectName}
                        </Badge>
                      )}
                      <Badge variant={currentTask.priority === 'high' ? 'destructive' : 
                                   currentTask.priority === 'medium' ? 'default' : 'secondary'}>
                        {currentTask.priority}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Timer Controls */}
        <div className="flex items-center justify-center space-x-4">
          {batchSession.status === 'idle' && (
            <Button onClick={startBatchTimer} size="lg" className="px-8">
              <Play className="h-4 w-4 mr-2" />
              Start Batch Timer
            </Button>
          )}
          
          {batchSession.status === 'running' && (
            <>
              <Button onClick={pauseBatchTimer} variant="outline" size="lg">
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
              <Button onClick={stopBatchTimer} variant="destructive" size="lg">
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
              <Button onClick={skipCurrentTask} variant="secondary" size="lg">
                <SkipForward className="h-4 w-4 mr-2" />
                Skip Task
              </Button>
            </>
          )}
          
          {batchSession.status === 'paused' && (
            <>
              <Button onClick={startBatchTimer} size="lg">
                <Play className="h-4 w-4 mr-2" />
                Resume
              </Button>
              <Button onClick={stopBatchTimer} variant="destructive" size="lg">
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            </>
          )}
        </div>

        {/* Task List */}
        <div className="space-y-4">
          <h3 className="font-semibold">Task Queue</h3>
          <div className="space-y-2">
            {batchSession.tasks.map((task, index) => (
              <div
                key={task.id}
                className={`flex items-center justify-between p-3 rounded-lg border ${
                  index === batchSession.currentTaskIndex
                    ? 'border-primary bg-primary/5'
                    : batchSession.completedTasks.includes(task.id)
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'
                    : 'border-border'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {batchSession.completedTasks.includes(task.id) ? (
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  ) : index === batchSession.currentTaskIndex ? (
                    <AlertCircle className="h-5 w-5 text-primary" />
                  ) : (
                    <Clock className="h-5 w-5 text-muted-foreground" />
                  )}
                  <div>
                    <p className="font-medium">{task.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {task.timeEstimate || 30} minutes
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {task.subjectName && (
                    <Badge variant="outline" style={{ backgroundColor: task.subjectColor + '20' }}>
                      {task.subjectName}
                    </Badge>
                  )}
                  <Badge variant={task.priority === 'high' ? 'destructive' : 
                               task.priority === 'medium' ? 'default' : 'secondary'}>
                    {task.priority}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Completion Message */}
        {batchSession.status === 'completed' && (
          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CardContent className="p-4 text-center">
              <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-green-800 dark:text-green-200">
                Batch Session Completed!
              </h3>
              <p className="text-sm text-green-600 dark:text-green-300">
                You've successfully completed {batchSession.tasks.length} tasks in this batch session.
              </p>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};