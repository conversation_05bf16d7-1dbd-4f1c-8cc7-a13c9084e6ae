import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Separator } from '../ui/separator';
import { 
  Coffee, 
  Stretch, 
  Eye, 
  Brain, 
  Heart, 
  Lightbulb, 
  Timer, 
  Play, 
  Pause,
  SkipForward,
  Settings,
  TrendingUp,
  Activity,
  Zap
} from 'lucide-react';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { useEnhancedTimerStore } from '../../stores/enhancedTimerStore';
import { toast } from '../ui/use-toast';
import { cn } from '../../lib/utils';

// Break suggestion interface
interface BreakSuggestion {
  id: string;
  title: string;
  description: string;
  duration: number; // in minutes
  icon: React.ReactNode;
  category: 'physical' | 'mental' | 'visual' | 'creative' | 'social';
  taskTypes: string[]; // Which task types this break is good for
  energyLevel: 'low' | 'medium' | 'high'; // Energy required
  benefits: string[];
  instructions: string[];
}

// Pomodoro customization interface
interface PomodoroCustomization {
  taskType: string;
  workDuration: number; // minutes
  shortBreakDuration: number; // minutes
  longBreakDuration: number; // minutes
  sessionsUntilLongBreak: number;
  preferredBreakTypes: string[];
  adaptiveBreaks: boolean; // Adjust break suggestions based on performance
}

// Task performance data interface
interface TaskPerformanceData {
  taskType: string;
  averageFocusTime: number; // minutes
  optimalSessionLength: number; // minutes
  preferredBreakDuration: number; // minutes
  productivityTrend: 'improving' | 'stable' | 'declining';
  lastUpdated: Date;
}

interface TaskSpecificBreakSuggestionsProps {
  currentTask?: {
    id: string;
    title: string;
    type: string;
    subject?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
  };
  sessionDuration: number; // current session duration in minutes
  isBreakTime: boolean;
  onStartBreak: (suggestion: BreakSuggestion) => void;
  onSkipBreak: () => void;
  onCustomizePomodoro: (customization: PomodoroCustomization) => void;
}

export const TaskSpecificBreakSuggestions: React.FC<TaskSpecificBreakSuggestionsProps> = ({
  currentTask,
  sessionDuration,
  isBreakTime,
  onStartBreak,
  onSkipBreak,
  onCustomizePomodoro
}) => {
  const { user } = useSupabaseAuth();
  const { settings, updateSettings } = useEnhancedTimerStore();
  
  const [breakSuggestions, setBreakSuggestions] = useState<BreakSuggestion[]>([]);
  const [customizations, setCustomizations] = useState<PomodoroCustomization[]>([]);
  const [performanceData, setPerformanceData] = useState<TaskPerformanceData[]>([]);
  const [currentBreak, setCurrentBreak] = useState<BreakSuggestion | null>(null);
  const [breakTimer, setBreakTimer] = useState<number>(0);
  const [isBreakActive, setIsBreakActive] = useState(false);
  const [showCustomization, setShowCustomization] = useState(false);

  // Predefined break suggestions
  const allBreakSuggestions: BreakSuggestion[] = [
    {
      id: 'eye-rest',
      title: '20-20-20 Eye Rest',
      description: 'Look at something 20 feet away for 20 seconds',
      duration: 1,
      icon: <Eye className="h-4 w-4" />,
      category: 'visual',
      taskTypes: ['reading', 'coding', 'writing', 'research'],
      energyLevel: 'low',
      benefits: ['Reduces eye strain', 'Prevents dry eyes', 'Improves focus'],
      instructions: [
        'Look away from your screen',
        'Find an object at least 20 feet away',
        'Focus on it for 20 seconds',
        'Blink several times before returning to work'
      ]
    },
    {
      id: 'desk-stretches',
      title: 'Desk Stretches',
      description: 'Simple stretches you can do at your desk',
      duration: 3,
      icon: <Stretch className="h-4 w-4" />,
      category: 'physical',
      taskTypes: ['all'],
      energyLevel: 'low',
      benefits: ['Relieves muscle tension', 'Improves circulation', 'Reduces stiffness'],
      instructions: [
        'Neck rolls: 5 in each direction',
        'Shoulder shrugs: 10 repetitions',
        'Wrist circles: 10 in each direction',
        'Seated spinal twist: Hold for 15 seconds each side'
      ]
    },
    {
      id: 'breathing-exercise',
      title: 'Deep Breathing',
      description: 'Calm your mind with focused breathing',
      duration: 2,
      icon: <Heart className="h-4 w-4" />,
      category: 'mental',
      taskTypes: ['problem-solving', 'exam-prep', 'creative'],
      energyLevel: 'low',
      benefits: ['Reduces stress', 'Improves focus', 'Increases oxygen flow'],
      instructions: [
        'Sit comfortably with eyes closed',
        'Breathe in slowly for 4 counts',
        'Hold for 4 counts',
        'Exhale slowly for 6 counts',
        'Repeat 5-8 times'
      ]
    },
    {
      id: 'quick-walk',
      title: 'Quick Walk',
      description: 'Take a short walk to refresh your mind',
      duration: 5,
      icon: <Activity className="h-4 w-4" />,
      category: 'physical',
      taskTypes: ['all'],
      energyLevel: 'medium',
      benefits: ['Boosts energy', 'Improves circulation', 'Clears mental fog'],
      instructions: [
        'Step away from your workspace',
        'Walk at a comfortable pace',
        'Focus on your surroundings',
        'Take deep breaths of fresh air',
        'Return feeling refreshed'
      ]
    },
    {
      id: 'brain-teaser',
      title: 'Quick Brain Teaser',
      description: 'Solve a simple puzzle to refresh your mind',
      duration: 3,
      icon: <Brain className="h-4 w-4" />,
      category: 'mental',
      taskTypes: ['creative', 'problem-solving'],
      energyLevel: 'medium',
      benefits: ['Stimulates creativity', 'Improves problem-solving', 'Refreshes perspective'],
      instructions: [
        'Choose a simple riddle or puzzle',
        'Work through it step by step',
        'Don\'t stress if you can\'t solve it',
        'The goal is mental stimulation, not perfection'
      ]
    },
    {
      id: 'hydration-snack',
      title: 'Hydration & Healthy Snack',
      description: 'Fuel your body and brain',
      duration: 4,
      icon: <Coffee className="h-4 w-4" />,
      category: 'physical',
      taskTypes: ['all'],
      energyLevel: 'low',
      benefits: ['Maintains energy levels', 'Improves concentration', 'Prevents fatigue'],
      instructions: [
        'Drink a full glass of water',
        'Choose a healthy snack (nuts, fruit, yogurt)',
        'Eat mindfully and slowly',
        'Avoid sugary or heavy foods'
      ]
    },
    {
      id: 'creative-doodling',
      title: 'Creative Doodling',
      description: 'Let your mind wander with simple drawing',
      duration: 5,
      icon: <Lightbulb className="h-4 w-4" />,
      category: 'creative',
      taskTypes: ['analytical', 'reading', 'memorization'],
      energyLevel: 'low',
      benefits: ['Stimulates creativity', 'Relaxes the mind', 'Improves mood'],
      instructions: [
        'Get a piece of paper and pen',
        'Draw whatever comes to mind',
        'Don\'t worry about artistic quality',
        'Let your thoughts flow freely'
      ]
    },
    {
      id: 'power-nap',
      title: 'Power Nap',
      description: 'A short nap to recharge your energy',
      duration: 10,
      icon: <Zap className="h-4 w-4" />,
      category: 'physical',
      taskTypes: ['intensive-study', 'exam-prep'],
      energyLevel: 'low',
      benefits: ['Restores energy', 'Improves memory consolidation', 'Reduces fatigue'],
      instructions: [
        'Find a quiet, comfortable place',
        'Set an alarm for 10 minutes',
        'Close your eyes and relax',
        'Don\'t worry if you don\'t fall asleep'
      ]
    }
  ];

  // Load data on component mount
  useEffect(() => {
    if (!user?.id) return;
    
    loadCustomizations();
    loadPerformanceData();
    updateBreakSuggestions();
  }, [user?.id, currentTask, sessionDuration]);

  // Update break suggestions when task or session changes
  useEffect(() => {
    updateBreakSuggestions();
  }, [currentTask, sessionDuration, performanceData]);

  const loadCustomizations = () => {
    if (!user?.id) return;
    
    const stored = localStorage.getItem(`pomodoroCustomizations_${user.id}`);
    if (stored) {
      setCustomizations(JSON.parse(stored));
    } else {
      // Create default customizations
      const defaults: PomodoroCustomization[] = [
        {
          taskType: 'reading',
          workDuration: 30,
          shortBreakDuration: 5,
          longBreakDuration: 15,
          sessionsUntilLongBreak: 3,
          preferredBreakTypes: ['eye-rest', 'desk-stretches'],
          adaptiveBreaks: true
        },
        {
          taskType: 'problem-solving',
          workDuration: 25,
          shortBreakDuration: 5,
          longBreakDuration: 20,
          sessionsUntilLongBreak: 4,
          preferredBreakTypes: ['quick-walk', 'breathing-exercise'],
          adaptiveBreaks: true
        },
        {
          taskType: 'creative',
          workDuration: 45,
          shortBreakDuration: 10,
          longBreakDuration: 25,
          sessionsUntilLongBreak: 3,
          preferredBreakTypes: ['creative-doodling', 'quick-walk'],
          adaptiveBreaks: true
        }
      ];
      setCustomizations(defaults);
      saveCustomizations(defaults);
    }
  };

  const loadPerformanceData = () => {
    if (!user?.id) return;
    
    const stored = localStorage.getItem(`taskPerformanceData_${user.id}`);
    if (stored) {
      setPerformanceData(JSON.parse(stored));
    }
  };

  const saveCustomizations = (newCustomizations: PomodoroCustomization[]) => {
    if (!user?.id) return;
    
    localStorage.setItem(`pomodoroCustomizations_${user.id}`, JSON.stringify(newCustomizations));
    setCustomizations(newCustomizations);
  };

  const updateBreakSuggestions = () => {
    if (!currentTask) {
      setBreakSuggestions(allBreakSuggestions.slice(0, 3));
      return;
    }

    // Get task-specific customization
    const customization = customizations.find(c => c.taskType === currentTask.type);
    
    // Filter suggestions based on task type and current session duration
    let filtered = allBreakSuggestions.filter(suggestion => 
      suggestion.taskTypes.includes('all') || 
      suggestion.taskTypes.includes(currentTask.type)
    );

    // Prioritize based on session duration
    if (sessionDuration > 45) {
      // Long session - prioritize physical breaks
      filtered = filtered.sort((a, b) => {
        if (a.category === 'physical' && b.category !== 'physical') return -1;
        if (b.category === 'physical' && a.category !== 'physical') return 1;
        return 0;
      });
    } else if (sessionDuration > 25) {
      // Medium session - prioritize visual and mental breaks
      filtered = filtered.sort((a, b) => {
        const priorityCategories = ['visual', 'mental'];
        const aScore = priorityCategories.includes(a.category) ? 1 : 0;
        const bScore = priorityCategories.includes(b.category) ? 1 : 0;
        return bScore - aScore;
      });
    }

    // Apply customization preferences
    if (customization?.preferredBreakTypes.length) {
      const preferred = filtered.filter(s => customization.preferredBreakTypes.includes(s.id));
      const others = filtered.filter(s => !customization.preferredBreakTypes.includes(s.id));
      filtered = [...preferred, ...others];
    }

    // Consider task difficulty
    if (currentTask.difficulty === 'hard') {
      // For hard tasks, prefer longer, more restorative breaks
      filtered = filtered.sort((a, b) => b.duration - a.duration);
    }

    setBreakSuggestions(filtered.slice(0, 4));
  };

  const startBreak = (suggestion: BreakSuggestion) => {
    setCurrentBreak(suggestion);
    setBreakTimer(suggestion.duration * 60); // Convert to seconds
    setIsBreakActive(true);
    onStartBreak(suggestion);
    
    toast({
      title: "Break started",
      description: `${suggestion.title} - ${suggestion.duration} minutes`
    });
  };

  const getCurrentCustomization = (): PomodoroCustomization | null => {
    if (!currentTask) return null;
    return customizations.find(c => c.taskType === currentTask.type) || null;
  };

  const updateTaskCustomization = (updates: Partial<PomodoroCustomization>) => {
    if (!currentTask) return;
    
    const existing = customizations.find(c => c.taskType === currentTask.type);
    if (existing) {
      const updated = customizations.map(c => 
        c.taskType === currentTask.type ? { ...c, ...updates } : c
      );
      saveCustomizations(updated);
    } else {
      const newCustomization: PomodoroCustomization = {
        taskType: currentTask.type,
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        sessionsUntilLongBreak: 4,
        preferredBreakTypes: [],
        adaptiveBreaks: true,
        ...updates
      };
      saveCustomizations([...customizations, newCustomization]);
    }
    
    // Update timer settings
    if (updates.workDuration || updates.shortBreakDuration || updates.longBreakDuration) {
      updateSettings({
        workDuration: (updates.workDuration || existing?.workDuration || 25) * 60,
        shortBreakDuration: (updates.shortBreakDuration || existing?.shortBreakDuration || 5) * 60,
        longBreakDuration: (updates.longBreakDuration || existing?.longBreakDuration || 15) * 60,
        sessionsUntilLongBreak: updates.sessionsUntilLongBreak || existing?.sessionsUntilLongBreak || 4
      });
    }
  };

  const getOptimalBreakDuration = (): number => {
    if (!currentTask) return 5;
    
    const performance = performanceData.find(p => p.taskType === currentTask.type);
    if (performance) {
      return performance.preferredBreakDuration;
    }
    
    // Default based on session duration
    if (sessionDuration > 45) return 10;
    if (sessionDuration > 25) return 5;
    return 3;
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isBreakTime && !showCustomization) {
    return null;
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Timer className="h-5 w-5" />
            <span>Break Time</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowCustomization(!showCustomization)}
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={onSkipBreak}>
              <SkipForward className="h-4 w-4" />
              Skip Break
            </Button>
          </div>
        </div>
        
        {currentTask && (
          <div className="text-sm text-muted-foreground">
            After {sessionDuration} minutes of {currentTask.type}
            {currentTask.subject && ` • ${currentTask.subject}`}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {showCustomization && (
          <Card className="p-4 border-primary/20">
            <h3 className="font-semibold mb-4">Pomodoro Customization</h3>
            {currentTask && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Work Duration</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <input
                        type="number"
                        min="15"
                        max="90"
                        value={getCurrentCustomization()?.workDuration || 25}
                        onChange={(e) => updateTaskCustomization({ workDuration: parseInt(e.target.value) })}
                        className="w-20 p-2 border rounded text-sm"
                      />
                      <span className="text-sm text-muted-foreground">minutes</span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Short Break</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <input
                        type="number"
                        min="3"
                        max="15"
                        value={getCurrentCustomization()?.shortBreakDuration || 5}
                        onChange={(e) => updateTaskCustomization({ shortBreakDuration: parseInt(e.target.value) })}
                        className="w-20 p-2 border rounded text-sm"
                      />
                      <span className="text-sm text-muted-foreground">minutes</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  Optimal break duration for this task type: {getOptimalBreakDuration()} minutes
                </div>
              </div>
            )}
          </Card>
        )}

        {/* Current Break Timer */}
        {isBreakActive && currentBreak && (
          <Card className="p-4 bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                {currentBreak.icon}
                <span className="font-semibold">{currentBreak.title}</span>
              </div>
              <div className="text-lg font-mono">{formatTime(breakTimer)}</div>
            </div>
            <Progress value={(currentBreak.duration * 60 - breakTimer) / (currentBreak.duration * 60) * 100} />
            <p className="text-sm text-muted-foreground mt-2">{currentBreak.description}</p>
          </Card>
        )}

        {/* Break Suggestions */}
        {!isBreakActive && (
          <div className="space-y-4">
            <h3 className="font-semibold">Suggested Breaks</h3>
            <div className="grid gap-3">
              {breakSuggestions.map((suggestion) => (
                <Card key={suggestion.id} className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 rounded-lg bg-primary/10">
                        {suggestion.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium">{suggestion.title}</h4>
                          <Badge variant="outline" className="text-xs">
                            {suggestion.duration}m
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {suggestion.category}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {suggestion.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {suggestion.benefits.slice(0, 2).map((benefit, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {benefit}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <Button 
                      size="sm"
                      onClick={() => startBreak(suggestion)}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Start
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Break Instructions */}
        {currentBreak && (
          <Card className="p-4">
            <h3 className="font-semibold mb-3">Break Instructions</h3>
            <ol className="space-y-2">
              {currentBreak.instructions.map((instruction, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </span>
                  <span>{instruction}</span>
                </li>
              ))}
            </ol>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};