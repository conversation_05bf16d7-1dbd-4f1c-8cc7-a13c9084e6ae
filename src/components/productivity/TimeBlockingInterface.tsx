import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { 
  Calendar, 
  Clock, 
  Plus, 
  Trash2, 
  Edit3, 
  Play, 
  ChevronLeft, 
  ChevronRight,
  Save,
  X
} from 'lucide-react';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { taskStorage } from '../../utils/taskLocalStorage';
import { EnhancedTodoItem } from '../../types/todo';
import { useEnhancedTimerStore } from '../../stores/enhancedTimerStore';
import { toast } from '../ui/use-toast';
import { cn } from '../../lib/utils';

// Time slot interface
interface TimeSlot {
  id: string;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  taskId?: string;
  taskTitle?: string;
  taskColor?: string;
  isBreak: boolean;
  breakType?: 'short' | 'long' | 'meal';
  date: string; // YYYY-MM-DD format
  status: 'scheduled' | 'active' | 'completed' | 'skipped';
  actualStartTime?: Date;
  actualEndTime?: Date;
}

// Time blocking template interface
interface TimeBlockTemplate {
  id: string;
  name: string;
  description?: string;
  slots: Omit<TimeSlot, 'id' | 'date' | 'status' | 'actualStartTime' | 'actualEndTime'>[];
  isDefault: boolean;
  createdAt: Date;
}

interface TimeBlockingInterfaceProps {
  onClose: () => void;
  selectedDate?: Date;
}

export const TimeBlockingInterface: React.FC<TimeBlockingInterfaceProps> = ({
  onClose,
  selectedDate = new Date()
}) => {
  const { user } = useSupabaseAuth();
  const { startTimer, linkTaskToTimer } = useEnhancedTimerStore();
  
  const [currentDate, setCurrentDate] = useState(selectedDate);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [availableTasks, setAvailableTasks] = useState<EnhancedTodoItem[]>([]);
  const [templates, setTemplates] = useState<TimeBlockTemplate[]>([]);
  const [isEditingSlot, setIsEditingSlot] = useState<string | null>(null);
  const [newSlot, setNewSlot] = useState<Partial<TimeSlot>>({
    startTime: '09:00',
    endTime: '10:00',
    isBreak: false
  });
  const [showTemplates, setShowTemplates] = useState(false);
  const [isCreatingTemplate, setIsCreatingTemplate] = useState(false);
  const [templateName, setTemplateName] = useState('');

  // Load data on component mount
  useEffect(() => {
    if (!user?.id) return;
    
    loadTimeSlots();
    loadAvailableTasks();
    loadTemplates();
  }, [user?.id, currentDate]);

  const loadTimeSlots = () => {
    if (!user?.id) return;
    
    const dateKey = formatDateKey(currentDate);
    const stored = localStorage.getItem(`timeBlocks_${user.id}_${dateKey}`);
    
    if (stored) {
      setTimeSlots(JSON.parse(stored));
    } else {
      setTimeSlots([]);
    }
  };

  const loadAvailableTasks = () => {
    if (!user?.id) return;
    
    const tasks = taskStorage.getAllTasks(user.id);
    const incompleteTasks = tasks.filter(task => 
      task.columnId !== 'done' && task.columnId !== 'completed'
    );
    setAvailableTasks(incompleteTasks);
  };

  const loadTemplates = () => {
    if (!user?.id) return;
    
    const stored = localStorage.getItem(`timeBlockTemplates_${user.id}`);
    if (stored) {
      setTemplates(JSON.parse(stored));
    } else {
      // Create default template
      const defaultTemplate: TimeBlockTemplate = {
        id: 'default',
        name: 'Standard Study Day',
        description: 'A balanced study schedule with regular breaks',
        slots: [
          { startTime: '09:00', endTime: '10:30', isBreak: false },
          { startTime: '10:30', endTime: '10:45', isBreak: true, breakType: 'short' },
          { startTime: '10:45', endTime: '12:15', isBreak: false },
          { startTime: '12:15', endTime: '13:15', isBreak: true, breakType: 'meal' },
          { startTime: '13:15', endTime: '14:45', isBreak: false },
          { startTime: '14:45', endTime: '15:00', isBreak: true, breakType: 'short' },
          { startTime: '15:00', endTime: '16:30', isBreak: false },
          { startTime: '16:30', endTime: '16:45', isBreak: true, breakType: 'short' },
          { startTime: '16:45', endTime: '18:15', isBreak: false }
        ],
        isDefault: true,
        createdAt: new Date()
      };
      setTemplates([defaultTemplate]);
      saveTemplates([defaultTemplate]);
    }
  };

  const saveTimeSlots = (slots: TimeSlot[]) => {
    if (!user?.id) return;
    
    const dateKey = formatDateKey(currentDate);
    localStorage.setItem(`timeBlocks_${user.id}_${dateKey}`, JSON.stringify(slots));
    setTimeSlots(slots);
  };

  const saveTemplates = (newTemplates: TimeBlockTemplate[]) => {
    if (!user?.id) return;
    
    localStorage.setItem(`timeBlockTemplates_${user.id}`, JSON.stringify(newTemplates));
    setTemplates(newTemplates);
  };

  const formatDateKey = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const formatTime = (time: string): string => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const calculateDuration = (startTime: string, endTime: string): number => {
    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(`2000-01-01T${endTime}:00`);
    return (end.getTime() - start.getTime()) / (1000 * 60); // minutes
  };

  const addTimeSlot = () => {
    if (!newSlot.startTime || !newSlot.endTime) {
      toast({
        title: "Invalid time slot",
        description: "Please provide both start and end times",
        variant: "destructive"
      });
      return;
    }

    const slot: TimeSlot = {
      id: `slot_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      startTime: newSlot.startTime!,
      endTime: newSlot.endTime!,
      taskId: newSlot.taskId,
      taskTitle: newSlot.taskId ? availableTasks.find(t => t.id === newSlot.taskId)?.title : undefined,
      taskColor: newSlot.taskId ? availableTasks.find(t => t.id === newSlot.taskId)?.subjectColor : undefined,
      isBreak: newSlot.isBreak || false,
      breakType: newSlot.breakType,
      date: formatDateKey(currentDate),
      status: 'scheduled'
    };

    const updatedSlots = [...timeSlots, slot].sort((a, b) => 
      a.startTime.localeCompare(b.startTime)
    );
    
    saveTimeSlots(updatedSlots);
    setNewSlot({ startTime: '09:00', endTime: '10:00', isBreak: false });
    
    toast({
      title: "Time slot added",
      description: `Added ${slot.isBreak ? 'break' : 'study'} slot from ${formatTime(slot.startTime)} to ${formatTime(slot.endTime)}`
    });
  };

  const deleteTimeSlot = (slotId: string) => {
    const updatedSlots = timeSlots.filter(slot => slot.id !== slotId);
    saveTimeSlots(updatedSlots);
    
    toast({
      title: "Time slot deleted",
      description: "The time slot has been removed from your schedule"
    });
  };

  const updateTimeSlot = (slotId: string, updates: Partial<TimeSlot>) => {
    const updatedSlots = timeSlots.map(slot => 
      slot.id === slotId ? { ...slot, ...updates } : slot
    );
    saveTimeSlots(updatedSlots);
    setIsEditingSlot(null);
  };

  const startTimerForSlot = async (slot: TimeSlot) => {
    if (!user?.id || !slot.taskId) return;

    try {
      await linkTaskToTimer(slot.taskId, user.id);
      await startTimer(slot.taskId, user.id);
      
      // Update slot status
      updateTimeSlot(slot.id, { 
        status: 'active',
        actualStartTime: new Date()
      });
      
      toast({
        title: "Timer started",
        description: `Started timer for: ${slot.taskTitle}`
      });
    } catch (error) {
      console.error('Error starting timer for slot:', error);
      toast({
        title: "Error",
        description: "Failed to start timer for this time slot",
        variant: "destructive"
      });
    }
  };

  const applyTemplate = (template: TimeBlockTemplate) => {
    const newSlots: TimeSlot[] = template.slots.map((slot, index) => ({
      id: `slot_${Date.now()}_${index}`,
      ...slot,
      date: formatDateKey(currentDate),
      status: 'scheduled' as const
    }));
    
    saveTimeSlots(newSlots);
    setShowTemplates(false);
    
    toast({
      title: "Template applied",
      description: `Applied "${template.name}" template to ${currentDate.toLocaleDateString()}`
    });
  };

  const saveAsTemplate = () => {
    if (!templateName.trim()) {
      toast({
        title: "Template name required",
        description: "Please provide a name for the template",
        variant: "destructive"
      });
      return;
    }

    const template: TimeBlockTemplate = {
      id: `template_${Date.now()}`,
      name: templateName,
      slots: timeSlots.map(({ id, date, status, actualStartTime, actualEndTime, ...slot }) => slot),
      isDefault: false,
      createdAt: new Date()
    };

    const updatedTemplates = [...templates, template];
    saveTemplates(updatedTemplates);
    setIsCreatingTemplate(false);
    setTemplateName('');
    
    toast({
      title: "Template saved",
      description: `Saved current schedule as "${template.name}"`
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const getTotalStudyTime = (): number => {
    return timeSlots
      .filter(slot => !slot.isBreak)
      .reduce((total, slot) => total + calculateDuration(slot.startTime, slot.endTime), 0);
  };

  const getSlotStatusColor = (status: TimeSlot['status']): string => {
    switch (status) {
      case 'active': return 'bg-green-100 border-green-300 dark:bg-green-900/20 dark:border-green-700';
      case 'completed': return 'bg-blue-100 border-blue-300 dark:bg-blue-900/20 dark:border-blue-700';
      case 'skipped': return 'bg-gray-100 border-gray-300 dark:bg-gray-900/20 dark:border-gray-700';
      default: return 'bg-background border-border';
    }
  };

  return (
    <Card className="w-full max-w-6xl mx-auto h-[80vh]">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Time Blocking</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => setShowTemplates(!showTemplates)}>
              Templates
            </Button>
            <Button variant="outline" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Date Navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={() => navigateDate('prev')}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h3 className="text-lg font-semibold">
              {currentDate.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </h3>
            <Button variant="outline" size="sm" onClick={() => navigateDate('next')}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <span>Total Study Time: {Math.floor(getTotalStudyTime() / 60)}h {getTotalStudyTime() % 60}m</span>
            <span>Slots: {timeSlots.length}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Time Slots List */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Schedule</h3>
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setIsCreatingTemplate(true)}
                  disabled={timeSlots.length === 0}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save as Template
                </Button>
              </div>
            </div>
            
            <ScrollArea className="h-96">
              <div className="space-y-2">
                {timeSlots.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No time slots scheduled for this day</p>
                    <p className="text-sm">Add slots manually or apply a template</p>
                  </div>
                ) : (
                  timeSlots.map((slot) => (
                    <Card key={slot.id} className={cn("p-4", getSlotStatusColor(slot.status))}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="text-sm font-mono">
                            {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                          </div>
                          <div className="flex items-center space-x-2">
                            {slot.isBreak ? (
                              <Badge variant="secondary">
                                {slot.breakType === 'meal' ? '🍽️' : '☕'} Break
                              </Badge>
                            ) : (
                              <>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">
                                  {slot.taskTitle || 'Unassigned Study Time'}
                                </span>
                                {slot.taskColor && (
                                  <div 
                                    className="w-3 h-3 rounded-full" 
                                    style={{ backgroundColor: slot.taskColor }}
                                  />
                                )}
                              </>
                            )}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {calculateDuration(slot.startTime, slot.endTime)}m
                          </Badge>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {!slot.isBreak && slot.taskId && slot.status === 'scheduled' && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => startTimerForSlot(slot)}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                          )}
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={() => setIsEditingSlot(slot.id)}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={() => deleteTimeSlot(slot.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>

          {/* Add New Slot / Templates Panel */}
          <div className="space-y-4">
            {showTemplates ? (
              <Card className="p-4">
                <h3 className="font-semibold mb-4">Templates</h3>
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {templates.map((template) => (
                      <Card key={template.id} className="p-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{template.name}</h4>
                            {template.isDefault && (
                              <Badge variant="secondary" className="text-xs">Default</Badge>
                            )}
                          </div>
                          {template.description && (
                            <p className="text-sm text-muted-foreground">{template.description}</p>
                          )}
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{template.slots.length} slots</span>
                            <span>
                              {Math.floor(template.slots.reduce((total, slot) => 
                                total + (slot.isBreak ? 0 : calculateDuration(slot.startTime, slot.endTime)), 0
                              ) / 60)}h study time
                            </span>
                          </div>
                          <Button 
                            size="sm" 
                            className="w-full"
                            onClick={() => applyTemplate(template)}
                          >
                            Apply Template
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </Card>
            ) : (
              <Card className="p-4">
                <h3 className="font-semibold mb-4">Add Time Slot</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="startTime">Start Time</Label>
                      <Input
                        id="startTime"
                        type="time"
                        value={newSlot.startTime}
                        onChange={(e) => setNewSlot({ ...newSlot, startTime: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="endTime">End Time</Label>
                      <Input
                        id="endTime"
                        type="time"
                        value={newSlot.endTime}
                        onChange={(e) => setNewSlot({ ...newSlot, endTime: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Type</Label>
                    <div className="flex space-x-2">
                      <Button
                        variant={!newSlot.isBreak ? "default" : "outline"}
                        size="sm"
                        onClick={() => setNewSlot({ ...newSlot, isBreak: false, breakType: undefined })}
                      >
                        Study
                      </Button>
                      <Button
                        variant={newSlot.isBreak ? "default" : "outline"}
                        size="sm"
                        onClick={() => setNewSlot({ ...newSlot, isBreak: true })}
                      >
                        Break
                      </Button>
                    </div>
                  </div>

                  {!newSlot.isBreak && (
                    <div>
                      <Label htmlFor="taskSelect">Task (Optional)</Label>
                      <select
                        id="taskSelect"
                        className="w-full p-2 border rounded-md bg-background"
                        value={newSlot.taskId || ''}
                        onChange={(e) => setNewSlot({ ...newSlot, taskId: e.target.value || undefined })}
                      >
                        <option value="">Select a task...</option>
                        {availableTasks.map((task) => (
                          <option key={task.id} value={task.id}>
                            {task.title} {task.subjectName && `(${task.subjectName})`}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {newSlot.isBreak && (
                    <div>
                      <Label>Break Type</Label>
                      <div className="flex space-x-2">
                        <Button
                          variant={newSlot.breakType === 'short' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setNewSlot({ ...newSlot, breakType: 'short' })}
                        >
                          Short
                        </Button>
                        <Button
                          variant={newSlot.breakType === 'long' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setNewSlot({ ...newSlot, breakType: 'long' })}
                        >
                          Long
                        </Button>
                        <Button
                          variant={newSlot.breakType === 'meal' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setNewSlot({ ...newSlot, breakType: 'meal' })}
                        >
                          Meal
                        </Button>
                      </div>
                    </div>
                  )}

                  <Button onClick={addTimeSlot} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Time Slot
                  </Button>
                </div>
              </Card>
            )}

            {/* Save Template Modal */}
            {isCreatingTemplate && (
              <Card className="p-4 border-primary">
                <h3 className="font-semibold mb-4">Save as Template</h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="templateName">Template Name</Label>
                    <Input
                      id="templateName"
                      value={templateName}
                      onChange={(e) => setTemplateName(e.target.value)}
                      placeholder="e.g., My Study Schedule"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button onClick={saveAsTemplate} className="flex-1">
                      Save Template
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setIsCreatingTemplate(false);
                        setTemplateName('');
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};