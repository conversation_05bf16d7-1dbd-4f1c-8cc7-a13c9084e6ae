import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { 
  Brain, 
  Clock, 
  AlertTriangle, 
  TrendingUp, 
  Target,
  Lightbulb,
  Calendar,
  Timer,
  Star,
  ChevronRight,
  RefreshCw
} from 'lucide-react';
import { 
  aiTaskRecommendationEngine, 
  TaskRecommendation, 
  TimeSlot, 
  BreakSuggestion 
} from '../../services/AITaskRecommendationEngine';
import { taskTimerIntegration } from '../../services/TaskTimerIntegrationService';
import { useEnhancedTodoStore } from '../../stores/enhancedTodoStore';
import { EnhancedTodoItem } from '../../types/todo';

interface AITaskRecommendationsProps {
  userId: string;
  currentTask?: EnhancedTodoItem;
  sessionDuration?: number; // in minutes
  availableTime?: number; // in minutes
  onTaskSelect?: (task: EnhancedTodoItem) => void;
  onStartTimer?: (taskId: string) => void;
  className?: string;
}

export const AITaskRecommendations: React.FC<AITaskRecommendationsProps> = ({
  userId,
  currentTask,
  sessionDuration = 0,
  availableTime = 60,
  onTaskSelect,
  onStartTimer,
  className = ''
}) => {
  const [recommendations, setRecommendations] = useState<TaskRecommendation[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [breakSuggestions, setBreakSuggestions] = useState<BreakSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'recommendations' | 'timeBlocks' | 'breaks'>('recommendations');
  
  const { board } = useEnhancedTodoStore();

  // Load recommendations on component mount and when dependencies change
  useEffect(() => {
    loadRecommendations();
  }, [userId, availableTime]);

  // Update break suggestions when current task or session duration changes
  useEffect(() => {
    if (currentTask && sessionDuration > 0) {
      const suggestions = aiTaskRecommendationEngine.getBreakSuggestions(
        currentTask,
        sessionDuration,
        userId
      );
      setBreakSuggestions(suggestions);
    }
  }, [currentTask, sessionDuration, userId]);

  const loadRecommendations = async () => {
    setIsLoading(true);
    try {
      // Get task recommendations
      const taskRecommendations = await aiTaskRecommendationEngine.getTaskRecommendations(
        userId,
        new Date(),
        availableTime,
        'productivity'
      );
      setRecommendations(taskRecommendations);

      // Get optimal time blocks
      const optimalTimeBlocks = await aiTaskRecommendationEngine.getOptimalTimeBlocks(
        userId,
        new Date(),
        { start: 9, end: 17 }
      );
      setTimeSlots(optimalTimeBlocks);

    } catch (error) {
      console.error('Error loading AI recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTaskSelect = (recommendation: TaskRecommendation) => {
    if (onTaskSelect) {
      onTaskSelect(recommendation.task);
    }
  };

  const handleStartTimer = async (taskId: string) => {
    try {
      if (onStartTimer) {
        onStartTimer(taskId);
      } else {
        // Default timer start behavior
        await taskTimerIntegration.startTimerForTask(taskId, userId);
      }
    } catch (error) {
      console.error('Error starting timer:', error);
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'medium': return <Target className="w-4 h-4 text-yellow-500" />;
      case 'low': return <Clock className="w-4 h-4 text-green-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const formatTimeSlot = (timeSlot: TimeSlot) => {
    const start = timeSlot.startTime.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
    const end = timeSlot.endTime.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
    return `${start} - ${end}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-purple-500" />
          <h3 className="text-lg font-semibold">AI Recommendations</h3>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={loadRecommendations}
          disabled={isLoading}
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('recommendations')}
          className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'recommendations'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
          }`}
        >
          <div className="flex items-center justify-center gap-2">
            <Target className="w-4 h-4" />
            Tasks
          </div>
        </button>
        <button
          onClick={() => setActiveTab('timeBlocks')}
          className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'timeBlocks'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
          }`}
        >
          <div className="flex items-center justify-center gap-2">
            <Calendar className="w-4 h-4" />
            Time Blocks
          </div>
        </button>
        <button
          onClick={() => setActiveTab('breaks')}
          className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'breaks'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
          }`}
        >
          <div className="flex items-center justify-center gap-2">
            <Lightbulb className="w-4 h-4" />
            Breaks
            {breakSuggestions.length > 0 && (
              <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                {breakSuggestions.length}
              </Badge>
            )}
          </div>
        </button>
      </div>

      {/* Content */}
      <div className="space-y-3">
        {/* Task Recommendations Tab */}
        {activeTab === 'recommendations' && (
          <div className="space-y-3">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600 dark:text-gray-400">
                  Analyzing your patterns...
                </span>
              </div>
            ) : recommendations.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <Brain className="w-12 h-12 text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 text-center">
                    No task recommendations available.
                    <br />
                    Complete some tasks to help AI learn your patterns.
                  </p>
                </CardContent>
              </Card>
            ) : (
              recommendations.slice(0, 5).map((recommendation) => (
                <Card 
                  key={recommendation.taskId} 
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleTaskSelect(recommendation)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          {getPriorityIcon(recommendation.task.priority)}
                          <h4 className="font-medium text-sm truncate">
                            {recommendation.task.title}
                          </h4>
                          <div className="flex items-center gap-1">
                            <div className={`w-2 h-2 rounded-full ${getUrgencyColor(recommendation.urgency)}`} />
                            <Badge variant="outline" className="text-xs">
                              {recommendation.urgency}
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                          {recommendation.reason}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Timer className="w-3 h-3" />
                            {formatDuration(recommendation.estimatedDuration)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="w-3 h-3" />
                            {Math.round(recommendation.score)}% match
                          </div>
                          <div className="flex items-center gap-1">
                            <TrendingUp className="w-3 h-3" />
                            {recommendation.confidence}% confidence
                          </div>
                        </div>

                        {recommendation.task.completionPercentage > 0 && (
                          <div className="mt-2">
                            <Progress 
                              value={recommendation.task.completionPercentage} 
                              className="h-1"
                            />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStartTimer(recommendation.taskId);
                                }}
                              >
                                <Timer className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              Start timer for this task
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Time Blocks Tab */}
        {activeTab === 'timeBlocks' && (
          <div className="space-y-3">
            {timeSlots.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 text-center">
                    No optimal time blocks available.
                    <br />
                    Complete study sessions to help AI learn your patterns.
                  </p>
                </CardContent>
              </Card>
            ) : (
              timeSlots.slice(0, 6).map((timeSlot, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex flex-col items-center">
                          <Clock className="w-5 h-5 text-blue-500 mb-1" />
                          <Badge variant="outline" className="text-xs">
                            {timeSlot.type}
                          </Badge>
                        </div>
                        <div>
                          <p className="font-medium text-sm">
                            {formatTimeSlot(timeSlot)}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            Optimal for focused work
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <p className="text-sm font-medium text-green-600">
                            {Math.round(timeSlot.productivityScore)}%
                          </p>
                          <p className="text-xs text-gray-500">productivity</p>
                        </div>
                        <Progress 
                          value={timeSlot.productivityScore} 
                          className="w-16 h-2"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Break Suggestions Tab */}
        {activeTab === 'breaks' && (
          <div className="space-y-3">
            {breakSuggestions.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <Lightbulb className="w-12 h-12 text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400 text-center">
                    No break suggestions at the moment.
                    <br />
                    Keep working and we'll suggest breaks when needed.
                  </p>
                </CardContent>
              </Card>
            ) : (
              breakSuggestions.map((suggestion, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex flex-col items-center">
                        <Lightbulb className="w-5 h-5 text-yellow-500 mb-1" />
                        <Badge 
                          variant={suggestion.type === 'long' ? 'default' : 'outline'} 
                          className="text-xs"
                        >
                          {suggestion.type}
                        </Badge>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium text-sm">
                            {suggestion.type === 'short' ? 'Short Break' :
                             suggestion.type === 'long' ? 'Long Break' : 'Task Switch'}
                          </h4>
                          {suggestion.duration > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {suggestion.duration}m
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                          {suggestion.reason}
                        </p>
                        
                        <div className="flex flex-wrap gap-1">
                          {suggestion.activities.map((activity, activityIndex) => (
                            <Badge 
                              key={activityIndex} 
                              variant="outline" 
                              className="text-xs"
                            >
                              {activity}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AITaskRecommendations;