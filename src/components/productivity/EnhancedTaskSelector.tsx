import { useState, useEffect, useMemo } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useSupabaseTodoStore } from '@/stores/supabaseTodoStore';
import { useSupabaseSubjectStore } from '@/stores/supabaseSubjectStore';
import { EnhancedTodoItem } from '@/types/todo';
import { taskTimerIntegration } from '@/services/TaskTimerIntegrationService';
import { useAIRecommendations } from '@/hooks/useAIRecommendations';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Plus,
  Clock,
  Calendar,
  Flag,
  BookOpen,
  ChevronDown,
  Timer,
  Play,
  CheckCircle2,
  Circle,
  AlertCircle,
  Target,
  Brain,
  Star,
  TrendingUp,
  Lightbulb,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, isToday, isTomorrow, isPast } from 'date-fns';
import { QuickTaskCreationModal } from './QuickTaskCreationModal';

interface EnhancedTaskSelectorProps {
  onTaskSelect: (task: EnhancedTodoItem) => void;
  selectedTaskId?: string;
  className?: string;
}

interface TaskFilters {
  search: string;
  subject: string;
  priority: string;
  status: string;
  showCompleted: boolean;
}

export function EnhancedTaskSelector({
  onTaskSelect,
  selectedTaskId,
  className,
}: EnhancedTaskSelectorProps) {
  const { user } = useSupabaseAuth();
  const { board, fetchTodos, loading } = useSupabaseTodoStore();
  const { subjects, fetchSubjects } = useSupabaseSubjectStore();

  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'recommended'>('recommended');
  const [filters, setFilters] = useState<TaskFilters>({
    search: '',
    subject: 'all',
    priority: 'all',
    status: 'all',
    showCompleted: false,
  });

  const [showCreateModal, setShowCreateModal] = useState(false);

  // AI Recommendations hook
  const {
    recommendations,
    isLoading: aiLoading,
    getTaskRecommendation,
    refreshRecommendations,
  } = useAIRecommendations({
    userId: user?.id || '',
    availableTime: 60,
    autoRefresh: true,
  });

  // Fetch data when component mounts or user changes
  useEffect(() => {
    if (user) {
      fetchTodos(user.id);
      fetchSubjects(user.id);
    }
  }, [user, fetchTodos, fetchSubjects]);

  // Get all tasks as array
  const allTasks = useMemo(() => {
    return Object.values(board.tasks);
  }, [board.tasks]);

  // Get filtered and sorted tasks
  const filteredTasks = useMemo(() => {
    let filtered = allTasks;

    // Filter by search
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (task) =>
          task.title.toLowerCase().includes(searchLower) ||
          task.description.toLowerCase().includes(searchLower)
      );
    }

    // Filter by subject
    if (filters.subject !== 'all') {
      filtered = filtered.filter((task) => task.subjectId === filters.subject);
    }

    // Filter by priority
    if (filters.priority !== 'all') {
      filtered = filtered.filter((task) => task.priority === filters.priority);
    }

    // Filter by status (column)
    if (filters.status !== 'all') {
      const column = board.columns[filters.status];
      if (column) {
        filtered = filtered.filter((task) => column.taskIds.includes(task.id));
      }
    }

    // Filter completed tasks
    if (!filters.showCompleted) {
      filtered = filtered.filter((task) => !board.columns['column-3']?.taskIds.includes(task.id));
    }

    // Sort by priority and due date
    return filtered.sort((a, b) => {
      // First by priority
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by due date (overdue first, then soonest)
      if (a.dueDate && b.dueDate) {
        return a.dueDate - b.dueDate;
      }
      if (a.dueDate) return -1;
      if (b.dueDate) return 1;

      // Finally by creation date (newest first)
      return b.createdAt - a.createdAt;
    });
  }, [allTasks, filters, board.columns]);

  // Get selected task details
  const selectedTask = selectedTaskId ? board.tasks[selectedTaskId] : null;

  // Get subject by ID
  const getSubject = (subjectId?: string) => {
    return subjects.find((s) => s.id === subjectId);
  };

  // Get task status
  const getTaskStatus = (taskId: string) => {
    for (const [columnId, column] of Object.entries(board.columns)) {
      if (column.taskIds.includes(taskId)) {
        return { columnId, title: column.title };
      }
    }
    return { columnId: 'column-1', title: 'Todo' };
  };

  // Format due date
  const formatDueDate = (dueDate?: number) => {
    if (!dueDate) return null;
    const date = new Date(dueDate);
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    return format(date, 'MMM d');
  };

  // Check if task is overdue
  const isOverdue = (dueDate?: number) => {
    if (!dueDate) return false;
    return isPast(new Date(dueDate)) && !isToday(new Date(dueDate));
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 dark:text-red-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'low':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-muted-foreground';
    }
  };

  // Get urgency color for AI recommendations
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  // Format duration for display
  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  // Handle task selection
  const handleTaskSelect = async (task: EnhancedTodoItem) => {
    try {
      onTaskSelect(task);
      setIsOpen(false);
    } catch (error) {
      console.error('Error selecting task:', error);
    }
  };

  // Start timer for task
  const handleStartTimer = async (task: EnhancedTodoItem, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!user) return;

    try {
      await taskTimerIntegration.startTimerForTask(task.id, user.id);
      onTaskSelect(task);
      setIsOpen(false);
    } catch (error) {
      console.error('Error starting timer for task:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full max-w-md mx-auto justify-between h-12",
            "bg-background/50 dark:bg-white/5 backdrop-blur-sm",
            "border-border dark:border-white/10",
            "hover:bg-background/80 dark:hover:bg-white/10",
            "shadow-lg hover:shadow-xl transition-all duration-300",
            className
          )}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {selectedTask ? (
              <>
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{
                    backgroundColor: getSubject(selectedTask.subjectId)?.color || '#3B82F6',
                  }}
                />
                <span className="truncate font-medium">{selectedTask.title}</span>
                <Badge
                  variant="secondary"
                  className={cn("text-xs", getPriorityColor(selectedTask.priority))}
                >
                  {selectedTask.priority}
                </Badge>
              </>
            ) : (
              <>
                <Target className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">Select a task to focus on</span>
              </>
            )}
          </div>
          <ChevronDown className="w-4 h-4 text-muted-foreground flex-shrink-0" />
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[80vh] p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Select Task to Focus On
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'all' | 'recommended')} className="px-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="recommended" className="flex items-center gap-2">
              <Brain className="w-4 h-4" />
              AI Recommended
              {recommendations.length > 0 && (
                <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                  {recommendations.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="all" className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              All Tasks
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {activeTab === 'all' && (
          <div className="px-6 pb-4 space-y-4">
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search tasks..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2">
                <Select
                  value={filters.subject}
                  onValueChange={(value) => setFilters({ ...filters, subject: value })}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Subject" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Subjects</SelectItem>
                    {subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id}>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: subject.color }}
                          />
                          {subject.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.priority}
                  onValueChange={(value) => setFilters({ ...filters, priority: value })}
                >
                  <SelectTrigger className="w-28">
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={filters.status}
                  onValueChange={(value) => setFilters({ ...filters, status: value })}
                >
                  <SelectTrigger className="w-28">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {Object.entries(board.columns).map(([columnId, column]) => (
                      <SelectItem key={columnId} value={columnId}>
                        {column.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* Task List */}
        <ScrollArea className="flex-1 px-6 pb-6">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'all' | 'recommended')}>
            <TabsContent value="recommended" className="mt-0">
              {aiLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                  <span className="ml-2 text-sm text-muted-foreground">Analyzing your patterns...</span>
                </div>
              ) : recommendations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No AI recommendations yet</p>
                  <p className="text-sm">Complete some tasks to help AI learn your patterns</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={refreshRecommendations}
                  >
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Refresh Recommendations
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {recommendations.slice(0, 8).map((recommendation) => {
                    const task = recommendation.task;
                    const subject = getSubject(task.subjectId);
                    const status = getTaskStatus(task.id);
                    const dueDate = formatDueDate(task.dueDate);
                    const overdue = isOverdue(task.dueDate);
                    const isActive = taskTimerIntegration.isTaskTimerActive(task.id);

                    return (
                      <Card
                        key={task.id}
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:shadow-md",
                          "border-border dark:border-white/10",
                          selectedTaskId === task.id && "ring-2 ring-primary",
                          isActive && "bg-primary/5 border-primary/20",
                          "relative overflow-hidden"
                        )}
                        onClick={() => handleTaskSelect(task)}
                      >
                        {/* AI Recommendation Indicator */}
                        <div className="absolute top-0 right-0 bg-gradient-to-l from-purple-500/20 to-transparent w-16 h-full" />
                        
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2">
                                {/* AI Score Badge */}
                                <Badge variant="secondary" className="bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
                                  <Brain className="w-3 h-3 mr-1" />
                                  {Math.round(recommendation.score)}%
                                </Badge>

                                {/* Urgency Indicator */}
                                <div className={`w-2 h-2 rounded-full ${getUrgencyColor(recommendation.urgency)}`} />

                                {/* Status Icon */}
                                {status.columnId === 'column-3' ? (
                                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                                ) : status.columnId === 'column-2' ? (
                                  <Circle className="w-4 h-4 text-blue-500" />
                                ) : (
                                  <Circle className="w-4 h-4 text-muted-foreground" />
                                )}

                                {/* Subject Color */}
                                {subject && (
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: subject.color }}
                                  />
                                )}

                                {/* Task Title */}
                                <h3 className="font-medium truncate">{task.title}</h3>

                                {/* Active Timer Indicator */}
                                {isActive && (
                                  <Badge variant="secondary" className="text-xs bg-primary/10 text-primary">
                                    <Timer className="w-3 h-3 mr-1" />
                                    Active
                                  </Badge>
                                )}
                              </div>

                              {/* AI Recommendation Reason */}
                              <p className="text-xs text-purple-600 dark:text-purple-400 mb-2 font-medium">
                                <Lightbulb className="w-3 h-3 inline mr-1" />
                                {recommendation.reason}
                              </p>

                              {/* Task Description */}
                              {task.description && (
                                <p className="text-sm text-muted-foreground mb-2 line-clamp-1">
                                  {task.description}
                                </p>
                              )}

                              {/* Task Metadata */}
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                {/* Priority */}
                                <div className="flex items-center gap-1">
                                  <Flag className={cn("w-3 h-3", getPriorityColor(task.priority))} />
                                  <span className={getPriorityColor(task.priority)}>
                                    {task.priority}
                                  </span>
                                </div>

                                {/* Estimated Duration */}
                                <div className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  <span>{formatDuration(recommendation.estimatedDuration)}</span>
                                </div>

                                {/* Confidence */}
                                <div className="flex items-center gap-1">
                                  <Star className="w-3 h-3" />
                                  <span>{recommendation.confidence}% confidence</span>
                                </div>

                                {/* Subject */}
                                {subject && (
                                  <div className="flex items-center gap-1">
                                    <BookOpen className="w-3 h-3" />
                                    <span>{subject.name}</span>
                                  </div>
                                )}

                                {/* Due Date */}
                                {dueDate && (
                                  <div className="flex items-center gap-1">
                                    <Calendar className={cn("w-3 h-3", overdue && "text-red-500")} />
                                    <span className={cn(overdue && "text-red-500")}>
                                      {dueDate}
                                    </span>
                                    {overdue && <AlertCircle className="w-3 h-3 text-red-500" />}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Action Button */}
                            <Button
                              size="sm"
                              variant={isActive ? "secondary" : "default"}
                              onClick={(e) => handleStartTimer(task, e)}
                              className="flex-shrink-0"
                              disabled={isActive}
                            >
                              <Play className="w-3 h-3 mr-1" />
                              {isActive ? "Active" : "Start"}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </TabsContent>

            <TabsContent value="all" className="mt-0">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                </div>
              ) : filteredTasks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Target className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No tasks found</p>
                  <p className="text-sm">Try adjusting your filters or create a new task</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredTasks.map((task) => {
                    const subject = getSubject(task.subjectId);
                    const status = getTaskStatus(task.id);
                    const dueDate = formatDueDate(task.dueDate);
                    const overdue = isOverdue(task.dueDate);
                    const isActive = taskTimerIntegration.isTaskTimerActive(task.id);
                    const aiRecommendation = getTaskRecommendation(task.id);

                    return (
                      <Card
                        key={task.id}
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:shadow-md",
                          "border-border dark:border-white/10",
                          selectedTaskId === task.id && "ring-2 ring-primary",
                          isActive && "bg-primary/5 border-primary/20",
                          aiRecommendation && "relative overflow-hidden"
                        )}
                        onClick={() => handleTaskSelect(task)}
                      >
                        {/* AI Recommendation Indicator for All Tasks */}
                        {aiRecommendation && (
                          <div className="absolute top-0 right-0 bg-gradient-to-l from-purple-500/10 to-transparent w-12 h-full" />
                        )}

                        <CardContent className="p-4">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2">
                                {/* AI Recommendation Badge */}
                                {aiRecommendation && (
                                  <Badge variant="outline" className="text-xs border-purple-200 text-purple-600 dark:border-purple-800 dark:text-purple-400">
                                    <Brain className="w-3 h-3 mr-1" />
                                    {Math.round(aiRecommendation.score)}%
                                  </Badge>
                                )}

                                {/* Status Icon */}
                                {status.columnId === 'column-3' ? (
                                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                                ) : status.columnId === 'column-2' ? (
                                  <Circle className="w-4 h-4 text-blue-500" />
                                ) : (
                                  <Circle className="w-4 h-4 text-muted-foreground" />
                                )}

                                {/* Subject Color */}
                                {subject && (
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: subject.color }}
                                  />
                                )}

                                {/* Task Title */}
                                <h3 className="font-medium truncate">{task.title}</h3>

                                {/* Active Timer Indicator */}
                                {isActive && (
                                  <Badge variant="secondary" className="text-xs bg-primary/10 text-primary">
                                    <Timer className="w-3 h-3 mr-1" />
                                    Active
                                  </Badge>
                                )}
                              </div>

                              {/* Task Description */}
                              {task.description && (
                                <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                                  {task.description}
                                </p>
                              )}

                              {/* Task Metadata */}
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                {/* Priority */}
                                <div className="flex items-center gap-1">
                                  <Flag className={cn("w-3 h-3", getPriorityColor(task.priority))} />
                                  <span className={getPriorityColor(task.priority)}>
                                    {task.priority}
                                  </span>
                                </div>

                                {/* Subject */}
                                {subject && (
                                  <div className="flex items-center gap-1">
                                    <BookOpen className="w-3 h-3" />
                                    <span>{subject.name}</span>
                                  </div>
                                )}

                                {/* Due Date */}
                                {dueDate && (
                                  <div className="flex items-center gap-1">
                                    <Calendar className={cn("w-3 h-3", overdue && "text-red-500")} />
                                    <span className={cn(overdue && "text-red-500")}>
                                      {dueDate}
                                    </span>
                                    {overdue && <AlertCircle className="w-3 h-3 text-red-500" />}
                                  </div>
                                )}

                                {/* Time Estimate */}
                                {task.timeEstimate && (
                                  <div className="flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    <span>{task.timeEstimate}m</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Action Button */}
                            <Button
                              size="sm"
                              variant={isActive ? "secondary" : "outline"}
                              onClick={(e) => handleStartTimer(task, e)}
                              className="flex-shrink-0"
                              disabled={isActive}
                            >
                              <Play className="w-3 h-3 mr-1" />
                              {isActive ? "Active" : "Start"}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </ScrollArea>

        <Separator />

        {/* Quick Actions */}
        <div className="p-6 pt-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              setIsOpen(false);
              setShowCreateModal(true);
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create New Task
          </Button>
        </div>
      </DialogContent>

      {/* Quick Task Creation Modal */}
      <QuickTaskCreationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onTaskCreated={(taskId) => {
          // Refresh tasks and potentially select the new task
          if (user) {
            fetchTodos(user.id);
          }
        }}
        preselectedSubject={filters.subject !== 'all' ? filters.subject : undefined}
      />
    </Dialog>
  );
}