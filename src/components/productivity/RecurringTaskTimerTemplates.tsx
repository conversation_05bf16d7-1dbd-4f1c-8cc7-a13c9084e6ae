import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { 
  Repeat, 
  Clock, 
  Plus, 
  Edit3, 
  Trash2, 
  Play, 
  Save,
  Copy,
  Calendar,
  Settings,
  Timer,
  Target
} from 'lucide-react';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { useEnhancedTimerStore } from '../../stores/enhancedTimerStore';
import { taskStorage } from '../../utils/taskLocalStorage';
import { EnhancedTodoItem } from '../../types/todo';
import { toast } from '../ui/use-toast';
import { cn } from '../../lib/utils';

// Recurring timer template interface
interface RecurringTimerTemplate {
  id: string;
  name: string;
  description?: string;
  taskPattern: string; // Pattern to match task titles/types
  timerSettings: {
    workDuration: number; // minutes
    shortBreakDuration: number; // minutes
    longBreakDuration: number; // minutes
    sessionsUntilLongBreak: number;
    autoStart: boolean;
    autoAdvanceBreaks: boolean;
  };
  schedule: {
    frequency: 'daily' | 'weekly' | 'custom';
    daysOfWeek?: number[]; // 0-6, Sunday = 0
    timeOfDay?: string; // HH:MM format
    duration?: number; // Total session duration in minutes
  };
  focusSettings: {
    ambientSound?: string;
    distractionBlocking: boolean;
    breakSuggestions: string[];
  };
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date;
  usageCount: number;
}// Te
mplate usage statistics interface
interface TemplateUsageStats {
  templateId: string;
  totalSessions: number;
  totalTime: number; // minutes
  averageProductivity: number; // 1-5 rating
  completionRate: number; // percentage
  lastWeekUsage: number;
  trend: 'increasing' | 'stable' | 'decreasing';
}

interface RecurringTaskTimerTemplatesProps {
  onClose: () => void;
  onApplyTemplate: (template: RecurringTimerTemplate) => void;
  currentTask?: EnhancedTodoItem;
}

export const RecurringTaskTimerTemplates: React.FC<RecurringTaskTimerTemplatesProps> = ({
  onClose,
  onApplyTemplate,
  currentTask
}) => {
  const { user } = useSupabaseAuth();
  const { settings, updateSettings, startTimer, linkTaskToTimer } = useEnhancedTimerStore();
  
  const [templates, setTemplates] = useState<RecurringTimerTemplate[]>([]);
  const [usageStats, setUsageStats] = useState<TemplateUsageStats[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<RecurringTimerTemplate | null>(null);
  const [newTemplate, setNewTemplate] = useState<Partial<RecurringTimerTemplate>>({
    name: '',
    description: '',
    taskPattern: '',
    timerSettings: {
      workDuration: 25,
      shortBreakDuration: 5,
      longBreakDuration: 15,
      sessionsUntilLongBreak: 4,
      autoStart: false,
      autoAdvanceBreaks: false
    },
    schedule: {
      frequency: 'daily'
    },
    focusSettings: {
      distractionBlocking: false,
      breakSuggestions: []
    },
    isActive: true
  });

  // Predefined template presets
  const templatePresets: Partial<RecurringTimerTemplate>[] = [
    {
      name: 'Morning Study Routine',
      description: 'Optimized for morning study sessions',
      taskPattern: 'study|reading|review',
      timerSettings: {
        workDuration: 30,
        shortBreakDuration: 5,
        longBreakDuration: 20,
        sessionsUntilLongBreak: 3,
        autoStart: true,
        autoAdvanceBreaks: false
      },
      schedule: {
        frequency: 'daily',
        timeOfDay: '09:00',
        duration: 120
      },
      focusSettings: {
        ambientSound: 'forest',
        distractionBlocking: true,
        breakSuggestions: ['eye-rest', 'desk-stretches']
      }
    },
    {
      name: 'Problem Solving Sessions',
      description: 'For math, coding, and analytical tasks',
      taskPattern: 'math|coding|problem|exercise',
      timerSettings: {
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        sessionsUntilLongBreak: 4,
        autoStart: false,
        autoAdvanceBreaks: true
      },
      schedule: {
        frequency: 'daily'
      },
      focusSettings: {
        ambientSound: 'white-noise',
        distractionBlocking: true,
        breakSuggestions: ['quick-walk', 'breathing-exercise']
      }
    },
    {
      name: 'Creative Work Flow',
      description: 'For writing, design, and creative tasks',
      taskPattern: 'write|creative|design|essay',
      timerSettings: {
        workDuration: 45,
        shortBreakDuration: 10,
        longBreakDuration: 25,
        sessionsUntilLongBreak: 3,
        autoStart: false,
        autoAdvanceBreaks: false
      },
      schedule: {
        frequency: 'custom'
      },
      focusSettings: {
        ambientSound: 'cafe',
        distractionBlocking: false,
        breakSuggestions: ['creative-doodling', 'quick-walk']
      }
    },
    {
      name: 'Exam Preparation',
      description: 'Intensive study sessions for exam prep',
      taskPattern: 'exam|test|preparation|review',
      timerSettings: {
        workDuration: 50,
        shortBreakDuration: 10,
        longBreakDuration: 30,
        sessionsUntilLongBreak: 2,
        autoStart: true,
        autoAdvanceBreaks: false
      },
      schedule: {
        frequency: 'daily',
        duration: 180
      },
      focusSettings: {
        ambientSound: 'rain',
        distractionBlocking: true,
        breakSuggestions: ['power-nap', 'hydration-snack']
      }
    }
  ];

  // Load data on component mount
  useEffect(() => {
    if (!user?.id) return;
    
    loadTemplates();
    loadUsageStats();
  }, [user?.id]);

  const loadTemplates = () => {
    if (!user?.id) return;
    
    const stored = localStorage.getItem(`recurringTimerTemplates_${user.id}`);
    if (stored) {
      setTemplates(JSON.parse(stored));
    } else {
      // Create default templates from presets
      const defaultTemplates: RecurringTimerTemplate[] = templatePresets.map((preset, index) => ({
        id: `template_${Date.now()}_${index}`,
        ...preset,
        createdAt: new Date(),
        usageCount: 0,
        isActive: true
      } as RecurringTimerTemplate));
      
      setTemplates(defaultTemplates);
      saveTemplates(defaultTemplates);
    }
  };

  const loadUsageStats = () => {
    if (!user?.id) return;
    
    const stored = localStorage.getItem(`templateUsageStats_${user.id}`);
    if (stored) {
      setUsageStats(JSON.parse(stored));
    }
  };

  const saveTemplates = (newTemplates: RecurringTimerTemplate[]) => {
    if (!user?.id) return;
    
    localStorage.setItem(`recurringTimerTemplates_${user.id}`, JSON.stringify(newTemplates));
    setTemplates(newTemplates);
  };

  const saveUsageStats = (newStats: TemplateUsageStats[]) => {
    if (!user?.id) return;
    
    localStorage.setItem(`templateUsageStats_${user.id}`, JSON.stringify(newStats));
    setUsageStats(newStats);
  };

  const createTemplate = () => {
    if (!newTemplate.name?.trim()) {
      toast({
        title: "Template name required",
        description: "Please provide a name for the template",
        variant: "destructive"
      });
      return;
    }

    const template: RecurringTimerTemplate = {
      id: `template_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      name: newTemplate.name,
      description: newTemplate.description || '',
      taskPattern: newTemplate.taskPattern || '',
      timerSettings: newTemplate.timerSettings!,
      schedule: newTemplate.schedule!,
      focusSettings: newTemplate.focusSettings!,
      isActive: true,
      createdAt: new Date(),
      usageCount: 0
    };

    const updatedTemplates = [...templates, template];
    saveTemplates(updatedTemplates);
    setIsCreating(false);
    resetNewTemplate();
    
    toast({
      title: "Template created",
      description: `Created "${template.name}" template`
    });
  };

  const updateTemplate = (templateId: string, updates: Partial<RecurringTimerTemplate>) => {
    const updatedTemplates = templates.map(template =>
      template.id === templateId ? { ...template, ...updates } : template
    );
    saveTemplates(updatedTemplates);
    setEditingTemplate(null);
    
    toast({
      title: "Template updated",
      description: "Template settings have been saved"
    });
  };

  const deleteTemplate = (templateId: string) => {
    const updatedTemplates = templates.filter(template => template.id !== templateId);
    saveTemplates(updatedTemplates);
    
    toast({
      title: "Template deleted",
      description: "The template has been removed"
    });
  };

  const duplicateTemplate = (template: RecurringTimerTemplate) => {
    const duplicate: RecurringTimerTemplate = {
      ...template,
      id: `template_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      name: `${template.name} (Copy)`,
      createdAt: new Date(),
      usageCount: 0,
      lastUsed: undefined
    };

    const updatedTemplates = [...templates, duplicate];
    saveTemplates(updatedTemplates);
    
    toast({
      title: "Template duplicated",
      description: `Created copy of "${template.name}"`
    });
  };

  const applyTemplate = async (template: RecurringTimerTemplate) => {
    try {
      // Update timer settings
      updateSettings({
        workDuration: template.timerSettings.workDuration * 60,
        shortBreakDuration: template.timerSettings.shortBreakDuration * 60,
        longBreakDuration: template.timerSettings.longBreakDuration * 60,
        sessionsUntilLongBreak: template.timerSettings.sessionsUntilLongBreak
      });

      // Update usage statistics
      const updatedTemplates = templates.map(t =>
        t.id === template.id 
          ? { ...t, usageCount: t.usageCount + 1, lastUsed: new Date() }
          : t
      );
      saveTemplates(updatedTemplates);

      // Update usage stats
      const existingStats = usageStats.find(s => s.templateId === template.id);
      if (existingStats) {
        const updatedStats = usageStats.map(s =>
          s.templateId === template.id
            ? { ...s, totalSessions: s.totalSessions + 1, lastWeekUsage: s.lastWeekUsage + 1 }
            : s
        );
        saveUsageStats(updatedStats);
      } else {
        const newStats: TemplateUsageStats = {
          templateId: template.id,
          totalSessions: 1,
          totalTime: 0,
          averageProductivity: 0,
          completionRate: 0,
          lastWeekUsage: 1,
          trend: 'stable'
        };
        saveUsageStats([...usageStats, newStats]);
      }

      // Auto-start timer if enabled and task is available
      if (template.timerSettings.autoStart && currentTask && user?.id) {
        await linkTaskToTimer(currentTask.id, user.id);
        await startTimer(currentTask.id, user.id);
      }

      onApplyTemplate(template);
      
      toast({
        title: "Template applied",
        description: `Applied "${template.name}" settings`
      });
    } catch (error) {
      console.error('Error applying template:', error);
      toast({
        title: "Error",
        description: "Failed to apply template settings",
        variant: "destructive"
      });
    }
  };

  const resetNewTemplate = () => {
    setNewTemplate({
      name: '',
      description: '',
      taskPattern: '',
      timerSettings: {
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        sessionsUntilLongBreak: 4,
        autoStart: false,
        autoAdvanceBreaks: false
      },
      schedule: {
        frequency: 'daily'
      },
      focusSettings: {
        distractionBlocking: false,
        breakSuggestions: []
      },
      isActive: true
    });
  };

  const getMatchingTemplates = (): RecurringTimerTemplate[] => {
    if (!currentTask) return templates.filter(t => t.isActive);
    
    return templates.filter(template => {
      if (!template.isActive) return false;
      if (!template.taskPattern) return true;
      
      const patterns = template.taskPattern.toLowerCase().split('|');
      const taskText = `${currentTask.title} ${currentTask.description || ''}`.toLowerCase();
      
      return patterns.some(pattern => taskText.includes(pattern.trim()));
    });
  };

  const getTemplateStats = (templateId: string): TemplateUsageStats | null => {
    return usageStats.find(s => s.templateId === templateId) || null;
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getDaysOfWeekText = (days?: number[]): string => {
    if (!days || days.length === 0) return 'Daily';
    if (days.length === 7) return 'Daily';
    
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days.map(day => dayNames[day]).join(', ');
  };

  return (
    <Card className="w-full max-w-4xl mx-auto h-[80vh]">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Repeat className="h-5 w-5" />
            <span>Recurring Timer Templates</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Template
            </Button>
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
        
        {currentTask && (
          <div className="text-sm text-muted-foreground">
            Current task: <span className="font-medium">{currentTask.title}</span>
            {currentTask.subjectName && ` • ${currentTask.subjectName}`}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Templates List */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">
                {currentTask ? 'Matching Templates' : 'All Templates'} ({getMatchingTemplates().length})
              </h3>
            </div>
            
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {getMatchingTemplates().length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Timer className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No templates found</p>
                    <p className="text-sm">Create a template to get started</p>
                  </div>
                ) : (
                  getMatchingTemplates().map((template) => {
                    const stats = getTemplateStats(template.id);
                    return (
                      <Card key={template.id} className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="font-medium">{template.name}</h4>
                              {!template.isActive && (
                                <Badge variant="secondary">Inactive</Badge>
                              )}
                            </div>
                            {template.description && (
                              <p className="text-sm text-muted-foreground mb-2">
                                {template.description}
                              </p>
                            )}
                            <div className="flex flex-wrap gap-2 text-xs">
                              <Badge variant="outline">
                                <Clock className="h-3 w-3 mr-1" />
                                {template.timerSettings.workDuration}m work
                              </Badge>
                              <Badge variant="outline">
                                <Calendar className="h-3 w-3 mr-1" />
                                {getDaysOfWeekText(template.schedule.daysOfWeek)}
                              </Badge>
                              {stats && (
                                <Badge variant="outline">
                                  <Target className="h-3 w-3 mr-1" />
                                  {stats.totalSessions} sessions
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Button 
                              size="sm"
                              onClick={() => applyTemplate(template)}
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Apply
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => setEditingTemplate(template)}
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => duplicateTemplate(template)}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => deleteTemplate(template.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        {/* Template Details */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Timer:</span>
                            <div className="font-mono">
                              {template.timerSettings.workDuration}m / {template.timerSettings.shortBreakDuration}m
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Schedule:</span>
                            <div>
                              {template.schedule.timeOfDay || 'Flexible'}
                            </div>
                          </div>
                        </div>
                        
                        {stats && (
                          <div className="mt-3 pt-3 border-t">
                            <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                              <div>
                                <div className="font-medium">{formatDuration(stats.totalTime)}</div>
                                <div>Total time</div>
                              </div>
                              <div>
                                <div className="font-medium">{stats.completionRate}%</div>
                                <div>Completion</div>
                              </div>
                              <div>
                                <div className="font-medium">{stats.averageProductivity.toFixed(1)}/5</div>
                                <div>Productivity</div>
                              </div>
                            </div>
                          </div>
                        )}
                      </Card>
                    );
                  })
                )}
              </div>
            </ScrollArea>
          </div>

          {/* Create/Edit Template Panel */}
          <div className="space-y-4">
            {(isCreating || editingTemplate) && (
              <Card className="p-4">
                <h3 className="font-semibold mb-4">
                  {editingTemplate ? 'Edit Template' : 'Create Template'}
                </h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="templateName">Template Name</Label>
                    <Input
                      id="templateName"
                      value={editingTemplate ? editingTemplate.name : newTemplate.name}
                      onChange={(e) => {
                        if (editingTemplate) {
                          setEditingTemplate({ ...editingTemplate, name: e.target.value });
                        } else {
                          setNewTemplate({ ...newTemplate, name: e.target.value });
                        }
                      }}
                      placeholder="e.g., Morning Study Routine"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="templateDescription">Description</Label>
                    <Input
                      id="templateDescription"
                      value={editingTemplate ? editingTemplate.description : newTemplate.description}
                      onChange={(e) => {
                        if (editingTemplate) {
                          setEditingTemplate({ ...editingTemplate, description: e.target.value });
                        } else {
                          setNewTemplate({ ...newTemplate, description: e.target.value });
                        }
                      }}
                      placeholder="Brief description..."
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="taskPattern">Task Pattern</Label>
                    <Input
                      id="taskPattern"
                      value={editingTemplate ? editingTemplate.taskPattern : newTemplate.taskPattern}
                      onChange={(e) => {
                        if (editingTemplate) {
                          setEditingTemplate({ ...editingTemplate, taskPattern: e.target.value });
                        } else {
                          setNewTemplate({ ...newTemplate, taskPattern: e.target.value });
                        }
                      }}
                      placeholder="study|reading|math (use | to separate)"
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label>Work Duration</Label>
                      <Input
                        type="number"
                        min="15"
                        max="90"
                        value={editingTemplate ? editingTemplate.timerSettings.workDuration : newTemplate.timerSettings?.workDuration}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (editingTemplate) {
                            setEditingTemplate({
                              ...editingTemplate,
                              timerSettings: { ...editingTemplate.timerSettings, workDuration: value }
                            });
                          } else {
                            setNewTemplate({
                              ...newTemplate,
                              timerSettings: { ...newTemplate.timerSettings!, workDuration: value }
                            });
                          }
                        }}
                      />
                    </div>
                    <div>
                      <Label>Short Break</Label>
                      <Input
                        type="number"
                        min="3"
                        max="15"
                        value={editingTemplate ? editingTemplate.timerSettings.shortBreakDuration : newTemplate.timerSettings?.shortBreakDuration}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (editingTemplate) {
                            setEditingTemplate({
                              ...editingTemplate,
                              timerSettings: { ...editingTemplate.timerSettings, shortBreakDuration: value }
                            });
                          } else {
                            setNewTemplate({
                              ...newTemplate,
                              timerSettings: { ...newTemplate.timerSettings!, shortBreakDuration: value }
                            });
                          }
                        }}
                      />
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button 
                      onClick={editingTemplate ? 
                        () => updateTemplate(editingTemplate.id, editingTemplate) : 
                        createTemplate
                      }
                      className="flex-1"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {editingTemplate ? 'Update' : 'Create'}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setIsCreating(false);
                        setEditingTemplate(null);
                        resetNewTemplate();
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </Card>
            )}
            
            {/* Template Presets */}
            {!isCreating && !editingTemplate && (
              <Card className="p-4">
                <h3 className="font-semibold mb-4">Quick Start Presets</h3>
                <div className="space-y-2">
                  {templatePresets.map((preset, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      className="w-full justify-start h-auto p-3"
                      onClick={() => {
                        setNewTemplate({ ...newTemplate, ...preset });
                        setIsCreating(true);
                      }}
                    >
                      <div className="text-left">
                        <div className="font-medium">{preset.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {preset.description}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </Card>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};