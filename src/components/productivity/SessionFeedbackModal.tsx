import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Star, Clock, Brain, Target } from 'lucide-react';
import { SessionFeedback } from '@/utils/supabase';

interface SessionFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (feedback: SessionFeedback) => void;
  taskName: string;
  sessionDuration: number; // in seconds
}

const SessionFeedbackModal: React.FC<SessionFeedbackModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  taskName,
  sessionDuration
}) => {
  const [productivityRating, setProductivityRating] = useState<number>(3);
  const [difficultyRating, setDifficultyRating] = useState<number>(3);
  const [focusRating, setFocusRating] = useState<number>(3);
  const [notes, setNotes] = useState<string>('');
  const [completedSubtasks, setCompletedSubtasks] = useState<string>('');
  const [nextSteps, setNextSteps] = useState<string>('');

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const handleSubmit = () => {
    const feedback: SessionFeedback = {
      productivityRating,
      difficultyRating,
      focusRating,
      notes: notes.trim() || undefined,
      completedSubtasks: completedSubtasks.trim() ? completedSubtasks.split('\n').filter(s => s.trim()) : undefined,
      nextSteps: nextSteps.trim() || undefined
    };

    onSubmit(feedback);
    onClose();
  };

  const handleSkip = () => {
    const basicFeedback: SessionFeedback = {
      productivityRating: 3, // Default neutral rating
    };
    onSubmit(basicFeedback);
    onClose();
  };

  const StarRating: React.FC<{
    value: number;
    onChange: (value: number) => void;
    label: string;
    icon: React.ReactNode;
    color: string;
  }> = ({ value, onChange, label, icon, color }) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <div className={`p-1 rounded ${color}`}>
          {icon}
        </div>
        <Label className="text-sm font-medium">{label}</Label>
      </div>
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onChange(star)}
            className="transition-colors"
          >
            <Star
              className={`w-6 h-6 ${
                star <= value
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300 hover:text-yellow-300'
              }`}
            />
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-blue-500" />
            Session Complete
          </DialogTitle>
          <DialogDescription>
            You worked on <span className="font-medium">{taskName}</span> for{' '}
            <span className="font-medium">{formatDuration(sessionDuration)}</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Productivity Rating */}
          <StarRating
            value={productivityRating}
            onChange={setProductivityRating}
            label="How productive was this session?"
            icon={<Target className="w-4 h-4 text-white" />}
            color="bg-green-500"
          />

          {/* Difficulty Rating */}
          <StarRating
            value={difficultyRating}
            onChange={setDifficultyRating}
            label="How difficult was the task?"
            icon={<Brain className="w-4 h-4 text-white" />}
            color="bg-orange-500"
          />

          {/* Focus Rating */}
          <StarRating
            value={focusRating}
            onChange={setFocusRating}
            label="How well did you focus?"
            icon={<Star className="w-4 h-4 text-white" />}
            color="bg-purple-500"
          />

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-medium">
              Session Notes (Optional)
            </Label>
            <Textarea
              id="notes"
              placeholder="How did the session go? Any insights or challenges?"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[80px]"
            />
          </div>

          {/* Completed Subtasks */}
          <div className="space-y-2">
            <Label htmlFor="completed" className="text-sm font-medium">
              What did you complete? (Optional)
            </Label>
            <Textarea
              id="completed"
              placeholder="List completed subtasks or milestones (one per line)"
              value={completedSubtasks}
              onChange={(e) => setCompletedSubtasks(e.target.value)}
              className="min-h-[60px]"
            />
          </div>

          {/* Next Steps */}
          <div className="space-y-2">
            <Label htmlFor="nextSteps" className="text-sm font-medium">
              Next Steps (Optional)
            </Label>
            <Textarea
              id="nextSteps"
              placeholder="What should you work on next time?"
              value={nextSteps}
              onChange={(e) => setNextSteps(e.target.value)}
              className="min-h-[60px]"
            />
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleSkip}>
            Skip Feedback
          </Button>
          <Button onClick={handleSubmit} className="bg-blue-600 hover:bg-blue-700">
            Save Feedback
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SessionFeedbackModal;