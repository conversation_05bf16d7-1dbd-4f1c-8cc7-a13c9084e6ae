import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  Target, 
  Settings,
  CheckCircle2,
  AlertCircle,
  Edit3,
  Coffee,
  BookOpen,
  Calculator,
  FileText,
  Zap,
  RotateCcw,
  StickyNote,
  ArrowRight
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { useEnhancedTimerStore, useTimerActions, useTimerLinkedTask } from '@/stores/enhancedTimerStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import SessionFeedbackModal from './SessionFeedbackModal';
import { SessionFeedback } from '@/utils/supabase';
import { toast } from '@/components/ui/use-toast';
import { taskStorage } from '@/utils/taskLocalStorage';
import { taskTimerIntegration } from '@/services/TaskTimerIntegrationService';


interface EnhancedTimerWithTaskIntegrationProps {
  className?: string;
}

const EnhancedTimerWithTaskIntegration: React.FC<EnhancedTimerWithTaskIntegrationProps> = ({
  className = ''
}) => {
  const { user } = useSupabaseAuth();
  const { startTimer, pauseTimer, stopTimer, resetTimer, linkTaskToTimer } = useTimerActions();
  const linkedTask = useTimerLinkedTask();
  const { status, displayTime, sessionStartTime, currentPhase } = useEnhancedTimerStore();
  
  const [currentTime, setCurrentTime] = useState(0);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [sessionDuration, setSessionDuration] = useState(0);
  const [showTaskActions, setShowTaskActions] = useState(false);
  const [showTaskSwitcher, setShowTaskSwitcher] = useState(false);
  const [taskNotes, setTaskNotes] = useState('');
  const [showNotesDialog, setShowNotesDialog] = useState(false);
  const [taskProgress, setTaskProgress] = useState(0);

  // Update current time display and task progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (status === 'running') {
      interval = setInterval(() => {
        const now = Date.now();
        const elapsed = Math.floor((now - sessionStartTime.getTime()) / 1000);
        setCurrentTime(elapsed);

        // Update task progress if we have an estimated time
        if (linkedTask?.estimatedTime) {
          const totalTimeMinutes = (linkedTask.actualTimeSpent || 0) + (elapsed / 60);
          const progress = Math.min((totalTimeMinutes / linkedTask.estimatedTime) * 100, 100);
          setTaskProgress(progress);
        }
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [status, sessionStartTime, linkedTask]);

  // Initialize task progress when task changes
  useEffect(() => {
    if (linkedTask?.estimatedTime) {
      const progress = Math.min(((linkedTask.actualTimeSpent || 0) / linkedTask.estimatedTime) * 100, 100);
      setTaskProgress(progress);
    } else {
      setTaskProgress(0);
    }
  }, [linkedTask]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStart = async () => {
    try {
      if (!linkedTask) {
        toast({
          title: "No Task Selected",
          description: "Please select a task before starting the timer.",
          variant: "destructive"
        });
        return;
      }

      if (!user) {
        toast({
          title: "Authentication Required",
          description: "Please sign in to start tracking time.",
          variant: "destructive"
        });
        return;
      }

      await startTimer(linkedTask.taskId, user.id);
      
      toast({
        title: "Timer Started",
        description: `Started tracking time for "${linkedTask.taskTitle}"`,
      });
    } catch (error) {
      console.error('Error starting timer:', error);
      toast({
        title: "Error",
        description: "Failed to start timer. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handlePause = async () => {
    try {
      await pauseTimer();
      toast({
        title: "Timer Paused",
        description: "Your session has been paused.",
      });
    } catch (error) {
      console.error('Error pausing timer:', error);
      toast({
        title: "Error",
        description: "Failed to pause timer.",
        variant: "destructive"
      });
    }
  };

  const handleStop = () => {
    if (status === 'running' || status === 'paused') {
      setSessionDuration(currentTime);
      setShowFeedbackModal(true);
    }
  };

  const handleFeedbackSubmit = async (feedback: SessionFeedback) => {
    try {
      if (!user) return;

      // Add userId to feedback for the store
      const feedbackWithUserId = { ...feedback, userId: user.id };
      
      await stopTimer(feedbackWithUserId);
      
      toast({
        title: "Session Completed",
        description: "Your study session has been saved with feedback.",
      });
    } catch (error) {
      console.error('Error stopping timer with feedback:', error);
      toast({
        title: "Error",
        description: "Failed to save session. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleReset = () => {
    resetTimer();
    setCurrentTime(0);
    toast({
      title: "Timer Reset",
      description: "Timer has been reset to initial state.",
    });
  };

  const handleTaskComplete = async () => {
    if (!linkedTask || !user) return;

    try {
      // Update task status to completed
      const task = taskStorage.getTask(user.id, linkedTask.taskId);
      if (task) {
        const updatedTask = {
          ...task,
          columnId: 'done',
          completionPercentage: 100,
          updatedAt: Date.now()
        };
        taskStorage.saveTask(user.id, updatedTask);

        // Stop timer if running
        if (status === 'running' || status === 'paused') {
          setSessionDuration(currentTime);
          setShowFeedbackModal(true);
        }

        toast({
          title: "Task Completed!",
          description: `"${linkedTask.taskTitle}" has been marked as complete.`,
        });
      }
    } catch (error) {
      console.error('Error completing task:', error);
      toast({
        title: "Error",
        description: "Failed to complete task. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleAddNotes = () => {
    setShowNotesDialog(true);
  };

  const handleSaveNotes = async () => {
    if (!linkedTask || !user || !taskNotes.trim()) return;

    try {
      const task = taskStorage.getTask(user.id, linkedTask.taskId);
      if (task) {
        const updatedTask = {
          ...task,
          description: task.description ? `${task.description}\n\nNotes: ${taskNotes}` : `Notes: ${taskNotes}`,
          updatedAt: Date.now()
        };
        taskStorage.saveTask(user.id, updatedTask);

        setTaskNotes('');
        setShowNotesDialog(false);
        
        toast({
          title: "Notes Added",
          description: "Your notes have been saved to the task.",
        });
      }
    } catch (error) {
      console.error('Error saving notes:', error);
      toast({
        title: "Error",
        description: "Failed to save notes. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleTaskSwitch = async (task: any) => {
    if (!user) return;

    try {
      // Pause current timer if running
      if (status === 'running') {
        await pauseTimer();
      }

      // Link new task to timer
      await linkTaskToTimer(task.id, user.id);
      
      setShowTaskSwitcher(false);
      
      toast({
        title: "Task Switched",
        description: "Timer has been linked to the new task.",
      });
    } catch (error) {
      console.error('Error switching task:', error);
      toast({
        title: "Error",
        description: "Failed to switch task. Please try again.",
        variant: "destructive"
      });
    }
  };

  const getBreakSuggestion = () => {
    if (!linkedTask) return "Take a short break!";

    const taskType = linkedTask.taskTitle.toLowerCase();
    
    if (taskType.includes('math') || taskType.includes('calculation') || taskType.includes('problem')) {
      return "🧮 Math break: Try some light stretching or a quick walk to refresh your analytical mind!";
    } else if (taskType.includes('read') || taskType.includes('study') || taskType.includes('research')) {
      return "📚 Reading break: Rest your eyes! Look at something distant or do some eye exercises.";
    } else if (taskType.includes('write') || taskType.includes('essay') || taskType.includes('report')) {
      return "✍️ Writing break: Stretch your hands and wrists, maybe do some finger exercises!";
    } else if (taskType.includes('code') || taskType.includes('program')) {
      return "💻 Coding break: Step away from the screen! Try some neck rolls and shoulder shrugs.";
    } else {
      return "☕ Study break: Hydrate, stretch, and give your mind a moment to process what you've learned!";
    }
  };

  const getTaskTypeIcon = () => {
    if (!linkedTask) return <BookOpen className="w-4 h-4" />;

    const taskType = linkedTask.taskTitle.toLowerCase();
    
    if (taskType.includes('math') || taskType.includes('calculation')) {
      return <Calculator className="w-4 h-4" />;
    } else if (taskType.includes('read') || taskType.includes('study')) {
      return <BookOpen className="w-4 h-4" />;
    } else if (taskType.includes('write') || taskType.includes('essay')) {
      return <FileText className="w-4 h-4" />;
    } else {
      return <Zap className="w-4 h-4" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'running': return 'Running';
      case 'paused': return 'Paused';
      default: return 'Idle';
    }
  };

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-500" />
              Study Timer
            </CardTitle>
            <Badge className={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Timer Display */}
          <div className="text-center">
            <div className="text-4xl font-mono font-bold text-gray-900 mb-2">
              {formatTime(currentTime)}
            </div>
            {status === 'running' && (
              <div className="text-sm text-gray-500">
                Session started at {sessionStartTime.toLocaleTimeString()}
              </div>
            )}
          </div>

          {/* Task Information */}
          {linkedTask && (
            <div className="bg-blue-50 rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    {getTaskTypeIcon()}
                    <h3 className="font-medium text-blue-900">
                      {linkedTask.taskTitle}
                    </h3>
                  </div>
                  {linkedTask.taskDescription && (
                    <p className="text-sm text-blue-700 mb-2 line-clamp-2">
                      {linkedTask.taskDescription}
                    </p>
                  )}
                  <div className="flex items-center gap-3 text-xs">
                    {linkedTask.subjectName && (
                      <Badge 
                        variant="secondary" 
                        className="text-xs"
                        style={{ 
                          backgroundColor: linkedTask.subjectColor + '20',
                          color: linkedTask.subjectColor 
                        }}
                      >
                        {linkedTask.subjectName}
                      </Badge>
                    )}
                    <span className="flex items-center gap-1 text-gray-600">
                      <Target className="w-3 h-3" />
                      {linkedTask.priority} priority
                    </span>
                    {linkedTask.estimatedTime && (
                      <span className="flex items-center gap-1 text-gray-600">
                        <Clock className="w-3 h-3" />
                        {linkedTask.estimatedTime}m estimated
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-blue-900">
                    {Math.round((linkedTask.actualTimeSpent || 0) + (currentTime / 60))}m tracked
                  </div>
                  {linkedTask.estimatedTime && (
                    <div className="text-xs text-blue-700">
                      {Math.round(taskProgress)}% complete
                    </div>
                  )}
                </div>
              </div>

              {/* Task Progress Bar */}
              {linkedTask.estimatedTime && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-blue-700">
                    <span>Progress</span>
                    <span>{Math.round(taskProgress)}%</span>
                  </div>
                  <Progress 
                    value={taskProgress} 
                    className="h-2 bg-blue-100"
                  />
                </div>
              )}

              {/* Quick Task Actions */}
              <div className="flex items-center gap-2 pt-2 border-t border-blue-200">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleTaskComplete}
                  className="text-xs border-green-300 text-green-700 hover:bg-green-50"
                >
                  <CheckCircle2 className="w-3 h-3 mr-1" />
                  Complete
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAddNotes}
                  className="text-xs border-blue-300 text-blue-700 hover:bg-blue-50"
                >
                  <StickyNote className="w-3 h-3 mr-1" />
                  Notes
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowTaskSwitcher(true)}
                  className="text-xs border-purple-300 text-purple-700 hover:bg-purple-50"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Switch
                </Button>
              </div>
            </div>
          )}

          {/* No Task Selected */}
          {!linkedTask && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
              <AlertCircle className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-sm text-yellow-800 mb-2">
                No task selected for time tracking
              </p>
              <p className="text-xs text-yellow-700">
                Select a task from the task selector to start tracking time
              </p>
            </div>
          )}

          {/* Timer Controls */}
          <div className="flex items-center justify-center gap-3">
            {status === 'idle' && (
              <Button 
                onClick={handleStart}
                disabled={!linkedTask || !user}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="w-4 h-4 mr-2" />
                Start
              </Button>
            )}

            {status === 'running' && (
              <>
                <Button 
                  onClick={handlePause}
                  variant="outline"
                  className="border-yellow-300 text-yellow-700 hover:bg-yellow-50"
                >
                  <Pause className="w-4 h-4 mr-2" />
                  Pause
                </Button>
                <Button 
                  onClick={handleStop}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              </>
            )}

            {status === 'paused' && (
              <>
                <Button 
                  onClick={handleStart}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Resume
                </Button>
                <Button 
                  onClick={handleStop}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              </>
            )}

            {status !== 'idle' && (
              <Button 
                onClick={handleReset}
                variant="outline"
                size="sm"
              >
                Reset
              </Button>
            )}
          </div>

          {/* Break Suggestions */}
          {status === 'running' && currentTime > 1500 && currentTime % 900 < 60 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
              <Coffee className="w-5 h-5 text-yellow-600 mx-auto mb-1" />
              <p className="text-sm text-yellow-800 font-medium mb-1">
                Break Suggestion
              </p>
              <p className="text-xs text-yellow-700">
                {getBreakSuggestion()}
              </p>
            </div>
          )}

          {/* Phase Information (for Pomodoro mode) */}
          {currentPhase !== 'work' && (
            <div className="text-center">
              <Badge variant="secondary" className="text-xs">
                {currentPhase === 'shortBreak' ? 'Short Break' : 'Long Break'}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Session Feedback Modal */}
      <SessionFeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        onSubmit={handleFeedbackSubmit}
        taskName={linkedTask?.taskTitle || 'Unknown Task'}
        sessionDuration={sessionDuration}
      />

      {/* Notes Dialog */}
      <Dialog open={showNotesDialog} onOpenChange={setShowNotesDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <StickyNote className="w-4 h-4" />
              Add Notes to Task
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 mb-2">
                Task: {linkedTask?.taskTitle}
              </p>
              <Textarea
                placeholder="Add your notes here..."
                value={taskNotes}
                onChange={(e) => setTaskNotes(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNotesDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveNotes} disabled={!taskNotes.trim()}>
              Save Notes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Task Switcher Dialog */}
      <Dialog open={showTaskSwitcher} onOpenChange={setShowTaskSwitcher}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <RotateCcw className="w-4 h-4" />
              Switch Task
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Select a new task to continue timing. Your current session will be paused.
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Task switching functionality will be available once the task selector is updated to support the enhanced task types.
              </p>
              <Button 
                variant="outline" 
                onClick={() => setShowTaskSwitcher(false)}
                className="w-full"
              >
                Close for now
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTaskSwitcher(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EnhancedTimerWithTaskIntegration;